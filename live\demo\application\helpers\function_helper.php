<?php

use <PERSON>reait\Firebase\Exception\Messaging\InvalidMessage;
use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification;

defined('BASEPATH') or die('No direct script access allowed!');

function JSONResponse($data = array(), $flags = 0)
{
    echo json_encode($data, $flags);
}

function JSONResponseDefault($result, $message)
{
    return JSONResponse(array(
        'RESULT' => $result,
        'MESSAGE' => $message
    ));
}

function getPost($index, $default = null)
{
    $CI = &get_instance();

    if ($CI->input->post($index)) {
        if (is_string($CI->input->post($index))) {
            return trim($CI->input->post($index));
        } else {
            return $CI->input->post($index);
        }
    }

    return $default;
}

function getGet($index, $default = null)
{
    $CI = &get_instance();

    if ($CI->input->get($index)) {
        return $CI->input->get($index);
    }

    return $default;
}

function getSessionValue($index)
{
    $CI = &get_instance();

    return $CI->session->userdata($index);
}

function setSessionValue($index)
{
    $CI = &get_instance();

    return $CI->session->set_userdata($index);
}

function hasSessionValue($index)
{
    $CI = &get_instance();

    return $CI->session->has_userdata($index);
}

function unsetSessionValue($index)
{
    $CI = &get_instance();

    return $CI->session->unset_userdata($index);
}

function destroySession()
{
    $CI = &get_instance();

    return $CI->session->sess_destroy();
}

function isLogin()
{
    return getSessionValue('ISLOGIN');
}

function getCurrentDate($format = 'Y-m-d H:i:s')
{
    return date($format);
}

function getCurrentIdUser()
{
    return getSessionValue('USERID');
}

function stringEncryption($action, $string)
{
    $output = false;

    $encrypt_method = 'AES-256-CBC'; // Default
    $secret_key = 'karpeldedvtech'; // Change the key!
    $secret_iv = 'owr216he890';  // Change the init vector!

    // hash
    $key = hash('sha256', $secret_key);

    // iv - encrypt method AES-256-CBC expects 16 bytes - else you will get a warning
    $iv = substr(hash('sha256', $secret_iv), 0, 16);

    if ($action == 'encrypt') {
        $output = openssl_encrypt($string, $encrypt_method, $key, 0, $iv);
        $output = base64_encode($output);
    } else if ($action == 'decrypt') {
        $output = openssl_decrypt(base64_decode($string ?? ''), $encrypt_method, $key, 0, $iv);
    }

    return $output;
}

/**
 * Encrypt file content
 *
 * @param string $filePath Path to the file to encrypt
 * @return bool Success status
 */
function encryptFile($filePath)
{
    if (!file_exists($filePath)) {
        return false;
    }

    try {
        // Read file content
        $fileContent = file_get_contents($filePath);
        if ($fileContent === false) {
            return false;
        }

        // Encrypt the content
        $encryptedContent = stringEncryption('encrypt', $fileContent);
        if ($encryptedContent === false) {
            return false;
        }

        // Write encrypted content back to file
        $result = file_put_contents($filePath, $encryptedContent);
        return $result !== false;
    } catch (Exception $e) {
        log_message('error', 'File encryption failed: ' . $e->getMessage());
        return false;
    }
}

/**
 * Decrypt file content
 *
 * @param string $filePath Path to the encrypted file
 * @return string|false Decrypted content or false on failure
 */
function decryptFile($filePath)
{
    if (!file_exists($filePath)) {
        return false;
    }

    try {
        // Read encrypted content
        $encryptedContent = file_get_contents($filePath);
        if ($encryptedContent === false) {
            return false;
        }

        // Decrypt the content
        $decryptedContent = stringEncryption('decrypt', $encryptedContent);
        return $decryptedContent;
    } catch (Exception $e) {
        log_message('error', 'File decryption failed: ' . $e->getMessage());
        return false;
    }
}

function getMerchant($userid, $by = 'a.id')
{
    $CI = &get_instance();

    return $CI->db->select('a.*, b.domain AS domain_owner')
        ->join('msusers b', 'b.id = a.ownerid', 'LEFT')
        ->get_where('msusers a', array(
            $by => $by == 'a.id' ? stringEncryption('decrypt', $userid) : $userid
        ))->row();
}

function getMerchantDomain($userid)
{
    $CI = &get_instance();

    $merchant = $CI->db->select('domain')
        ->get_where('msusers', array('id' => $userid))
        ->row();

    if ($merchant && $merchant->domain) {
        // Check if domain has protocol
        if (strpos($merchant->domain, 'http') === 0) {
            return rtrim($merchant->domain, '/') . '/';
        } else {
            // Add https protocol by default
            return 'https://' . rtrim($merchant->domain, '/') . '/';
        }
    }

    // Fallback to current base URL if no domain set
    return base_url();
}

function DateFormat($date, $format = null)
{
    if ($format == null) {
        $format = 'd/m/Y';
    }

    $date = str_replace('/', '-', $date);

    return date($format, strtotime($date));
}

function getCurrentUser($userid = null, $lock = false)
{
    $CI = &get_instance();

    // Tentukan ID user yang akan digunakan
    $id = $userid === null ? getCurrentIdUser() : $userid;

    if ($lock) {
        // Gunakan pesimis locking dengan FOR UPDATE
        $CI->db->select('*', false); // Menghindari kesalahan pada MySQL
        $CI->db->from('msusers');
        $CI->db->where('id', $id);

        $query = $CI->db->get_compiled_select();
        $query .= " FOR UPDATE";

        return $CI->db->query($query)->row();
    } else {
        // Query biasa tanpa locking
        return $CI->db->get_where('msusers', array('id' => $id))->row();
    }
}


function getCurrentBalance($userid = null, $lock = false)
{
    return getCurrentUser($userid, $lock)->balance ?? 0;
}

function IDR($nominal, $digit = 0, $pemisah = '.', $rupiah = ',')
{
    return number_format($nominal ?? 0, $digit, $pemisah, $rupiah);
}

function ppobFeature()
{
    return array(
        'ppob/prabayar',
        'ppob/prabayarmassal',
        'ppob/history',
        'ppob/services',
        'ppob/pascabayar',
        'ppob/report',
        'ppob/favourite',
        'ppob/information',
    );
}

function smmFeature()
{
    return array(
        'smm/order',
        'smm/ordermassal',
        'smm/history',
        'smm/services',
        'smm/report',
        'smm/favourite',
        'smm/information'
    );
}

function depositFeature()
{
    return array(
        'deposit/topup',
        'deposit/history',
        'deposit/report'
    );
}

function ticketFeature()
{
    return array(
        'ticket/new',
        'ticket/history',
    );
}

function accountFeature()
{
    return array(
        'account/change',
        'account/password',
        'account/pin',
        'account/history/login',
        'account/history/balance',
        'account/contact'
    );
}

function getCurrentAPIKeys($category, $userid = null)
{
    $CI = &get_instance();

    $get = $CI->db->get_where('apikeys', array(
        'userid' => $userid == null ? getCurrentIdUser() : $userid,
        'category' => $category
    ))->row();

    return $get;
}

function getCurrentVendor($category, $userid = null)
{
    $get = getCurrentAPIKeys($category, $userid);

    return $get != null ? $get->vendor : null;
}

function generateTransactionNumber($prefix)
{
    $user_id = null;

    $random_suffix = rand(1000, 9999);
    $seven_digits = str_pad($random_suffix, 5, "0", STR_PAD_LEFT);

    if (isLogin()) {
        $user_id = getCurrentIdUser();

        return $prefix . '-' . getCurrentDate('YmdHis') . '-' . $seven_digits . '-' . $user_id;
    } else {
        return $prefix . '-' . getCurrentDate('YmdHis') . '-' . $seven_digits;
    }
}

function generateUniqueCodePayDisini($prefix = 'DEPOSIT-')
{
    $datePart = date('Ymd');
    $randomPart = rand(1000, 9999);

    // Batasi panjang string total menjadi 32 karakter
    $maxRandomLength = 32 - strlen($prefix) - strlen($datePart);
    $randomPart = substr($randomPart, 0, $maxRandomLength);

    $depositCode = $prefix . $datePart . '-' . $randomPart;

    return $depositCode;
}

function getResponseCodeDigiflazz($rc)
{
    $result = '[{"rc":"00","message":"Transaksi Sukses"},{"rc":"01","message":"Timeout"},{"rc":"02","message":"Transaksi Gagal"},{"rc":"03","message":"Transaksi Pending"},{"rc":"40","message":"Payload Error"},{"rc":"41","message":"Signature tidak valid"},{"rc":"42","message":"Gagal memproses API Buyer"},{"rc":"43","message":"SKU tidak di temukan atau Non-Aktif"},{"rc":"44","message":"Saldo tidak cukup"},{"rc":"45","message":"IP Anda tidak kami kenali"},{"rc":"47","message":"Transaksi sudah terjadi di buyer lain"},{"rc":"49","message":"Ref ID tidak unik"},{"rc":"50","message":"Transaksi Tidak Ditemukan"},{"rc":"51","message":"Nomor Tujuan Diblokir"},{"rc":"52","message":"Prefix Tidak Sesuai Dengan Operator"},{"rc":"53","message":"Produk Seller Sedang Ya Tersedia"},{"rc":"54","message":"Nomor Tujuan Salah"},{"rc":"55","message":"Produk Sedang Gangguan"},{"rc":"56","message":"Limit saldo seller"},{"rc":"57","message":"Jumlah Digit Kurang Atau Lebih"},{"rc":"58","message":"Sedang Cut Off"},{"rc":"59","message":"Tujuan di Luar Wilayah/Cluster"},{"rc":"60","message":"Tagihan belum tersedia"},{"rc":"61","message":"Belum pernah melakukan deposit"},{"rc":"62","message":"Seller sedang mengalami gangguan"},{"rc":"63","message":"Tidak support transaksi multi"},{"rc":"64","message":"Tarik tiket gagal, coba nominal lain atau hubungi admin."},{"rc":"65","message":"Limit transaksi multi"},{"rc":"66","message":"Cut Off (Perbaikan Sistem Seller)"},{"rc":"67","message":"Seller belum ter-verfikasi"},{"rc":"68","message":"Stok habis"},{"rc":"69","message":"Harga seller lebih besar dari ketentuan harga Buyer"},{"rc":"70","message":"Timeout Dari Biller"},{"rc":"71","message":"Produk Sedang Tidak Stabil"},{"rc":"72","message":"Lakukan Unreg Paket Dahulu"},{"rc":"80","message":"Akun Anda telah diblokir oleh Seller"},{"rc":"81","message":"Seller ini telah diblokir oleh Anda"},{"rc":"82","message":"Akun Anda belum ter-verfikasi"},{"rc":"99","message":"DF Router Issue"}]';
    $result = json_decode($result);

    foreach ($result as $key => $value) {
        if ($value->rc == $rc) {
            return $value->message;
        }
    }

    return null;
}

function getThemeConfiguration($themename, $merchantid)
{
    $CI = &get_instance();

    $row = $CI->db->get_where('themeconfiguration', array(
        'themename' => $themename,
        'userid' => $merchantid
    ))->row();

    return $row;
}

function getAdminLTE2ClassTheme($key)
{
    $skin = array(
        'blue' => 'skin-blue',
        'black' => 'skin-black',
        'purple' => 'skin-purple',
        'green' => 'skin-green',
        'red' => 'skin-red',
        'yellow' => 'skin-yellow',
        'bluelight' => 'skin-blue-light',
        'blacklight' => 'skin-black-light',
        'purplelight' => 'skin-purple-light',
        'greenlight' => 'skin-green-light',
        'redlight' => 'skin-red-light',
        'yellowlight' => 'skin-yellow-light'
    );

    return isset($skin[$key]) ? $skin[$key] : 'skin-blue';
}

function getCurrentPaymentGateway($merchantid)
{
    $CI = &get_instance();

    $get = $CI->db->get_where('mspaymentgateway', array(
        'userid' => $merchantid,
        'type' => 'Payment Gateway'
    ))->row();

    return $get;
}

function sendMail($host, $username, $password, $port, $email, $companyname, $subject, $message)
{
    $CI = &get_instance();

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return false;
    }

    $config = array(
        'mailtype' => 'html',
        'charset' => 'UTF-8',
        'protocol' => 'smtp',
        'smtp_host' => $host,
        'smtp_user' => $username,
        'smtp_pass' => $password,
        'smtp_crypto' => 'ssl',
        'smtp_port' => $port,
        'crlf' => "\r\n",
        'newline' => "\r\n"
    );

    // Initialize
    $CI->email->initialize($config);
    $CI->email->from($username, $companyname);
    $CI->email->to($email);
    $CI->email->subject($subject);
    $CI->email->message($message);

    return $CI->email->send();
}

function remove_emoji($text)
{
    $text = iconv('UTF-8', 'ISO-8859-15//IGNORE', $text);
    $text = preg_replace('/\s+/', ' ', $text);
    return iconv('ISO-8859-15', 'UTF-8', $text);
}

function removeSymbol($value)
{
    $value = stripslashes($value ?? '');
    $value = htmlspecialchars($value);
    $value = remove_emoji($value);

    return $value;
}

function getCurrentThemeConfiguration($userid)
{
    $CI = &get_instance();

    $get = $CI->db->get_where('themeconfiguration', array(
        'userid' => $userid,
        'isused' => 1
    ))->row();

    return $get != null ? $get->themename : null;
}

function viewTemplate($userid, $view, $vars = array(), $return = false)
{
    $CI = &get_instance();
    $currenttheme = getCurrentThemeConfiguration($userid);

    if ($currenttheme == null) {
        if (isset($vars['content'])) {
            $vars['content'] = "adminlte-2.4.18/" . $vars['content'];
        }

        return $CI->load->view("adminlte-2.4.18/$view", $vars, $return);
    } else {
        if ($currenttheme == 'AdminLTE2') {
            if (isset($vars['content'])) {
                $vars['content'] = "adminlte-2.4.18/" . $vars['content'];
            }

            return $CI->load->view("adminlte-2.4.18/$view", $vars, $return);
        } else if ($currenttheme == 'AdminLTE2-TopNav') {
            if (isset($vars['content'])) {
                $vars['content'] = "adminlte-2.4.18-topnav/" . $vars['content'];
            }

            return $CI->load->view("adminlte-2.4.18-topnav/$view", $vars, $return);
        } else if ($currenttheme == 'HighAdmin-TopNav') {
            if (isset($vars['content'])) {
                $vars['content'] = "highadmin-topnav/" . $vars['content'];
            }

            return $CI->load->view("highadmin-topnav/$view", $vars, $return);
        } else if ($currenttheme == 'Dason-TopNav') {
            if (isset($vars['content'])) {
                $vars['content'] = 'dason-topnav/' . $vars['content'];
            }

            return $CI->load->view('dason-topnav/' . $view, $vars, $return);
        } else if ($currenttheme == 'Fin-App') {
            if (isset($vars['content'])) {
                $vars['content'] = 'fin-app/' . $vars['content'];
            }

            return $CI->load->view('fin-app/' . $view, $vars, $return);
        } else if ($currenttheme == 'Able') {
            if (isset($vars['content'])) {
                $vars['content'] = 'able/' . $vars['content'];
            }

            return $CI->load->view('able/' . $view, $vars, $return);
        } else if ($currenttheme == 'Sobat-Serverppob') {
            if (isset($vars['content'])) {
                $vars['content'] = 'sobat-serverppob/' . $vars['content'];
            }

            return $CI->load->view('sobat-serverppob/' . $view, $vars, $return);
        }
    }
}

function getAverageTransactionSuccess($productid)
{
    $CI = &get_instance();

    $get = $CI->db->select('a.serviceid, SEC_TO_TIME(TIME_TO_SEC(AVG(TIMEDIFF(a.updateddate, a.createddate)))) AS rata2')
        ->from('trorder a')
        ->join('msusers c', 'c.id = a.userid', 'LEFT')
        ->join('msproduct b', 'b.id = a.serviceid', 'LEFT')
        ->where('a.serviceid', $productid)
        ->group_by('a.serviceid')
        ->get();

    if ($get->num_rows() > 0) {
        $row = $get->row();

        if ($row->rata2 != null) {
            $rata2 = explode(':', date('H:i:s', strtotime($row->rata2)));

            return $rata2[0] . " jam " . $rata2[1] . " menit " . $rata2[2] . " detik";
        } else {
            return "-";
        }
    } else {
        return "-";
    }
}

function getAdditionalPages($userid)
{
    $CI = &get_instance();

    return $CI->db->get_where('additionalpage', array('userid' => $userid))->result();
}

function getMerchantBalance($where = array())
{
    try {
        $CI = &get_instance();
        $apikeys = $CI->apikeys->result($where);

        foreach ($apikeys as $key => $value) {
            if ($value->vendor == 'Digiflazz') {
                $digiflazz = new Digiflazz(stringEncryption('decrypt', $value->usercode), stringEncryption('decrypt', $value->apikey));
                $saldo = $digiflazz->check_balance();

                if (isset($saldo->data->deposit)) {
                    $balance = $saldo->data->deposit;

                    $update = array();
                    $update['balance'] = $balance;

                    $CI->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            } else if ($value->vendor == 'VIPayment') {
                $vipayment = new VIPayment(stringEncryption('decrypt', $value->usercode), stringEncryption('decrypt', $value->apikey));
                $profile = $vipayment->profile();

                if (isset($profile->data->balance)) {
                    $balance = $profile->data->balance;

                    $update = array();
                    $update['balance'] = $balance;

                    $CI->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            } else if ($value->vendor == 'BuzzerPanel') {
                $buzzerpanel = new BuzzerPanel(stringEncryption('decrypt', $value->apikey), stringEncryption('decrypt', $value->secretkey));
                $profile = $buzzerpanel->profile();

                if (isset($profile->data->balance)) {
                    $balance = $profile->data->balance;

                    $update = array();
                    $update['balance'] = $balance;

                    $CI->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            } else if ($value->vendor == 'MedanPedia') {
                $medanpedia = new MedanPedia(stringEncryption('decrypt', $value->usercode), stringEncryption('decrypt', $value->apikey));
                $profile = $medanpedia->profile();

                if (isset($profile->status)) {
                    $balance = $profile->data->balance;

                    $update = array();
                    $update['balance'] = $balance;

                    $CI->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            } else if ($value->vendor == 'IrvanKede') {
                $irvankede = new IrvanKede(stringEncryption('decrypt', $value->usercode), stringEncryption('decrypt', $value->apikey));
                $profile = $irvankede->profile();

                if ($profile->status) {
                    $balance = $profile->data->balance;

                    $update = array();
                    $update['balance'] = $balance;

                    $CI->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            } else if ($value->vendor == 'DailyPanel') {
                $dailypanel = new DailyPanel(stringEncryption('decrypt', $value->apikey), stringEncryption('decrypt', $value->secretkey));
                $profile = $dailypanel->profile();

                if (isset($profile->msg->balance)) {
                    $balance = $profile->msg->balance;

                    $update = array();
                    $update['balance'] = $balance;

                    $CI->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            } else if ($value->vendor == 'WStore') {
                $wstore = new WStore(stringEncryption('decrypt', $value->usercode), stringEncryption('decrypt', $value->apikey), stringEncryption('decrypt', $value->secretkey));
                $profile = $wstore->profile();

                if (isset($profile->data->balance)) {
                    $balance = $profile->data->balance;

                    $update = array();
                    $update['balance'] = $balance;

                    $CI->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            } else if ($value->vendor == 'UNDRCTRL') {
                $undrctrl = new UNDRCTRL(stringEncryption('decrypt', $value->apikey));
                $profile = $undrctrl->profile();

                if (isset($profile->balance)) {
                    $balance = $profile->balance;

                    $update = array();
                    $update['balance'] = $balance;

                    $CI->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            } else if ($value->vendor == 'SosmedOnline') {
                $sosmedonline = new SosmedOnline(stringEncryption('decrypt', $value->usercode), stringEncryption('decrypt', $value->apikey));
                $profile = $sosmedonline->profile();

                if (isset($profile->status) && $profile->status) {
                    $balance = $profile->data->balance;

                    $update = array();
                    $update['balance'] = $balance;

                    $CI->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            } else if ($value->vendor == 'SosmedOnlineVIP') {
                $sosmedonlinevip = new SosmedOnlineVIP(stringEncryption('decrypt', $value->usercode), stringEncryption('decrypt', $value->apikey));
                $profile = $sosmedonlinevip->profile();

                if (isset($profile->status) && $profile->status) {
                    $balance = $profile->data->balance;

                    $update = array();
                    $update['balance'] = $balance;

                    $CI->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            } else if ($value->vendor == 'DjuraganSosmed') {
                $djuragansosmed = new DjuraganSosmed(stringEncryption('decrypt', $value->apikey));
                $profile = $djuragansosmed->profile();

                if (isset($profile->balance)) {
                    $balance = $profile->balance;

                    $update = array();
                    $update['balance'] = $balance;

                    $CI->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            } else if ($value->vendor == 'SMMRaja') {
                $smmraja = new SMMRaja(stringEncryption('decrypt', $value->apikey));
                $profile = $smmraja->profile();

                if (isset($profile->balance)) {
                    $balance = $profile->balance * $value->currency_rate;

                    $update = array();
                    $update['balance'] = $balance;

                    $CI->apikeys->update(array(
                        'id' => $value->id,
                    ), $update);
                }
            } else if ($value->vendor == 'SMMIllusion') {
                $smmillusion = new SMMIllusion(stringEncryption('decrypt', $value->apikey));
                $profile = $smmillusion->profile();

                if (isset($profile->balance)) {
                    $balance = $profile->balance * $value->currency_rate;

                    $update = array();
                    $update['balance'] = $balance;

                    $CI->apikeys->update(array(
                        'id' => $value->id,
                    ), $update);
                }
            } else if ($value->vendor == 'V1Pedia') {
                $v1pedia = new V1Pedia(stringEncryption('decrypt', $value->usercode), stringEncryption('decrypt', $value->apikey), stringEncryption('decrypt', $value->secretkey));
                $profile = $v1pedia->profile();

                if (isset($profile->data->balance)) {
                    $balance = $profile->data->balance;

                    $update = array();
                    $update['balance'] = $balance;

                    $CI->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            }
        }
    } catch (Exception $ex) {
        log_message('error', $ex->getMessage());
    }
}

function requestSocket($uri, $data)
{
    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, "https://socket.server-ppobsmm.com/$uri");
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $result = curl_exec($ch);
    curl_close($ch);

    return json_decode($result);
}

function pullTransaction($target)
{
    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, "https://socket.server-ppobsmm.com/realtime/transaction/refresh");
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query(array(
        'targetuserid' => stringEncryption('encrypt', $target)
    )));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $result = curl_exec($ch);
    curl_close($ch);

    return json_decode($result);
}

function log_message_user($type, $message, $userid = null)
{
    $CI = &get_instance();

    $insert = array();
    $insert['userid'] = $userid ?? getCurrentIdUser();
    $insert['type'] = $type;
    $insert['message'] = $message;
    $insert['createddate'] = getCurrentDate();

    return $CI->db->insert('errorlogger', $insert);
}

function get_script_captcha()
{
    $CI = &get_instance();

    return $CI->captcha->getScriptTag();
}

function get_recaptcha_response()
{
    $CI = &get_instance();

    $g_captcha = getPost('g-recaptcha-response');
    $g_response = $CI->captcha->verifyResponse($g_captcha);

    if (!isset($g_response['success']) || $g_response['success'] <> true) {
        return false;
    } else {
        return true;
    }
}

function get_widget_captcha($theme = null)
{
    $CI = &get_instance();

    if ($theme == 'dark') {
        return $CI->captcha->getWidget([
            'data-theme' => 'dark'
        ]);
    }

    return $CI->captcha->getWidget();
}

function getProfitProduct($category, $price, $userid = null)
{
    $CI = &get_instance();
    $userid = $userid == null ? getCurrentIdUser() : $userid;

    $min_price = $CI->db->order_by('minprice', 'ASC')->get_where('msprofit', array(
        'userid' => $userid,
        'category' => $category
    ))->row();

    $margintype = "Nominal";
    if ($min_price != null) {
        $margintype = $min_price->margintype;
        $profit = $min_price->profit;
        $max_price = $min_price->maxprice;
        $min_price = $min_price->minprice;
    } else {
        $profit = 0;
        $min_price = 0;
        $max_price = 0;
    }

    if ($price > $max_price) {
        $profit_get = $CI->db->get_where('msprofit', array(
            'minprice <' => (int)$price,
            'maxprice >' => (int)$price,
            'userid' => $userid,
            'category' => $category
        ))->row();

        if ($profit_get != null) {
            $margintype = $profit_get->margintype;
            $profit = $profit_get->profit;
        } else {
            $profit = 0;
        }
    } else {
        $profit = 0;
    }

    return array(
        'profit' => $profit,
        'margintype' => $margintype
    );
}

function getPriceWithProfit($category, $price, $userid = null)
{
    $profit = getProfitProduct($category, $price, $userid);

    if ($profit['margintype'] == 'Nominal') {
        $price = $profit['profit'];
    } else if ($profit['margintype'] == 'Persentase') {
        $price = $price * ($profit['profit'] / 100);
    }

    return $price;
}

function get_base_url($uri = '')
{
    $prefix = ((isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == "on") ? "https" : "http") . "://";

    if (ENVIRONMENT == 'development') {
        return $prefix . "localhost/serverppob/$uri";
    } else {
        return $prefix . "server-ppobsmm.com/$uri";
    }
}

if (!function_exists('xml_dom')) {
    function xml_dom()
    {
        return new DOMDocument('1.0', 'UTF-8');
    }
}

if (!function_exists('xml_add_child')) {
    function xml_add_child($parent, $name, $value = NULL, $cdata = FALSE)
    {
        if ($parent->ownerDocument != "") {
            $dom = $parent->ownerDocument;
        } else {
            $dom = $parent;
        }

        $child = $dom->createElement($name);
        $parent->appendChild($child);

        if ($value != NULL) {
            if ($cdata) {
                $child->appendChild($dom->createCdataSection($value));
            } else {
                $child->appendChild($dom->createTextNode($value));
            }
        }

        return $child;
    }
}


if (!function_exists('xml_add_attribute')) {
    function xml_add_attribute($node, $name, $value = NULL)
    {
        $dom = $node->ownerDocument;

        $attribute = $dom->createAttribute($name);
        $node->appendChild($attribute);

        if ($value != NULL) {
            $attribute_value = $dom->createTextNode($value);
            $attribute->appendChild($attribute_value);
        }

        return $node;
    }
}


if (!function_exists('xml_print')) {
    function xml_print($dom, $return = FALSE)
    {
        $dom->formatOutput = TRUE;
        $xml = $dom->saveXML();
        if ($return) {
            return $xml;
        } else {
            echo $xml;
        }
    }
}

function readConfig($filename, $key = null)
{
    $config = array();
    $lines = file($filename);

    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line) || $line[0] == '#') {
            continue; // skip empty or comment lines
        }

        $pos = strpos($line, '=');
        if ($pos === false) {
            continue; // skip lines without "=" separator
        }

        $key = trim(substr($line, 0, $pos));
        $value = trim(substr($line, $pos + 1));

        $config[$key] = $value;
    }

    if ($key != null && isset($config[$key])) {
        return $config[$key];
    } else {
        return $config;
    }
}

# create function getIPAddress to get ip address client
function getIPAddress()
{
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    return $ip;
}

# create function getUserAgent to get user agent client
function getUserAgent()
{
    return $_SERVER['HTTP_USER_AGENT'];
}

function strukContent()
{
    return array(
        'clientcode' => 'Kode Transaksi',
        'productname' => 'Nama Produk',
        'target' => 'Tujuan',
        'price' => 'Harga',
        'currentsaldo' => 'Saldo Sebelum',
        'aftercutsaldo' => 'Saldo Sesudah',
        'status' => 'Status',
        'sn' => 'SN',
        'startcount' => 'Start Count',
        'remain' => 'Remain',
        'qty' => 'Jumlah',
        'createddate' => 'Tanggal di Buat',
        'updateddate' => 'Tanggal di Perbarui',
    );
}

function utf8ize($mixed)
{
    if (is_array($mixed)) {
        foreach ($mixed as $key => $value) {
            $mixed[$key] = utf8ize($value);
        }
    } elseif (is_string($mixed)) {
        return mb_convert_encoding($mixed, "UTF-8", "UTF-8");
    }

    return $mixed;
}

function parameterListNotification()
{
    return array(
        '${CODE}' => 'clientcode',
        '${TARGET}' => 'target',
        '${PRICE}' => 'price',
        '${BALANCE_BEFORE}' => 'currentsaldo',
        '${BALANCE_AFTER}' => 'aftercutsaldo',
        '${STATUS}' => 'status',
        '${SN}' => 'sn',
        '${PRODUCT}' => 'productname',
        '${QTY}' => 'qty',
    );
}

function parameterListNotificationDeposit()
{
    return array(
        '${PAYMENT}' => 'payment',
        '${AMOUNT}' => 'nominal',
        '${NOTE}' => 'note',
        '${CODE}' => 'code',
        '${PAYMENTTYPE}' => 'paymenttype',
        '${BONUS}' => 'nominalbonus',
        '${FEE}' => 'fee',
        '${STATUS}' => 'status'
    );
}

function parameterListEmailMarketing()
{
    return array(
        '${NAME}' => 'name',
        '${EMAIL}' => 'email',
        '${BALANCE}' => 'balance',
        '${USERNAME}' => 'username',
        '${PHONENUMBER}' => 'phonenumber',
        '${COMPANYNAME}' => 'companyname',
        '${COMPANYADDRESS}' => 'companyaddress',
        '${UNSUBSCRIBE_LINK}' => 'unsubscribe_link',
        '${HOOK_URL}' => 'hook_url'
    );
}

function replaceParameterNotification($orderid, $userid, $notificationType = 'whatsapp')
{
    $CI = &get_instance();

    $order = $CI->db->select('a.clientcode, a.target, a.price, a.currentsaldo, a.status, a.sn, b.productname, a.qty, a.paymenttype')
        ->join('msproduct b', 'a.serviceid = b.id')
        ->get_where('trorder a', array(
            'a.id' => $orderid
        ))->row();

    if ($order != null) {
        $get = getCurrentUser($userid);

        $type = null;
        if (strtolower($order->status) == 'error' || strtolower($order->status) == 'failed' || strtolower($order->status) == 'gagal') {
            $type = $notificationType == 'firebase' ? 'transaction_failed' : 'failed';
        } else if (strtolower($order->status) == 'pending') {
            $type = $notificationType == 'firebase' ? 'transaction_pending' : 'pending';
        } else if (strtolower($order->status) == 'completed' || strtolower($order->status) == 'success' || strtolower($order->status) == 'sukses') {
            $type = $notificationType == 'firebase' ? 'transaction_success' : 'success';
        }

        // Pilih kolom notifikasi berdasarkan tipe
        $notificationColumn = $notificationType == 'firebase' ? 'firebasenotification' : 'whatsappnotification';

        if ($get->$notificationColumn != null) {
            $notification_data = json_decode($get->$notificationColumn);

            if ($type != null && isset($notification_data->$type)) {
                $notification = $notification_data->$type;

                if ($order->paymenttype == 'Otomatis' || $order->paymenttype == 'Manual') {
                    // remove string with contains ${BALANCE_BEFORE} and ${BALANCE_AFTER}
                    $notification = explode("\n", $notification);
                    foreach ($notification as $key => $value) {
                        if (strpos($value, '${BALANCE_BEFORE}') !== false || strpos($value, '${BALANCE_AFTER}') !== false) {
                            unset($notification[$key]);
                        }
                    }
                    $notification = implode("\n", $notification);
                }

                foreach (parameterListNotification() as $key => $value) {
                    if ($value != 'aftercutsaldo' && $value != 'currentsaldo' && $value != 'price' && $value != 'qty') {
                        $notification = str_replace($key, strtoupper($order->$value ?? ''), $notification ?? '');
                    } else {
                        if ($value == 'aftercutsaldo') {
                            $notification = str_replace($key, "Rp " . IDR($order->currentsaldo - $order->price), $notification);
                        } else if ($value == 'currentsaldo' || $value == 'price') {
                            $notification = str_replace($key, "Rp " . IDR($order->$value), $notification);
                        } else if ($value == 'qty') {
                            $notification = str_replace($key, number_format($order->$value ?? 0), $notification);
                        }
                    }
                }

                return $notification;
            }
        }
    }

    return null;
}

function replaceParameterNotificationDeposit($depositid, $userid, $notificationType = 'whatsapp')
{
    $CI = &get_instance();

    $deposit = $CI->db->select('a.*')
        ->get_where('deposit a', array(
            'a.id' => $depositid
        ))->row();

    if ($deposit != null) {
        $get = getCurrentUser($userid);

        $type = null;
        if (strtolower($deposit->status) == 'cancel' || strtolower($deposit->status) == 'failed' || strtolower($deposit->status) == 'expired') {
            $type = 'deposit_failed';
        } else if (strtolower($deposit->status) == 'pending') {
            $type = 'deposit_pending';
        } else if (strtolower($deposit->status) == 'success' || strtolower($deposit->status) == 'settlement' || strtolower($deposit->status) == 'sukses') {
            $type = 'deposit_success';
        }

        // Pilih kolom notifikasi berdasarkan tipe
        $notificationColumn = $notificationType == 'firebase' ? 'firebasenotification' : 'whatsappnotification';

        if ($get->$notificationColumn != null) {
            $notification_data = json_decode($get->$notificationColumn);

            if ($type != null && isset($notification_data->$type)) {
                $notification = $notification_data->$type;

                foreach (parameterListNotificationDeposit() as $key => $value) {
                    $notification = str_replace($key, strtoupper($deposit->$value ?? ''), $notification ?? '');
                }

                return $notification;
            }
        }
    }

    return null;
}

function replaceParameterEmailMarketing($content, $userid, $recipientid, $bypass_unsubscribe = false, $broadcast_id = null)
{
    $CI = &get_instance();
    $CI->load->model('MsUsers', 'msusers');

    // Get recipient data
    $recipient = $CI->msusers->get(array('id' => $recipientid))->row();

    if (!$recipient) {
        return $content;
    }

    // Get company data from merchant (user who sends the email)
    $merchant = $CI->msusers->get(array('id' => $userid))->row();

    if (!$merchant) {
        return $content;
    }

    // Generate unsubscribe token
    $unsubscribe_data = array(
        'userid' => $recipientid,
        'merchantid' => $userid,
        'timestamp' => time()
    );

    // Add broadcast_id if provided
    if ($broadcast_id !== null) {
        $unsubscribe_data['broadcast_id'] = $broadcast_id;
    }

    $unsubscribe_token = stringEncryption('encrypt', json_encode($unsubscribe_data));

    // Get merchant domain for unsubscribe link
    $merchant_domain = getMerchantDomain($userid);
    $unsubscribe_link = $merchant_domain . 'email-unsubscribe?token=' . urlencode($unsubscribe_token) . '&userid=' . stringEncryption('encrypt', $merchant->id);

    // Generate hook URL for click tracking
    $hook_data = array(
        'userid' => $recipientid,
        'merchantid' => $userid,
        'timestamp' => time()
    );

    // Add broadcast_id if provided
    if ($broadcast_id !== null) {
        $hook_data['broadcast_id'] = $broadcast_id;
    }

    $hook_token = stringEncryption('encrypt', json_encode($hook_data));
    $hook_url = $merchant_domain . 'email-hook/' . urlencode($hook_token) . '?userid=' . stringEncryption('encrypt', $merchant->id);

    // Replace parameters
    foreach (parameterListEmailMarketing() as $parameter => $field) {
        $value = '';

        switch ($field) {
            case 'name':
                $value = $recipient->name ?? '';
                break;
            case 'email':
                $value = $recipient->email ?? '';
                break;
            case 'balance':
                $value = 'Rp ' . number_format($recipient->balance ?? 0, 0, ',', '.');
                break;
            case 'username':
                $value = $recipient->username ?? '';
                break;
            case 'phonenumber':
                $value = $recipient->phonenumber ?? '';
                break;
            case 'companyname':
                $value = $merchant->name ?? '';
                break;
            case 'companyaddress':
                $value = $merchant->address ?? '';
                break;
            case 'unsubscribe_link':
                $value = $unsubscribe_link;
                break;
            case 'hook_url':
                $value = $hook_url;
                break;
        }

        $content = str_replace($parameter, $value, $content);
    }

    // Add automatic unsubscribe footer if not already present
    if (strpos($content, '${UNSUBSCRIBE_LINK}') === false && strpos(strtolower($content), 'unsubscribe') === false && $bypass_unsubscribe == false) {
        $unsubscribe_footer = '<hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">';
        $unsubscribe_footer .= '<div style="text-align: center; font-size: 12px; color: #666; margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">';
        $unsubscribe_footer .= '<p style="margin: 0 0 10px 0;"><strong>Informasi Email Marketing</strong></p>';
        $unsubscribe_footer .= '<p style="margin: 0 0 10px 0;">Email ini dikirim oleh <strong>' . ($merchant->companyname ?? 'Server PPOB & SMM') . '</strong> kepada <strong>' . ($recipient->email ?? '') . '</strong></p>';
        $unsubscribe_footer .= '<p style="margin: 0 0 10px 0;">Jika Anda tidak ingin menerima email marketing dari kami lagi, ';
        $unsubscribe_footer .= '<a href="' . $unsubscribe_link . '" style="color: #dc3545; text-decoration: underline; font-weight: bold;">klik di sini untuk berhenti berlangganan</a>.</p>';
        $unsubscribe_footer .= '<p style="margin: 0; font-size: 11px; color: #999;">Dengan berhenti berlangganan, Anda tidak akan menerima email promosi, penawaran, atau informasi produk dari kami. Anda masih dapat berlangganan kembali kapan saja.</p>';
        $unsubscribe_footer .= '</div>';

        $content .= $unsubscribe_footer;
    }

    return $content;
}

function changePrefixPhone($phone)
{
    $phone = str_replace(' ', '', $phone);
    $phone = str_replace('-', '', $phone);
    $phone = str_replace('+', '', $phone);

    if (substr($phone, 0, 1) == '0') {
        $phone = '62' . substr($phone, 1);
    }

    return $phone;
}

function validateUsername($username)
{
    // Menggunakan regular expression untuk memeriksa format username
    $pattern = '/^[a-zA-Z0-9_]{4,20}$/'; // Format: huruf (besar atau kecil), angka, underscore (_) dengan panjang 4-20 karakter
    if (preg_match($pattern, $username)) {
        return true; // Username valid
    } else {
        return false; // Username tidak valid
    }
}

function stringToNumber($string)
{
    $result = '';
    for ($i = 0; $i < strlen($string); $i++) {
        $result .= ord($string[$i]) ^ ord('karpeldedvtech'[$i % strlen('karpeldedvtech')]);
    }
    return (int)$result;
}

function numberToString($number)
{
    $number = strval($number);
    $result = '';
    for ($i = 0; $i < strlen($number); $i++) {
        $result .= chr(intval($number[$i]) ^ ord('karpeldedvtech'[$i % strlen('karpeldedvtech')]));
    }
    return $result;
}

function tgl_indo($tanggal)
{
    $bulan = array(
        'Januari',
        'Februari',
        'Maret',
        'April',
        'Mei',
        'Juni',
        'Juli',
        'Agustus',
        'September',
        'Oktober',
        'November',
        'Desember'
    );

    $pecahkan = explode('-', $tanggal);

    if (isset($pecahkan[0]) && isset($pecahkan[1]) && isset($pecahkan[2])) {
        return $pecahkan[2] . ' ' . $bulan[(int)$pecahkan[1] - 1] . ' ' . $pecahkan[0];
    } else {
        return date('d F Y', strtotime($tanggal));
    }
}

function paymentType()
{
    return array(
        "Manual" => "Manual",
        "Otomatis" => "Otomatis"
    );
}

function get_client_ip()
{
    $ipaddress = '';
    if (getenv('HTTP_CLIENT_IP'))
        $ipaddress = getenv('HTTP_CLIENT_IP');
    else if (getenv('HTTP_X_FORWARDED_FOR'))
        $ipaddress = getenv('HTTP_X_FORWARDED_FOR');
    else if (getenv('HTTP_X_FORWARDED'))
        $ipaddress = getenv('HTTP_X_FORWARDED');
    else if (getenv('HTTP_FORWARDED_FOR'))
        $ipaddress = getenv('HTTP_FORWARDED_FOR');
    else if (getenv('HTTP_FORWARDED'))
        $ipaddress = getenv('HTTP_FORWARDED');
    else if (getenv('REMOTE_ADDR'))
        $ipaddress = getenv('REMOTE_ADDR');
    else
        $ipaddress = 'UNKNOWN';
    return $ipaddress;
}

function getCurrentBalanceVendor($userid, $vendorid)
{
    $CI = &get_instance();

    $get = $CI->db->get_where('msvendor', array(
        'userid' => $userid,
        'id' => $vendorid
    ))->row();

    return $get->balance ?? 0;
}

function sensorKarakter($string, $jumlahDigit, $pengganti = '*')
{
    // Pisahkan kata-kata dalam string berdasarkan spasi
    $kataKata = explode(' ', $string ?? '');

    // Proses setiap kata
    foreach ($kataKata as &$kata) {
        $panjangKata = strlen($kata);

        if ($panjangKata > $jumlahDigit) {
            // Bagian yang tidak disensor
            $awalKata = substr($kata, 0, $panjangKata - $jumlahDigit);
            // Bagian yang disensor
            $akhirKata = str_repeat($pengganti, $jumlahDigit);
            // Gabungkan kembali
            $kata = $awalKata . $akhirKata;
        } else {
            // Jika panjang kata lebih kecil dari jumlah digit, sensor seluruh kata
            $kata = str_repeat($pengganti, $panjangKata);
        }
    }

    // Gabungkan kembali kata-kata yang telah diproses menjadi satu string
    return implode(' ', $kataKata);
}

function sendNotificationToApps($title, $body, $deviceid, $data = null)
{
    try {
        $factory = (new Factory)->withServiceAccount(APPPATH . 'config/firebase.json');
        $messaging = $factory->createMessaging();

        // Create the notification
        $notification = Notification::create($title, $body);

        // Create the message with target
        $message = CloudMessage::withTarget('token', $deviceid)
            ->withNotification($notification);

        // Add custom data if provided
        if ($data !== null) {
            $message = $message->withData($data);
        }

        // Send the message
        $send = $messaging->send($message);

        return $send;
    } catch (InvalidMessage $e) {
        return $e->getMessage();
    } catch (\Throwable $e) {
        return $e->getMessage();
    }
}

function sendFirebaseNotificationOrder($orderid, $userid)
{
    try {
        $CI = &get_instance();

        // 1. Cek transaksi tersebut dilakukan melalui web/apps (ambil dari trorder kolom orderplatform)
        $order = $CI->db->select('orderplatform, status')
            ->get_where('trorder', array('id' => $orderid))
            ->row();

        if ($order == null) {
            return false;
        }

        // 2. Jika transaksi tersebut dilakukan melalui apps
        if (strtolower($order->orderplatform) == 'apps') {
            // 3. Cari sesi mobile pembeli tersebut (ambil dari mobilesession)
            $mobileSession = $CI->db->select('fcm_token')
                ->get_where('mobilesession', array(
                    'userid' => $userid,
                ))
                ->row();

            // 4. Jika di mobile session terdapat sesi yang aktif dan ada fcm_token
            if ($mobileSession != null && !empty($mobileSession->fcm_token)) {
                // Tentukan title berdasarkan status
                $title = '';
                if (strtolower($order->status) == 'error' || strtolower($order->status) == 'failed' || strtolower($order->status) == 'gagal') {
                    $title = 'Transaksi Gagal';
                } else if (strtolower($order->status) == 'pending') {
                    $title = 'Transaksi Sedang di Proses';
                } else if (strtolower($order->status) == 'completed' || strtolower($order->status) == 'success' || strtolower($order->status) == 'sukses') {
                    $title = 'Transaksi Berhasil';
                }

                // Ambil template dari replaceParameterNotification dengan type firebase
                $messageBody = replaceParameterNotification($orderid, $userid, 'firebase');

                if (!empty($title) && !empty($messageBody)) {
                    // Panggil function sendNotificationToApps
                    $result = sendNotificationToApps($title, $messageBody, $mobileSession->fcm_token);
                    return $result;
                }
            }
        }

        return false;
    } catch (Exception $ex) {
        return false;
    }
}

function sendFirebaseNotificationDeposit($depositid, $userid)
{
    try {
        $CI = &get_instance();

        // 1. Cek deposit tersebut dilakukan melalui web/apps (ambil dari deposit kolom depositplatform)
        $deposit = $CI->db->select('depositplatform, status')
            ->get_where('deposit', array('id' => $depositid))
            ->row();

        if ($deposit == null) {
            return false;
        }

        // 2. Jika deposit tersebut dilakukan melalui apps
        if (strtolower($deposit->depositplatform) == 'apps') {
            // 3. Cari sesi mobile pembeli tersebut (ambil dari mobilesession)
            $mobileSession = $CI->db->select('fcm_token')
                ->get_where('mobilesession', array(
                    'userid' => $userid,
                ))
                ->row();

            // 4. Jika di mobile session terdapat sesi yang aktif dan ada fcm_token
            if ($mobileSession != null && !empty($mobileSession->fcm_token)) {
                // Tentukan title berdasarkan status
                $title = '';
                if (strtolower($deposit->status) == 'cancel' || strtolower($deposit->status) == 'failed' || strtolower($deposit->status) == 'expired') {
                    $title = 'Deposit Gagal';
                } else if (strtolower($deposit->status) == 'pending') {
                    $title = 'Deposit Sedang di Proses';
                } else if (strtolower($deposit->status) == 'success' || strtolower($deposit->status) == 'settlement' || strtolower($deposit->status) == 'sukses') {
                    $title = 'Deposit Berhasil';
                }

                // Ambil template dari replaceParameterNotificationDeposit dengan type firebase
                $messageBody = replaceParameterNotificationDeposit($depositid, $userid, 'firebase');

                if (!empty($title) && !empty($messageBody)) {
                    // Panggil function sendNotificationToApps
                    $result = sendNotificationToApps($title, $messageBody, $mobileSession->fcm_token);
                    return $result;
                }
            }
        }

        return false;
    } catch (Exception $ex) {
        return false;
    }
}
