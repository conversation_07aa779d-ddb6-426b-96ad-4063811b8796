<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!--begin:Menu item-->
<div class="menu-item">
    <!--begin:Menu link-->
    <a class="menu-link <?= uri_string() == 'dashboard' ? 'active' : null ?>" href="<?= base_url('dashboard') ?>">
        <span class="menu-icon">
            <!--begin::Svg Icon | path: icons/duotune/general/gen014.svg-->
            <span class="svg-icon svg-icon-2">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect x="2" y="2" width="9" height="9" rx="2" fill="currentColor"></rect>
                    <rect opacity="0.3" x="13" y="2" width="9" height="9" rx="2" fill="currentColor"></rect>
                    <rect opacity="0.3" x="13" y="13" width="9" height="9" rx="2" fill="currentColor"></rect>
                    <rect opacity="0.3" x="2" y="13" width="9" height="9" rx="2" fill="currentColor"></rect>
                </svg>
            </span>
            <!--end::Svg Icon-->
        </span>

        <span class="menu-title">Beranda</span>
    </a>
    <!--end:Menu link-->
</div>
<!--end:Menu item-->

<?php if (isUser()): ?>
    <!--begin:Menu item-->
    <div class="menu-item">
        <!--begin:Menu link-->
        <a class="menu-link <?= uri_string() == 'academy' ? 'active' : null ?>" href="<?= base_url('academy') ?>">
            <span class="menu-icon">
                <!--begin::Svg Icon | path: icons/duotune/general/gen014.svg-->
                <span class="svg-icon svg-icon-2">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 14L9 11H15L12 14Z" fill="currentColor" />
                        <path opacity="0.3" d="M17.5 11H6.5L4 13.5V6.5C4 5.7 4.7 5 5.5 5H18.5C19.3 5 20 5.7 20 6.5V13.5L17.5 11Z" fill="currentColor" />
                        <path d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z" fill="currentColor" />
                    </svg>
                </span>
                <!--end::Svg Icon-->
            </span>

            <span class="menu-title">Onvaya Academy</span>
        </a>
        <!--end:Menu link-->
    </div>
    <!--end:Menu item-->
<?php endif; ?>

<!--begin:Menu item-->
<div class="menu-item pt-5">
    <!--begin:Menu content-->
    <div class="menu-content">
        <span class="menu-heading fw-bold text-uppercase fs-7">Halaman</span>
    </div>
    <!--end:Menu content-->
</div>
<!--end:Menu item-->

<?php if (isUser()) : ?>
    <?php if ($this->user->companycategory != null && getCurrentUser()->licenseid != null) : ?>
        <!--begin:Menu item-->
        <div data-kt-menu-trigger="click" class="menu-item <?= in_array(uri_string(), transactionFeature()) ? 'here show' : null ?> menu-accordion">
            <!--begin:Menu link-->
            <span class="menu-link">
                <span class="menu-icon">
                    <!--begin::Svg Icon | path: icons/duotune/ecommerce/ecm002.svg-->
                    <span class="svg-icon svg-icon-2">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M21 10H13V11C13 11.6 12.6 12 12 12C11.4 12 11 11.6 11 11V10H3C2.4 10 2 10.4 2 11V13H22V11C22 10.4 21.6 10 21 10Z" fill="currentColor"></path>
                            <path opacity="0.3" d="M12 12C11.4 12 11 11.6 11 11V3C11 2.4 11.4 2 12 2C12.6 2 13 2.4 13 3V11C13 11.6 12.6 12 12 12Z" fill="currentColor"></path>
                            <path opacity="0.3" d="M18.1 21H5.9C5.4 21 4.9 20.6 4.8 20.1L3 13H21L19.2 20.1C19.1 20.6 18.6 21 18.1 21ZM13 18V15C13 14.4 12.6 14 12 14C11.4 14 11 14.4 11 15V18C11 18.6 11.4 19 12 19C12.6 19 13 18.6 13 18ZM17 18V15C17 14.4 16.6 14 16 14C15.4 14 15 14.4 15 15V18C15 18.6 15.4 19 16 19C16.6 19 17 18.6 17 18ZM9 18V15C9 14.4 8.6 14 8 14C7.4 14 7 14.4 7 15V18C7 18.6 7.4 19 8 19C8.6 19 9 18.6 9 18Z" fill="currentColor"></path>
                        </svg>
                    </span>
                    <!--end::Svg Icon-->
                </span>

                <span class="menu-title">Transaksi</span>
                <span class="menu-arrow"></span>
            </span>
            <!--end:Menu link-->

            <!--begin:Menu sub-->
            <div class="menu-sub menu-sub-accordion">
                <?php if ($this->user->companycategory == 'PPOB & SMM' || $this->user->companycategory == 'PPOB') : ?>
                    <!--begin:Menu item-->
                    <div class="menu-item">
                        <!--begin:Menu link-->
                        <a class="menu-link <?= uri_string() == 'transaction/prabayar' ? 'active' : null ?>" href="<?= base_url('transaction/prabayar') ?>">
                            <span class="menu-bullet">
                                <span class="bullet bullet-dot"></span>
                            </span>

                            <span class="menu-title">Prabayar</span>
                        </a>
                        <!--end:Menu link-->
                    </div>
                    <!--end:Menu item-->

                    <!--begin:Menu item-->
                    <div class="menu-item">
                        <!--begin:Menu link-->
                        <a class="menu-link <?= uri_string() == 'transaction/pascabayar' ? 'active' : null ?>" href="<?= base_url('transaction/pascabayar') ?>">
                            <span class="menu-bullet">
                                <span class="bullet bullet-dot"></span>
                            </span>

                            <span class="menu-title">Pascabayar</span>
                        </a>
                        <!--end:Menu link-->
                    </div>
                    <!--end:Menu item-->
                <?php endif; ?>

                <?php if ($this->user->companycategory == 'PPOB & SMM' || $this->user->companycategory == 'SMM') : ?>
                    <!--begin:Menu item-->
                    <div class="menu-item">
                        <!--begin:Menu link-->
                        <a class="menu-link <?= uri_string() == 'transaction/smm' ? 'active' : null ?>" href="<?= base_url('transaction/smm') ?>">
                            <span class="menu-bullet">
                                <span class="bullet bullet-dot"></span>
                            </span>

                            <span class="menu-title">Media Sosial</span>
                        </a>
                        <!--end:Menu link-->
                    </div>
                    <!--end:Menu item-->
                <?php endif; ?>
            </div>
        </div>
        <!--end:Menu item -->
    <?php endif; ?>

    <!--begin:Menu item-->
    <div data-kt-menu-trigger="click" class="menu-item <?= in_array(uri_string(), depositFeature()) ? 'here show' : null ?> menu-accordion">
        <!--begin:Menu link-->
        <span class="menu-link">
            <span class="menu-icon">
                <i class="fonticon fonticon-cash-payment"></i>
            </span>

            <span class="menu-title">Deposit</span>
            <span class="menu-arrow"></span>
        </span>
        <!--end:Menu link-->

        <!--begin:Menu sub-->
        <div class="menu-sub <?= in_array(uri_string(), depositFeature()) ? 'here show' : null ?> menu-sub-accordion">
            <!--begin:Menu item-->
            <div class="menu-item">
                <!--begin:Menu link-->
                <a class="menu-link <?= uri_string() == 'deposit/topup' ? 'active' : null ?>" href="<?= base_url('deposit/topup') ?>">
                    <span class="menu-bullet">
                        <span class="bullet bullet-dot"></span>
                    </span>

                    <span class="menu-title">Topup</span>
                </a>
                <!--end:Menu link-->
            </div>
            <!--end:Menu item-->

            <!--begin:Menu item-->
            <div class="menu-item">
                <!--begin:Menu link-->
                <a class="menu-link <?= uri_string() == 'deposit/history' ? 'active' : null ?>" href="<?= base_url('deposit/history') ?>">
                    <span class="menu-bullet">
                        <span class="bullet bullet-dot"></span>
                    </span>

                    <span class="menu-title">Riwayat</span>
                </a>
                <!--end:Menu link-->
            </div>
            <!--end:Menu item-->
        </div>
    </div>
    <!--end:Menu item -->

    <?php if (getCurrentUser()->licenseid != null) : ?>
        <!--begin:Menu item-->
        <div data-kt-menu-trigger="click" class="menu-item <?= in_array(uri_string(), paymentGatewayFeature()) ? 'here show' : null ?> menu-accordion">
            <!--begin:Menu link-->
            <span class="menu-link">
                <span class="menu-icon">
                    <i class="bi bi-currency-dollar"></i>
                </span>

                <span class="menu-title">Payment Gateway</span>
                <span class="menu-arrow"></span>
            </span>
            <!--end:Menu link-->

            <!--begin:Menu sub-->
            <div class="menu-sub <?= in_array(uri_string(), paymentGatewayFeature()) ? 'here show' : null ?> menu-sub-accordion">
                <!--begin:Menu item-->
                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'paymentgateway/manual' ? 'active' : null ?>" href="<?= base_url('paymentgateway/manual') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Manual</span>
                    </a>
                    <!--end:Menu link-->
                </div>
                <!--end:Menu item-->

                <!--begin:Menu item-->
                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'paymentgateway/automatic' ? 'active' : null ?>" href="<?= base_url('paymentgateway/automatic') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Otomatis</span>
                    </a>
                    <!--end:Menu link-->
                </div>
                <!--end:Menu item-->
            </div>
        </div>
        <!--end:Menu item -->
    <?php endif; ?>
<?php endif; ?>


<?php if (((isUser()) && getCurrentUser()->licenseid != null) || isAdmin()) : ?>
    <div data-kt-menu-trigger="click" class="menu-item <?= in_array(uri_string(), usersFeature()) ? 'here show' : null ?> menu-accordion">
        <!--begin:Menu link-->
        <span class="menu-link">
            <span class="menu-icon">
                <i class="bi bi-person"></i>
            </span>

            <span class="menu-title">Member</span>
            <span class="menu-arrow"></span>
        </span>
        <!--end:Menu link-->

        <!--begin:Menu sub-->
        <div class="menu-sub <?= in_array(uri_string(), usersFeature()) ? 'here show' : null ?> menu-sub-accordion">
            <?php if (isUser()) : ?>
                <!--begin:Menu item-->
                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'users/data' ? 'active' : null ?>" href="<?= base_url('users/data') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Data Pengguna</span>
                    </a>
                    <!--end:Menu link-->
                </div>
                <!--end:Menu item-->

                <!--begin:Menu item-->
                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'users/verificationkyc' ? 'active' : null ?>" href="<?= base_url('users/verificationkyc') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Verifikasi KYC</span>
                    </a>
                    <!--end:Menu link-->
                </div>
                <!--end:Menu item-->

                <!--begin:Menu item-->
                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'users/transfer/saldo' ? 'active' : null ?>" href="<?= base_url('users/transfer/saldo') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Transfer Saldo</span>
                    </a>
                    <!--end:Menu link-->
                </div>
                <!--end:Menu item-->

                <!--begin:Menu item-->
                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'users/deposit/history' ? 'active' : null ?>" href="<?= base_url('users/deposit/history') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Riwayat Topup</span>
                    </a>
                    <!--end:Menu link-->
                </div>
                <!--end:Menu item-->

                <!--begin:Menu item-->
                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'users/ticket/history' ? 'active' : null ?>" href="<?= base_url('users/ticket/history') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Tiket</span>
                    </a>
                    <!--end:Menu link-->
                </div>
            <?php elseif (isAdmin()) : ?>
                <!--begin:Menu item-->
                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'admins/deposit/history' ? 'active' : null ?>" href="<?= base_url('admins/deposit/history') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Riwayat Topup</span>
                    </a>
                    <!--end:Menu link-->
                </div>

                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'admins/invoice/history' ? 'active' : null ?>" href="<?= base_url('admins/invoice/history') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Tagihan</span>
                    </a>
                    <!--end:Menu link-->
                </div>

                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'manage/historyrequestuploadplaystore' ? 'active' : null ?>" href="<?= base_url('manage/historyrequestuploadplaystore') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Pengajuan Playstore</span>
                    </a>
                    <!--end:Menu link-->
                </div>
                <!--end:Menu item-->

                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'admins/transfer/saldo' ? 'active' : null ?>" href="<?= base_url('admins/transfer/saldo') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Transfer Saldo</span>
                    </a>
                    <!--end:Menu link-->
                </div>
                <!--end:Menu item-->

                <!--begin:Menu item-->
                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'manage/member' ? 'active' : null ?>" href="<?= base_url('manage/member') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Member</span>
                    </a>
                    <!--end:Menu link-->
                </div>
                <!--end:Menu item-->
            <?php endif; ?>
        </div>
    </div>
    <!--end:Menu item -->
<?php endif; ?>

<?php if ((isUser()) && getCurrentUser()->licenseid != null) : ?>
    <?php if ($this->user->companycategory != null) : ?>
        <div data-kt-menu-trigger="click" class="menu-item <?= in_array(uri_string(), productFeature()) ? 'here show' : null ?> menu-accordion">
            <!--begin:Menu link-->
            <span class="menu-link">
                <span class="menu-icon">
                    <i class="bi bi-list"></i>
                </span>

                <span class="menu-title">Produk</span>
                <span class="menu-arrow"></span>
            </span>
            <!--end:Menu link-->

            <!--begin:Menu sub-->
            <div class="menu-sub <?= in_array(uri_string(), productFeature()) ? 'here show' : null ?> menu-sub-accordion">
                <?php if ($this->user->companycategory == 'PPOB & SMM' || $this->user->companycategory == 'PPOB') : ?>
                    <!--begin:Menu item-->
                    <div class="menu-item">
                        <!--begin:Menu link-->
                        <a class="menu-link <?= uri_string() == 'database/stockproduct' ? 'active' : null ?>" href="<?= base_url('database/stockproduct') ?>">
                            <span class="menu-bullet">
                                <span class="bullet bullet-dot"></span>
                            </span>

                            <span class="menu-title">Stok Produk</span>
                        </a>
                        <!--end:Menu link-->
                    </div>
                    <!--end:Menu item-->

                    <!--begin:Menu item-->
                    <div class="menu-item">
                        <!--begin:Menu link-->
                        <a class="menu-link <?= uri_string() == 'product/prabayar' ? 'active' : null ?>" href="<?= base_url('product/prabayar') ?>">
                            <span class="menu-bullet">
                                <span class="bullet bullet-dot"></span>
                            </span>

                            <span class="menu-title">Prabayar</span>
                        </a>
                        <!--end:Menu link-->
                    </div>
                    <!--end:Menu item-->

                    <!--begin:Menu item-->
                    <div class="menu-item">
                        <!--begin:Menu link-->
                        <a class="menu-link <?= uri_string() == 'product/pascabayar' ? 'active' : null ?>" href="<?= base_url('product/pascabayar') ?>">
                            <span class="menu-bullet">
                                <span class="bullet bullet-dot"></span>
                            </span>

                            <span class="menu-title">Pascabayar</span>
                        </a>
                        <!--end:Menu link-->
                    </div>
                    <!--end:Menu item-->
                <?php endif; ?>

                <?php if ($this->user->companycategory == 'PPOB & SMM' || $this->user->companycategory == 'SMM') : ?>
                    <!--begin:Menu item-->
                    <div class="menu-item">
                        <!--begin:Menu link-->
                        <a class="menu-link <?= uri_string() == 'product/smm' ? 'active' : null ?>" href="<?= base_url('product/smm') ?>">
                            <span class="menu-bullet">
                                <span class="bullet bullet-dot"></span>
                            </span>

                            <span class="menu-title">Media Sosial</span>
                        </a>
                        <!--end:Menu link-->
                    </div>
                    <!--end:Menu item-->
                <?php endif; ?>

                <?php if (getCurrentUser()->licenseid != null) : ?>
                    <!--begin:Menu item-->
                    <div class="menu-item">
                        <!--begin:Menu link-->
                        <a class="menu-link <?= uri_string() == 'product/information' ? 'active' : null ?>" href="<?= base_url('product/information') ?>">
                            <span class="menu-bullet">
                                <span class="bullet bullet-dot"></span>
                            </span>

                            <span class="menu-title">Informasi Layanan</span>
                        </a>
                        <!--end:Menu link-->
                    </div>
                    <!--end:Menu item-->
                <?php endif; ?>
            </div>
        </div>
        <!--end:Menu item -->
    <?php endif; ?>
<?php endif; ?>

<?php if (((isUser()) && getCurrentUser()->licenseid != null) || isAdmin()) : ?>
    <!--begin:Menu item-->
    <div data-kt-menu-trigger="click" class="menu-item <?= in_array(uri_string(), managementFeature()) ? 'here show' : null ?> menu-accordion">
        <!--begin:Menu link-->
        <span class="menu-link">
            <span class="menu-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path opacity="0.3" d="M5 8.04999L11.8 11.95V19.85L5 15.85V8.04999Z" fill="currentColor" />
                    <path d="M20.1 6.65L12.3 2.15C12 1.95 11.6 1.95 11.3 2.15L3.5 6.65C3.2 6.85 3 7.15 3 7.45V16.45C3 16.75 3.2 17.15 3.5 17.25L11.3 21.75C11.5 21.85 11.6 21.85 11.8 21.85C12 21.85 12.1 21.85 12.3 21.75L20.1 17.25C20.4 17.05 20.6 16.75 20.6 16.45V7.45C20.6 7.15 20.4 6.75 20.1 6.65ZM5 15.85V7.95L11.8 4.05L18.6 7.95L11.8 11.95V19.85L5 15.85Z" fill="currentColor" />
                </svg>
            </span>

            <span class="menu-title">Management</span>
            <span class="menu-arrow"></span>
        </span>
        <!--end:Menu link-->

        <!--begin:Menu sub-->
        <div class="menu-sub <?= in_array(uri_string(), managementFeature()) ? 'here show' : null ?> menu-sub-accordion">
            <?php if (isUser()) : ?>
                <!--begin:Menu item-->
                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'manage/addpages' ? 'active' : null ?>" href="<?= base_url('manage/addpages') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Halaman Tambahan</span>
                    </a>
                    <!--end:Menu link-->
                </div>
                <!--end:Menu item-->

                <!--begin:Menu item-->
                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'manage/category/product' ? 'active' : null ?>" href="<?= base_url('manage/category/product') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Kategori Produk</span>
                    </a>
                    <!--end:Menu link-->
                </div>
                <!--end:Menu item-->

                <!--begin:Menu item-->
                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'manage/news' ? 'active' : null ?>" href="<?= base_url('manage/news') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Berita</span>
                    </a>
                    <!--end:Menu link-->
                </div>
                <!--end:Menu item-->

                <!--begin:Menu item-->
                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'manage/slider' ? 'active' : null ?>" href="<?= base_url('manage/slider') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Slider</span>
                    </a>
                    <!--end:Menu link-->
                </div>
                <!--end:Menu item-->

                <!--begin:Menu item-->
                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'manage/icons' ? 'active' : null ?>" href="<?= base_url('manage/icons') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Icon</span>
                    </a>
                    <!--end:Menu link-->
                </div>
                <!--end:Menu item-->

                <!--begin:Menu item-->
                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'manage/role' ? 'active' : null ?>" href="<?= base_url('manage/role') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Hak Akses</span>
                    </a>
                    <!--end:Menu link-->
                </div>
                <!--end:Menu item-->

                <?php if ($this->user->companycategory == 'PPOB & SMM' || $this->user->companycategory == 'SMM') : ?>
                    <!--begin:Menu item-->
                    <div class="menu-item">
                        <!--begin:Menu link-->
                        <a class="menu-link <?= uri_string() == 'manage/platformsosmed' ? 'active' : null ?>" href="<?= base_url('manage/platformsosmed') ?>">
                            <span class="menu-bullet">
                                <span class="bullet bullet-dot"></span>
                            </span>

                            <span class="menu-title">Platform Sosmed</span>
                        </a>
                        <!--end:Menu link-->
                    </div>
                    <!--end:Menu item-->
                <?php endif; ?>

                <!--begin:Menu item-->
                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'manage/livechat' ? 'active' : null ?>" href="<?= base_url('manage/livechat') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Live Chat</span>
                    </a>
                    <!--end:Menu link-->
                </div>

                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'manage/customersupport' ? 'active' : null ?>" href="<?= base_url('manage/customersupport') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Kontak</span>
                    </a>
                    <!--end:Menu link-->
                </div>

                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'manage/prefixoperator' ? 'active' : null ?>" href="<?= base_url('manage/prefixoperator') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Prefix Operator</span>
                    </a>
                    <!--end:Menu link-->
                </div>

                <?php if (getCurrentUser()->unlock_app == 1) : ?>
                    <!--begin:Menu item-->
                    <div class="menu-item">
                        <!--begin:Menu link-->
                        <a class="menu-link <?= uri_string() == 'manage/customapp' ? 'active' : null ?>" href="<?= base_url('manage/customapp') ?>">
                            <span class="menu-bullet">
                                <span class="bullet bullet-dot"></span>
                            </span>

                            <span class="menu-title">Custom Aplikasi</span>
                        </a>
                        <!--end:Menu link-->
                    </div>
                    <!--end:Menu item-->
                <?php endif; ?>

                <?php if (getCurrentUser()->access_emailmarketing == 1) : ?>
                    <!--begin:Menu item-->
                    <div class="menu-item">
                        <!--begin:Menu link-->
                        <a class="menu-link <?= uri_string() == 'manage/broadcastemailmarketing' ? 'active' : null ?>" href="<?= base_url('manage/broadcastemailmarketing') ?>">
                            <span class="menu-bullet">
                                <span class="bullet bullet-dot"></span>
                            </span>

                            <span class="menu-title">Broadcast Email</span>
                        </a>
                        <!--end:Menu link-->
                    </div>
                    <!--end:Menu item-->
                <?php endif; ?>

                <?php if (getCurrentThemeConfiguration(getCurrentIdUser()) == 'Fin-App') : ?>
                    <!--begin:Menu item-->
                    <div class="menu-item">
                        <!--begin:Menu link-->
                        <a class="menu-link <?= uri_string() == 'manage/faq' ? 'active' : null ?>" href="<?= base_url('manage/faq') ?>">
                            <span class="menu-bullet">
                                <span class="bullet bullet-dot"></span>
                            </span>

                            <span class="menu-title">FAQ</span>
                        </a>
                        <!--end:Menu link-->
                    </div>
                    <!--end:Menu item-->
                <?php endif; ?>
            <?php elseif (isAdmin()) : ?>
                <!--begin:Menu item-->
                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'manage/news' ? 'active' : null ?>" href="<?= base_url('manage/news') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Berita</span>
                    </a>
                    <!--end:Menu link-->
                </div>
                <!--end:Menu item-->

                <!--begin:Menu item-->
                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'manage/vendor' ? 'active' : null ?>" href="<?= base_url('manage/vendor') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Vendor</span>
                    </a>
                    <!--end:Menu link-->
                </div>
                <!--end:Menu item-->

                <!--begin:Menu item-->
                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'manage/privacypolicy' ? 'active' : null ?>" href="<?= base_url('manage/privacypolicy') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Kebijakan Privasi</span>
                    </a>
                    <!--end:Menu link-->
                </div>
                <!--end:Menu item-->

                <!--begin:Menu item-->
                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'manage/termsofservice' ? 'active' : null ?>" href="<?= base_url('manage/termsofservice') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Syarat dan Ketentuan</span>
                    </a>
                    <!--end:Menu link-->
                </div>
                <!--end:Menu item-->

                <!--begin:Menu item-->
                <div class="menu-item">
                    <!--begin:Menu link-->
                    <a class="menu-link <?= uri_string() == 'manage/onvayaacademy' ? 'active' : null ?>" href="<?= base_url('manage/onvayaacademy') ?>">
                        <span class="menu-bullet">
                            <span class="bullet bullet-dot"></span>
                        </span>

                        <span class="menu-title">Onvaya Academy</span>
                    </a>
                    <!--end:Menu link-->
                </div>
                <!--end:Menu item-->
            <?php endif; ?>
        </div>
    </div>
    <!--end:Menu item -->
<?php endif; ?>

<!--begin:Menu item-->
<div data-kt-menu-trigger="click" class="menu-item <?= in_array(uri_string(), accountFeature()) ? 'here show' : null ?> menu-accordion">
    <!--begin:Menu link-->
    <span class="menu-link">
        <span class="menu-icon">
            <!--begin::Svg Icon | path: icons/duotune/general/gen022.svg-->
            <span class="svg-icon svg-icon-2">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M11.2929 2.70711C11.6834 2.31658 12.3166 2.31658 12.7071 2.70711L15.2929 5.29289C15.6834 5.68342 15.6834 6.31658 15.2929 6.70711L12.7071 9.29289C12.3166 9.68342 11.6834 9.68342 11.2929 9.29289L8.70711 6.70711C8.31658 6.31658 8.31658 5.68342 8.70711 5.29289L11.2929 2.70711Z" fill="currentColor" />
                    <path d="M11.2929 14.7071C11.6834 14.3166 12.3166 14.3166 12.7071 14.7071L15.2929 17.2929C15.6834 17.6834 15.6834 18.3166 15.2929 18.7071L12.7071 21.2929C12.3166 21.6834 11.6834 21.6834 11.2929 21.2929L8.70711 18.7071C8.31658 18.3166 8.31658 17.6834 8.70711 17.2929L11.2929 14.7071Z" fill="currentColor" />
                    <path opacity="0.3" d="M5.29289 8.70711C5.68342 8.31658 6.31658 8.31658 6.70711 8.70711L9.29289 11.2929C9.68342 11.6834 9.68342 12.3166 9.29289 12.7071L6.70711 15.2929C6.31658 15.6834 5.68342 15.6834 5.29289 15.2929L2.70711 12.7071C2.31658 12.3166 2.31658 11.6834 2.70711 11.2929L5.29289 8.70711Z" fill="currentColor" />
                    <path opacity="0.3" d="M17.2929 8.70711C17.6834 8.31658 18.3166 8.31658 18.7071 8.70711L21.2929 11.2929C21.6834 11.6834 21.6834 12.3166 21.2929 12.7071L18.7071 15.2929C18.3166 15.6834 17.6834 15.6834 17.2929 15.2929L14.7071 12.7071C14.3166 12.3166 14.3166 11.6834 14.7071 11.2929L17.2929 8.70711Z" fill="currentColor" />
                </svg>
            </span>
            <!--end::Svg Icon-->
        </span>
        <span class="menu-title">Akun</span>
        <span class="menu-arrow"></span>
    </span>
    <!--end:Menu link-->

    <!--begin:Menu sub-->
    <div class="menu-sub <?= in_array(uri_string(), accountFeature()) ? 'here show' : null ?> menu-sub-accordion">
        <!--begin:Menu item-->
        <div class="menu-item">
            <!--begin:Menu link-->
            <a class="menu-link <?= uri_string() == 'settings/account' ? 'active' : null ?>" href="<?= base_url('settings/account') ?>">
                <span class="menu-bullet">
                    <span class="bullet bullet-dot"></span>
                </span>
                <span class="menu-title">Pengaturan</span>
            </a>
            <!--end:Menu link-->
        </div>
        <!--end:Menu item-->

        <!--begin:Menu item-->
        <div class="menu-item">
            <!--begin:Menu link-->
            <a class="menu-link <?= uri_string() == 'settings/password' ? 'active' : null ?>" href="<?= base_url('settings/password') ?>">
                <span class="menu-bullet">
                    <span class="bullet bullet-dot"></span>
                </span>
                <span class="menu-title">Password</span>
            </a>
            <!--end:Menu link-->
        </div>
        <!--end:Menu item-->

        <?php if (isUser() && getCurrentUser()->licenseid != null) : ?>
            <!--begin:Menu item-->
            <div class="menu-item">
                <!--begin:Menu link-->
                <a class="menu-link <?= uri_string() == 'settings/seo' ? 'active' : null ?>" href="<?= base_url('settings/seo') ?>">
                    <span class="menu-bullet">
                        <span class="bullet bullet-dot"></span>
                    </span>
                    <span class="menu-title">SEO</span>
                </a>
                <!--end:Menu link-->
            </div>
            <!--end:Menu item-->

            <!--begin:Menu item-->
            <div class="menu-item">
                <!--begin:Menu link-->
                <a class="menu-link <?= uri_string() == 'settings/apikey' ? 'active' : null ?>" href="<?= base_url('settings/apikey') ?>">
                    <span class="menu-bullet">
                        <span class="bullet bullet-dot"></span>
                    </span>
                    <span class="menu-title">API Key</span>
                </a>
                <!--end:Menu link-->
            </div>
            <!--end:Menu item-->

            <!--begin:Menu item-->
            <div class="menu-item">
                <!--begin:Menu link-->
                <a class="menu-link <?= uri_string() == 'settings/margins' ? 'active' : null ?>" href="<?= base_url('settings/margins') ?>">
                    <span class="menu-bullet">
                        <span class="bullet bullet-dot"></span>
                    </span>
                    <span class="menu-title">Margin/Profit</span>
                </a>
                <!--end:Menu link-->
            </div>
            <!--end:Menu item-->

            <!--begin:Menu item-->
            <div class="menu-item">
                <!--begin:Menu link-->
                <a class="menu-link <?= uri_string() == 'settings/struk' ? 'active' : null ?>" href="<?= base_url('settings/struk') ?>">
                    <span class="menu-bullet">
                        <span class="bullet bullet-dot"></span>
                    </span>
                    <span class="menu-title">Konten Struk</span>
                </a>
                <!--end:Menu link-->
            </div>
            <!--end:Menu item-->

            <!--begin:Menu item-->
            <div class="menu-item">
                <!--begin:Menu link-->
                <a class="menu-link <?= uri_string() == 'settings/domain' ? 'active' : null ?>" href="<?= base_url('settings/domain') ?>">
                    <span class="menu-bullet">
                        <span class="bullet bullet-dot"></span>
                    </span>
                    <span class="menu-title">Domain</span>
                </a>
                <!--end:Menu link-->
            </div>
            <!--end:Menu item-->

            <!--begin:Menu item-->
            <div class="menu-item">
                <!--begin:Menu link-->
                <a class="menu-link <?= uri_string() == 'settings/theme' ? 'active' : null ?>" href="<?= base_url('settings/theme') ?>">
                    <span class="menu-bullet">
                        <span class="bullet bullet-dot"></span>
                    </span>
                    <span class="menu-title">Tema</span>
                </a>
                <!--end:Menu link-->
            </div>
            <!--end:Menu item-->

            <!--begin:Menu item-->
            <div class="menu-item">
                <!--begin:Menu link-->
                <a class="menu-link <?= uri_string() == 'settings/smtp' ? 'active' : null ?>" href="<?= base_url('settings/smtp') ?>">
                    <span class="menu-bullet">
                        <span class="bullet bullet-dot"></span>
                    </span>
                    <span class="menu-title">SMTP Email</span>
                </a>
                <!--end:Menu link-->
            </div>
            <!--end:Menu item-->

            <!--begin:Menu item-->
            <div class="menu-item">
                <!--begin:Menu link-->
                <a class="menu-link <?= uri_string() == 'settings/logmessage' ? 'active' : null ?>" href="<?= base_url('settings/logmessage') ?>">
                    <span class="menu-bullet">
                        <span class="bullet bullet-dot"></span>
                    </span>
                    <span class="menu-title">Log Error</span>
                </a>
                <!--end:Menu link-->
            </div>
            <!--end:Menu item-->


        <?php endif; ?>
    </div>
    <!--end:Menu sub-->
</div>
<!--end:Menu item-->

<?php if (isUser() && getCurrentUser()->licenseid != null) : ?>
    <!--begin:Menu item-->
    <div data-kt-menu-trigger="click" class="menu-item <?= in_array(uri_string(), ['settings/whatsapp', 'settings/firebase']) ? 'here show' : null ?> menu-accordion">
        <!--begin:Menu link-->
        <span class="menu-link">
            <span class="menu-icon">
                <!--begin::Svg Icon | path: icons/duotune/communication/com012.svg-->
                <span class="svg-icon svg-icon-2">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path opacity="0.3" d="M20 3H4C2.89543 3 2 3.89543 2 5V16C2 17.1046 2.89543 18 4 18H4.5C4.77614 18 5 18.2239 5 18.5V21L10.5 18H20C21.1046 18 22 17.1046 22 16V5C22 3.89543 21.1046 3 20 3Z" fill="currentColor" />
                        <rect x="6" y="12" width="7" height="2" rx="1" fill="currentColor" />
                        <rect x="6" y="7" width="12" height="2" rx="1" fill="currentColor" />
                    </svg>
                </span>
                <!--end::Svg Icon-->
            </span>
            <span class="menu-title">Notifikasi</span>
            <span class="menu-arrow"></span>
        </span>
        <!--end:Menu link-->

        <!--begin:Menu sub-->
        <div class="menu-sub <?= in_array(uri_string(), ['settings/whatsapp', 'settings/firebase']) ? 'here show' : null ?> menu-sub-accordion">
            <!--begin:Menu item-->
            <div class="menu-item">
                <!--begin:Menu link-->
                <a class="menu-link <?= uri_string() == 'settings/whatsapp' ? 'active' : null ?>" href="<?= base_url('settings/whatsapp') ?>">
                    <span class="menu-bullet">
                        <span class="bullet bullet-dot"></span>
                    </span>
                    <span class="menu-title">WhatsApp</span>
                </a>
                <!--end:Menu link-->
            </div>
            <!--end:Menu item-->

            <!--begin:Menu item-->
            <div class="menu-item">
                <!--begin:Menu link-->
                <a class="menu-link <?= uri_string() == 'settings/firebase' ? 'active' : null ?>" href="<?= base_url('settings/firebase') ?>">
                    <span class="menu-bullet">
                        <span class="bullet bullet-dot"></span>
                    </span>
                    <span class="menu-title">Firebase</span>
                </a>
                <!--end:Menu link-->
            </div>
            <!--end:Menu item-->
        </div>
        <!--end:Menu sub-->
    </div>
    <!--end:Menu item-->
<?php endif; ?>

<?php if (isUser()) : ?>
    <!--begin:Menu item-->
    <div class="menu-item">
        <!--begin:Menu link-->
        <a class="menu-link <?= uri_string() == 'report/profit/monthly' ? 'active' : null ?>" href="<?= base_url('report/profit/monthly') ?>">
            <span class="menu-icon">
                <!--begin::Svg Icon -->
                <span class="svg-icon svg-icon-1"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path opacity="0.3" d="M5 15C3.3 15 2 13.7 2 12C2 10.3 3.3 9 5 9H5.10001C5.00001 8.7 5 8.3 5 8C5 5.2 7.2 3 10 3C11.9 3 13.5 4 14.3 5.5C14.8 5.2 15.4 5 16 5C17.7 5 19 6.3 19 8C19 8.4 18.9 8.7 18.8 9C18.9 9 18.9 9 19 9C20.7 9 22 10.3 22 12C22 13.7 20.7 15 19 15H5ZM5 12.6H13L9.7 9.29999C9.3 8.89999 8.7 8.89999 8.3 9.29999L5 12.6Z" fill="currentColor" />
                        <path d="M17 17.4V12C17 11.4 16.6 11 16 11C15.4 11 15 11.4 15 12V17.4H17Z" fill="currentColor" />
                        <path opacity="0.3" d="M12 17.4H20L16.7 20.7C16.3 21.1 15.7 21.1 15.3 20.7L12 17.4Z" fill="currentColor" />
                        <path d="M8 12.6V18C8 18.6 8.4 19 9 19C9.6 19 10 18.6 10 18V12.6H8Z" fill="currentColor" />
                    </svg>
                </span>
                <!--end::Svg Icon-->
            </span>

            <span class="menu-title">Laporan Keuntungan</span>
        </a>
        <!--end:Menu link-->
    </div>
    <!--end:Menu item-->

    <!--begin:Menu item-->
    <div class="menu-item">
        <!--begin:Menu link-->
        <a class="menu-link <?= uri_string() == 'manage/invoices' ? 'active' : null ?>" href="<?= base_url('manage/invoices') ?>">
            <span class="menu-icon">
                <span class="svg-icon svg-icon-2">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path opacity="0.3" d="M18 21.6C16.3 21.6 15 20.3 15 18.6V2.50001C15 2.20001 14.6 1.99996 14.3 2.19996L13 3.59999L11.7 2.3C11.3 1.9 10.7 1.9 10.3 2.3L9 3.59999L7.70001 2.3C7.30001 1.9 6.69999 1.9 6.29999 2.3L5 3.59999L3.70001 2.3C3.50001 2.1 3 2.20001 3 3.50001V18.6C3 20.3 4.3 21.6 6 21.6H18Z" fill="currentColor" />
                        <path d="M12 12.6H11C10.4 12.6 10 12.2 10 11.6C10 11 10.4 10.6 11 10.6H12C12.6 10.6 13 11 13 11.6C13 12.2 12.6 12.6 12 12.6ZM9 11.6C9 11 8.6 10.6 8 10.6H6C5.4 10.6 5 11 5 11.6C5 12.2 5.4 12.6 6 12.6H8C8.6 12.6 9 12.2 9 11.6ZM9 7.59998C9 6.99998 8.6 6.59998 8 6.59998H6C5.4 6.59998 5 6.99998 5 7.59998C5 8.19998 5.4 8.59998 6 8.59998H8C8.6 8.59998 9 8.19998 9 7.59998ZM13 7.59998C13 6.99998 12.6 6.59998 12 6.59998H11C10.4 6.59998 10 6.99998 10 7.59998C10 8.19998 10.4 8.59998 11 8.59998H12C12.6 8.59998 13 8.19998 13 7.59998ZM13 15.6C13 15 12.6 14.6 12 14.6H10C9.4 14.6 9 15 9 15.6C9 16.2 9.4 16.6 10 16.6H12C12.6 16.6 13 16.2 13 15.6Z" fill="currentColor" />
                        <path d="M15 18.6C15 20.3 16.3 21.6 18 21.6C19.7 21.6 21 20.3 21 18.6V12.5C21 12.2 20.6 12 20.3 12.2L19 13.6L17.7 12.3C17.3 11.9 16.7 11.9 16.3 12.3L15 13.6V18.6Z" fill="currentColor" />
                    </svg>
                </span>
            </span>

            <span class="menu-title">Tagihan</span>
        </a>
        <!--end:Menu link-->
    </div>
    <!--end:Menu item-->

    <!--begin:Menu item-->
    <div class="menu-item">
        <!--begin:Menu link-->
        <a class="menu-link <?= uri_string() == 'addons' ? 'active' : null ?>" href="<?= base_url('addons') ?>">
            <span class="menu-icon">
                <span class="svg-icon svg-icon-2">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path opacity="0.3" d="M3 13H10C10.6 13 11 13.4 11 14V21C11 21.6 10.6 22 10 22H3C2.4 22 2 21.6 2 21V14C2 13.4 2.4 13 3 13Z" fill="currentColor" />
                        <path d="M7 16H6C5.4 16 5 15.6 5 15V13H8V15C8 15.6 7.6 16 7 16Z" fill="currentColor" />
                        <path opacity="0.3" d="M14 13H21C21.6 13 22 13.4 22 14V21C22 21.6 21.6 22 21 22H14C13.4 22 13 21.6 13 21V14C13 13.4 13.4 13 14 13Z" fill="currentColor" />
                        <path d="M18 16H17C16.4 16 16 15.6 16 15V13H19V15C19 15.6 18.6 16 18 16Z" fill="currentColor" />
                        <path opacity="0.3" d="M3 2H10C10.6 2 11 2.4 11 3V10C11 10.6 10.6 11 10 11H3C2.4 11 2 10.6 2 10V3C2 2.4 2.4 2 3 2Z" fill="currentColor" />
                        <path d="M7 5H6C5.4 5 5 4.6 5 4V2H8V4C8 4.6 7.6 5 7 5Z" fill="currentColor" />
                        <path opacity="0.3" d="M14 2H21C21.6 2 22 2.4 22 3V10C22 10.6 21.6 11 21 11H14C13.4 11 13 10.6 13 10V3C13 2.4 13.4 2 14 2Z" fill="currentColor" />
                        <path d="M18 5H17C16.4 5 16 4.6 16 4V2H19V4C19 4.6 18.6 5 18 5Z" fill="currentColor" />
                    </svg>
                </span>
            </span>

            <span class="menu-title">Addons</span>
        </a>
        <!--end:Menu link-->
    </div>
    <!--end:Menu item-->

    <!--begin:Menu item-->
    <div class="menu-item">
        <!--begin:Menu link-->
        <a class="menu-link <?= uri_string() == 'upgrade' ? 'active' : null ?>" href="<?= base_url('upgrade') ?>">
            <span class="menu-icon">
                <!--begin::Svg Icon | path: icons/duotune/general/gen014.svg-->
                <span class="svg-icon svg-icon-2">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path opacity="0.3" d="M4.05424 15.1982C8.34524 7.76818 13.5782 3.26318 20.9282 2.01418C21.0729 1.98837 21.2216 1.99789 21.3618 2.04193C21.502 2.08597 21.6294 2.16323 21.7333 2.26712C21.8372 2.37101 21.9144 2.49846 21.9585 2.63863C22.0025 2.7788 22.012 2.92754 21.9862 3.07218C20.7372 10.4222 16.2322 15.6552 8.80224 19.9462L4.05424 15.1982ZM3.81924 17.3372L2.63324 20.4482C2.58427 20.5765 2.5735 20.7163 2.6022 20.8507C2.63091 20.9851 2.69788 21.1082 2.79503 21.2054C2.89218 21.3025 3.01536 21.3695 3.14972 21.3982C3.28408 21.4269 3.42387 21.4161 3.55224 21.3672L6.66524 20.1802L3.81924 17.3372ZM16.5002 5.99818C16.2036 5.99818 15.9136 6.08615 15.6669 6.25097C15.4202 6.41579 15.228 6.65006 15.1144 6.92415C15.0009 7.19824 14.9712 7.49984 15.0291 7.79081C15.0869 8.08178 15.2298 8.34906 15.4396 8.55884C15.6494 8.76862 15.9166 8.91148 16.2076 8.96935C16.4986 9.02723 16.8002 8.99753 17.0743 8.884C17.3484 8.77046 17.5826 8.5782 17.7474 8.33153C17.9123 8.08486 18.0002 7.79485 18.0002 7.49818C18.0002 7.10035 17.8422 6.71882 17.5609 6.43752C17.2796 6.15621 16.8981 5.99818 16.5002 5.99818Z" fill="currentColor"></path>
                        <path d="M4.05423 15.1982L2.24723 13.3912C2.15505 13.299 2.08547 13.1867 2.04395 13.0632C2.00243 12.9396 1.9901 12.8081 2.00793 12.679C2.02575 12.5498 2.07325 12.4266 2.14669 12.3189C2.22013 12.2112 2.31752 12.1219 2.43123 12.0582L9.15323 8.28918C7.17353 10.3717 5.4607 12.6926 4.05423 15.1982ZM8.80023 19.9442L10.6072 21.7512C10.6994 21.8434 10.8117 21.9129 10.9352 21.9545C11.0588 21.996 11.1903 22.0083 11.3195 21.9905C11.4486 21.9727 11.5718 21.9252 11.6795 21.8517C11.7872 21.7783 11.8765 21.6809 11.9402 21.5672L15.7092 14.8442C13.6269 16.8245 11.3061 18.5377 8.80023 19.9442ZM7.04023 18.1832L12.5832 12.6402C12.7381 12.4759 12.8228 12.2577 12.8195 12.032C12.8161 11.8063 12.725 11.5907 12.5653 11.4311C12.4057 11.2714 12.1901 11.1803 11.9644 11.1769C11.7387 11.1736 11.5205 11.2583 11.3562 11.4132L5.81323 16.9562L7.04023 18.1832Z" fill="currentColor"></path>
                    </svg>
                </span>
                <!--end::Svg Icon-->
            </span>

            <span class="menu-title">Upgrade Akun</span>
        </a>
        <!--end:Menu link-->
    </div>
    <!--end:Menu item-->
<?php endif; ?>