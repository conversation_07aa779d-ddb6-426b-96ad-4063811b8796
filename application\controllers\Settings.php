<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property ApiKeys $apikeys
 * @property MsProduct $msproduct
 * @property MsUsers $msusers
 * @property ThemeConfiguration $themeconfiguration
 * @property MsProfit $msprofit
 * @property QueueSync $queuesync
 * @property DomainRequestNs $domainrequestns
 * @property CI_Upload $upload
 * @property Datatables $datatables
 * @property CI_DB_mysqli_driver $db
 * @property ApiKeys_WhatsApp $apikeys_whatsapp
 * @property HistoryBalance $historybalance
 * @property MsLicense $mslicense
 * @property MsVendor $msvendor
 */
class Settings extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('ApiKeys', 'apikeys');
        $this->load->model('MsProduct', 'msproduct');
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('ThemeConfiguration', 'themeconfiguration');
        $this->load->model('MsProfit', 'msprofit');
        $this->load->model('QueueSync', 'queuesync');
        $this->load->model('DomainRequestNs', 'domainrequestns');
        $this->load->model('ApiKeys_WhatsApp', 'apikeys_whatsapp');
        $this->load->model('HistoryBalance', 'historybalance');
        $this->load->model('MsVendor', 'msvendor');
    }

    public function account()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Pengaturan Akun';
        $data['content'] = 'settings/account';
        $data['user'] = $this->msusers->get(array('id' => getCurrentIdUser()))->row();

        return $this->load->view('master', $data);
    }

    public function password()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Password';
        $data['content'] = 'settings/password';

        return $this->load->view('master', $data);
    }

    public function apikey()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        if (getCurrentUser()->multivendor != 1) {
            $data = array();
            $data['title'] = 'Server PPOB & SMM - API Key';
            $data['content'] = 'settings/apikey';
            $data['ppob'] = $this->apikeys->get(array(
                'userid' => getCurrentIdUser(),
                'category' => 'PPOB'
            ))->row();
            $data['smm'] = $this->apikeys->get(array(
                'userid' => getCurrentIdUser(),
                'category' => 'SMM'
            ))->row();
            $data['apikey'] = $this->apikeys->get(array(
                'userid' => getCurrentIdUser(),
                "(category = 'PPOB' OR category = 'SMM') =" => true
            ))->row();
        } else {
            $data = array();
            $data['title'] = 'Server PPOB & SMM - API Key';
            $data['content'] = 'manage/vendor/index';
        }

        return $this->load->view('master', $data);
    }

    private function vendor_ppob()
    {
        $listvendor = listVendor('PPOB');
        return array_keys($listvendor);
    }

    public function process_setting_ppob()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $vendor = getPost('vendor');
            $usercode = getPost('usercode');
            $apikey = getPost('apikey');
            $secretvalue = getPost('secretvalue');
            $prioritycallback = getPost('prioritycallback');
            $dontadd_product = getPost('dontadd_product');

            if ($vendor == null) {
                throw new Exception('Vendor wajib diisi');
            } else {
                $vendor = trim($vendor);
            }

            if (!in_array($vendor, $this->vendor_ppob())) {
                throw new Exception('Vendor tidak ditemukan');
            } else if ($usercode == null) {
                if ($vendor == 'Digiflazz') {
                    throw new Exception('Username wajib diisi');
                } else if ($vendor == 'VIPayment') {
                    throw new Exception('API ID wajib diisi');
                }
            } else if ($apikey == null) {
                if ($vendor == 'VIPayment') {
                    throw new Exception('API Key wajib diisi');
                } else if ($vendor == 'Digiflazz') {
                    throw new Exception('Password wajib diisi');
                }
            } else {
                $usercode = removeSymbol($usercode);
                $apikey = removeSymbol($apikey);
            }

            $execute = array();
            $execute['userid'] = getCurrentIdUser();
            $execute['vendor'] = $vendor;
            $execute['usercode'] = stringEncryption('encrypt', $usercode);
            $execute['apikey'] = stringEncryption('encrypt', $apikey);
            $execute['category'] = 'PPOB';
            $execute['dontadd_product'] = $dontadd_product ? 1 : 0;

            if ($vendor == 'Digiflazz') {
                if ($prioritycallback && $secretvalue == null) {
                    throw new Exception('Secret Value wajib diisi');
                }

                if ($secretvalue != null) {
                    $execute['secretvalue'] = stringEncryption('encrypt', $secretvalue);
                    $execute['priority_callback'] = $prioritycallback ? 1 : 0;
                } else {
                    $execute['secretvalue'] = null;
                    $execute['priority_callback'] = 0;
                }
            } else {
                $execute['secretvalue'] = null;
                $execute['priority_callback'] = 0;
            }

            $get = $this->apikeys->get(array(
                'userid' => getCurrentIdUser(),
                'category' => 'PPOB'
            ));

            if ($get->num_rows() == 0) {
                $this->apikeys->insert($execute);
            } else {
                $row = $get->row();

                if ($row->vendor != $vendor) {
                    $execute['balance'] = 0;
                }

                $this->apikeys->update(array(
                    'userid' => getCurrentIdUser(),
                    'category' => 'PPOB'
                ), $execute);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    private function vendor_smm()
    {
        return array(
            'BuzzerPanel',
            'MedanPedia',
            'IrvanKede',
            'DailyPanel',
            'WStore',
            'UNDRCTRL',
            'SosmedOnline',
            'SosmedOnlineVIP',
            'DjuraganSosmed',
            'SMMRaja',
            // 'Snow',
            'SMMIllusion',
            'V1Pedia'
        );
    }

    public function process_setting_smm()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $vendor = getPost('vendor');
            $usercode = getPost('usercode');
            $apikey = getPost('apikey');
            $secretkey = getPost('secretkey');
            $currencyrate = getPost('currencyrate');
            $public_key = getPost('public_key');
            $dontadd_product = getPost('dontadd_product');

            if ($vendor == null) {
                throw new Exception('Vendor wajib diisi');
            } else if (!in_array($vendor, $this->vendor_smm())) {
                throw new Exception('Vendor tidak ditemukan');
            } else if ($usercode == null) {
                if ($vendor == 'MedanPedia' || $vendor == 'IrvanKede' || $vendor == 'WStore' || $vendor == 'SosmedOnline' || $vendor == 'SosmedOnlineVIP' || $vendor == 'V1Pedia') {
                    throw new Exception('User Code wajib diisi');
                }
            } else if ($apikey == null) {
                throw new Exception('API Key wajib diisi');
            } else if ($secretkey == null) {
                if ($vendor == 'BuzzerPanel' || $vendor == 'DailyPanel' || $vendor == 'WStore' | $vendor == 'V1Pedia') {
                    throw new Exception('Secret Key wajib diisi');
                }
            } else if ($currencyrate == null) {
                if ($vendor == 'SMMRaja' || $vendor == 'Snow' || $vendor == 'SMMIllusion') {
                    throw new Exception('Rate wajib diisi');
                }
            } else if ($public_key == null) {
                if ($vendor == 'UNDRCTRL') {
                    throw new Exception('Public Key wajib diisi');
                }
            } else {
                $usercode = removeSymbol($usercode);
                $apikey = removeSymbol($apikey);
                $secretkey = removeSymbol($secretkey);
                $currencyrate = removeSymbol($currencyrate);
            }

            $execute = array();
            $execute['userid'] = getCurrentIdUser();
            $execute['vendor'] = $vendor;

            if ($vendor == 'MedanPedia' || $vendor == 'IrvanKede' || $vendor == 'SosmedOnline' || $vendor == 'SosmedOnlineVIP' || $vendor == 'V1Pedia') {
                $execute['usercode'] = stringEncryption('encrypt', $usercode);
                $execute['secretkey'] = null;
                $execute['currency_rate'] = null;
            } else if ($vendor == 'BuzzerPanel' || $vendor == 'DailyPanel') {
                $execute['usercode'] = null;
                $execute['secretkey'] = stringEncryption('encrypt', $secretkey);
                $execute['currency_rate'] = null;
            } else if ($vendor == 'WStore' || $vendor == 'V1Pedia') {
                $execute['usercode'] = stringEncryption('encrypt', $usercode);
                $execute['secretkey'] = stringEncryption('encrypt', $secretkey);
                $execute['currency_rate'] = null;
            } else if ($vendor == 'UNDRCTRL' || $vendor == 'DjuraganSosmed') {
                $execute['usercode'] = null;
                $execute['secretkey'] = null;
                $execute['currency_rate'] = null;
            } else if ($vendor == 'Snow' || $vendor == 'SMMRaja' || $vendor == 'SMMIllusion') {
                $execute['usercode'] = null;
                $execute['secretkey'] = null;
                $execute['currency_rate'] = $currencyrate;
            }

            if ($vendor == 'UNDRCTRL') {
                $execute['public_key'] = stringEncryption('encrypt', $public_key);
            } else {
                $execute['public_key'] = null;
            }

            $execute['apikey'] = stringEncryption('encrypt', $apikey);
            $execute['category'] = 'SMM';
            $execute['dontadd_product'] = $dontadd_product ? 1 : 0;

            $get = $this->apikeys->get(array(
                'userid' => getCurrentIdUser(),
                'category' => 'SMM'
            ));

            if ($get->num_rows() == 0) {
                $this->apikeys->insert($execute);
            } else {
                $row = $get->row();

                if ($row->vendor != $vendor) {
                    $execute['balance'] = 0;
                }

                $this->apikeys->update(array(
                    'userid' => getCurrentIdUser(),
                    'category' => 'SMM'
                ), $execute);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_change_password()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            }

            $password = getPost('password');
            $newpassword = getPost('newpassword');

            if ($password == null) {
                throw new Exception('Password wajib diisi');
            } else if ($newpassword == null) {
                throw new Exception('Password Baru wajib diisi');
            } else if (!password_strength_check($newpassword)) {
                throw new Exception('Kata sandi minimal memiliki 8 karakter atau lebih dengan campuran huruf, angka & simbol');
            }

            $get = $this->msusers->get(array(
                'id' => getCurrentIdUser()
            ))->row();

            if (!password_verify($password, $get->password)) {
                throw new Exception('Password yang anda masukkan salah');
            }

            $update = array();
            $update['password'] = password_hash($newpassword, PASSWORD_DEFAULT);

            $this->msusers->update(array(
                'id' => getCurrentIdUser()
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah password');
            }

            $this->db->trans_commit();

            destroySession();

            return JSONResponseDefault('OK', 'Password berhasil diubah');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_change_personal_account()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            }

            $name = getPost('name');
            $phonenumber = getPost('phonenumber');

            if ($name == null) {
                throw new Exception('Nama wajib diisi');
            } else if (preg_match('/[^a-zA-Z\s]+/', $name)) {
                throw new Exception('Nama yang anda masukkan tidak valid');
            } else if ($phonenumber == null) {
                throw new Exception('Nomor handphone wajib diisi');
            } else if (!is_numeric($phonenumber)) {
                throw new Exception('Nomor handphone harus berupa angka');
            } else {
                $name = removeSymbol($name);
            }

            $update = array();
            $update['name'] = $name;
            $update['phonenumber'] = $phonenumber;

            $this->msusers->update(array(
                'id' => getCurrentIdUser()
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah data');
            }

            $this->db->trans_commit();

            setSessionValue(array(
                'NAME' => $name
            ));

            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_change_company_account()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $user = getCurrentUser(getCurrentIdUser());

            $name = getPost('name');
            $address = getPost('address');
            $category = getPost('category');
            $avatar_remove = getPost('avatar_remove');
            $limitsaldo = getPost('balancelimit');
            $maintenance = getPost('maintenance');
            $pendaftarantanpaverifikasi = getPost('pendaftarantanpaverifikasi');
            $logo = isset($_FILES['logo']) ? $_FILES['logo'] : null;

            if ($name == null) {
                throw new Exception('Nama wajib diisi');
            } else if (preg_match('/[^a-zA-Z\s]+/', $name)) {
                throw new Exception('Nama yang anda masukkan tidak valid');
            } else if (isUser() && $category != 'PPOB & SMM' && $category != 'PPOB' && $category != 'SMM') {
                throw new Exception('Kategori Usaha tidak ditemukan');
            } else {
                $name = removeSymbol($name);
                $address = removeSymbol($address);
            }

            $update = array();
            if ($avatar_remove) {
                $extract_url = basename($user->companyicon);
                $extension = explode('.', $extract_url);

                if (file_exists('./uploads/' . $extract_url) && isset($extension[1]) && $extension[1] == 'ico') {
                    @unlink('./uploads/' . $extract_url);
                }

                $update['companyicon'] = null;
            }

            if (isset($logo['size']) && $logo['size'] > 0) {
                $config = array();
                $config['allowed_types'] = 'ico|png';
                $config['upload_path'] = './uploads/';
                $config['encrypt_name'] = true;

                $this->load->library('upload', $config);

                if ($this->upload->do_upload('logo')) {
                    $update['companyicon'] = base_url('uploads/' . $this->upload->data('file_name'));
                } else {
                    throw new Exception($this->upload->display_errors('', ''));
                }
            }

            $update['companyname'] = $name;
            $update['balancelimit'] = $limitsaldo;
            $update['ismaintenance'] = $maintenance ? 1 : 0;
            $update['isregisterwithoutverification'] = $pendaftarantanpaverifikasi ? 1 : 0;

            $update['companyaddress'] = $address;

            if (getCurrentUser()->licenseid == 1) {
                $update['companycategory'] = $category;
            } else {
                if (getCurrentUser()->licenseid == 2) {
                    $update['companycategory'] = 'PPOB';
                } else if (getCurrentUser()->licenseid == 3) {
                    $update['companycategory'] = 'SMM';
                }
            }

            $this->msusers->update(array(
                'id' => getCurrentIdUser()
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function domain()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $found_cloudflare = false;
        $valid_cloudflare = false;
        $cloudflare_result = null;
        $currentuser = getCurrentUser(getCurrentIdUser());

        if ($currentuser->domain != null) {
            $ns = dns_get_record($currentuser->domain, DNS_NS);

            if ($ns != false) {
                foreach ($ns as $key => $value) {
                    if (searchCloudflare($value['target'])) {
                        $found_cloudflare = true;
                    }
                }
            }

            $cloudflare = new Cloudflare(String_Helper::CLOUDLFARE_ACCOUNT, String_Helper::CLOUDFLARE_TOKEN);
            $zone = $cloudflare->zones(array('name' => $currentuser->domain));

            if (count($zone->result) > 0) {
                $cloudflare_result = $zone->result;
                $valid_cloudflare = true;
            }
        } else {
            $ns = null;
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Domain';
        $data['content'] = 'settings/domain';
        $data['user'] = $currentuser;
        $data['ns'] = $ns;
        $data['foundcloudflare'] = $found_cloudflare;
        $data['validcloudflare'] = $valid_cloudflare;
        $data['request'] = $this->domainrequestns->total(array('userid' => getCurrentIdUser(), 'status' => 'Pending'));
        $data['cloudflare'] = $cloudflare_result;

        return $this->load->view('master', $data);
    }

    public function process_domain()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $request = $this->domainrequestns->total(array('userid' => getCurrentIdUser(), 'status' => 'Pending'));

            if ($request > 0) {
                throw new Exception('Anda masih memiliki permintaan yang belum selesai');
            }

            $domain = getPost('domain');

            if ($domain == null) {
                throw new Exception('Domain wajib diisi');
            } else {
                $domain = removeSymbol($domain);
            }

            $get_domain = $this->msusers->like('domain', $domain)
                ->total(array(
                    'id !=' => getCurrentIdUser()
                ));

            if ($get_domain > 0) {
                throw new Exception('Domain sudah digunakan');
            }

            $update = array();
            $update['domain'] = $domain;

            $this->msusers->update(array(
                'id' => getCurrentIdUser()
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function theme()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Tema';
        $data['content'] = 'settings/theme';

        return $this->load->view('master', $data);
    }

    private function theme_name()
    {
        return array(
            'Fin-App',
            'Able'
        );
    }

    public function process_change_theme()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $themename = getPost('themename');

            if (!in_array($themename, $this->theme_name())) {
                throw new Exception('Tema tidak ditemukan');
            }

            $this->themeconfiguration->update(array(
                'userid' => getCurrentIdUser()
            ), array(
                'isused' => null
            ));

            $get = $this->themeconfiguration->get(array(
                'userid' => getCurrentIdUser(),
                'themename' => $themename
            ));

            if ($get->num_rows() == 0) {
                $insert = array();
                $insert['userid'] = getCurrentIdUser();
                $insert['themename'] = $themename;
                $insert['isused'] = 1;

                $this->themeconfiguration->insert($insert);
            } else {
                $themeRow = $get->row();

                $update = array();
                $update['isused'] = 1;

                $this->themeconfiguration->update(array(
                    'id' => $themeRow->id
                ), $update);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah tema');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Tema berhasil digunakan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function configuration_theme()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $themename = getPost('themename');

            if (!in_array($themename, $this->theme_name())) {
                throw new Exception('Tema tidak ditemukan');
            }

            $theme = $this->themeconfiguration->get(array(
                'userid' => getCurrentIdUser(),
                'themename' => $themename
            ))->row();

            $data = array();
            $data['id'] = $theme != null ? stringEncryption('encrypt', $theme->id) : null;
            $data['config'] = $theme != null ? json_decode($theme->themeconfig ?? '') : null;

            if ($themename == 'Fin-App') {
                $content = $this->load->view('settings/theme/finapp/configuration', $data, true);
            } else if ($themename == 'Able') {
                $content = $this->load->view('settings/theme/able/configuration', $data, true);
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $content
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }



    public function process_configuration_theme()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');
            $company = getPost('company');
            $aboutus = getPost('aboutus');
            $privacypolicy = getPost('privacypolicy');

            $themename = null;
            if ($id == null) {
                throw new Exception('ID tema tidak ditemukan');
            } else {
                $id = stringEncryption('decrypt', $id);

                $get = $this->themeconfiguration->get(array(
                    'userid' => getCurrentIdUser(),
                    'id' => $id
                ));

                if ($get->num_rows() == 0) {
                    throw new Exception('Tema tidak ditemukan');
                }

                $row = $get->row();
                $themename = $row->themename;
            }

            // Validation for Fin-App theme
            if ($themename == 'Fin-App') {
                if ($company == null) {
                    throw new Exception('Nama Usaha wajib diisi');
                } else if ($aboutus == null) {
                    throw new Exception('Tentang Kami tidak boleh kosong');
                } else if ($privacypolicy == null) {
                    throw new Exception('Kebijakan Privasi tidak boleh kosong');
                } else {
                    $company = removeSymbol($company);
                }
            }
            // Validation for Able theme
            else if ($themename == 'Able') {
                if ($company == null) {
                    throw new Exception('Nama Usaha wajib diisi');
                } else {
                    $company = removeSymbol($company);
                }
            }

            $theme = $this->themeconfiguration->get(array(
                'userid' => getCurrentIdUser(),
                'themename' => $themename
            ))->row();

            $execute = array();
            $execute['userid'] = getCurrentIdUser();
            $execute['themename'] = $themename;

            if ($themename == 'Fin-App') {
                $execute['themeconfig'] = json_encode(array(
                    'company' => $company,
                    'aboutus' => $aboutus,
                    'privacypolicy' => $privacypolicy
                ));
            } else if ($themename == 'Able') {
                $execute['themeconfig'] = json_encode(array(
                    'company' => $company,
                ));
            }

            if ($theme == null) {
                $this->themeconfiguration->insert($execute);
            } else {
                $this->themeconfiguration->update(array(
                    'id' => $theme->id
                ), $execute);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah konfigurasi tema');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Konfigurasi berhasil disimpan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function margin()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Margin/Profit';
        $data['content'] = 'settings/margin/index';

        return $this->load->view('master', $data);
    }

    public function datatables_margin()
    {
        try {
            if (isLogin() && isUser() && getCurrentUser()->licenseid != null) {
                $data = array();

                $datatable = $this->datatables->make('MsProfit', 'QueryDatatables', 'SearchDatatables');

                $where = array(
                    'userid' => getCurrentIdUser()
                );

                if ($this->user->companycategory == 'PPOB') {
                    $where["(category = 'Prabayar' OR category = 'Pascabayar') ="] = true;
                } else if ($this->user->companycategory == 'SMM') {
                    $where['category'] = 'Media Sosial';
                }

                foreach ($datatable->getData($where) as $key => $value) {
                    $detail = array();

                    if ($value->margintype != 'Persentase') {
                        $profit = IDR($value->profit);
                    } else {
                        $profit = $value->profit . "%";
                    }

                    $detail[] = $value->category;
                    $detail[] = IDR($value->minprice);
                    $detail[] = IDR($value->maxprice);
                    $detail[] = "Berdasarkan " . $value->margintype ?? 'Nominal';
                    $detail[] = $profit;
                    $detail[] = "<a href=\"javascript:;\" class=\"btn btn-icon btn-warning btn-sm\" onclick=\"editMargin('" . stringEncryption('encrypt', $value->id) . "')\">
                                <i class=\"fa fa-edit\"></i>
                            </a>

                            <a href=\"javascript:;\" class=\"btn btn-icon btn-danger btn-sm\" onclick=\"deleteMargin('" . stringEncryption('encrypt', $value->id) . "')\">
                                <i class=\"fa fa-trash\"></i>
                            </a>";

                    $data[] = $detail;
                }

                return $datatable->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function add_margin()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('settings/margin/add', array(), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_add_margin()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $category = getPost('category');
            $minprice = getPost('minprice', 0);
            $maxprice = getPost('maxprice', 0);
            $margintype = getPost('margintype');
            $profit = getPost('profit', 0);

            if ($category == null) {
                throw new Exception('Nama pembayaran wajib diisi');
            } else if ($category != 'Prabayar' && $category != 'Pascabayar' && $category != 'Media Sosial') {
                throw new Exception('Kategori tidak ditemukan');
            } else if ($margintype != 'Nominal' && $margintype != 'Persentase') {
                throw new Exception('Tipe Margin/Profit tidak ditemukan');
            } else if (!is_numeric($minprice)) {
                throw new Exception('Minimal Harga harus berupa angka');
            } else if (!is_numeric($maxprice)) {
                throw new Exception('Maksimal Harga harus berupa angka');
            } else if ($profit == null) {
                throw new Exception('Profit wajib diisi');
            } else if (!is_numeric($profit)) {
                throw new Exception('Profit harus berupa angka');
            }

            if (($category == 'Prabayar' || $category == 'Pascabayar') && ($this->user->companycategory != 'PPOB & SMM' && $this->user->companycategory != 'PPOB')) {
                throw new Exception('Kategori ini tidak dapat digunakan');
            } else if ($category == 'Media Sosial' && ($this->user->companycategory != 'PPOB & SMM' && $this->user->companycategory != 'SMM')) {
                throw new Exception('Kategori ini tidak dapat digunakan');
            }

            if ($margintype == 'Persentase') {
                if ($profit > 100) {
                    throw new Exception('Jumlah profit tidak boleh lebih dari 100%');
                }
            }

            $insert = array();
            $insert['userid'] = getCurrentIdUser();
            $insert['category'] = $category;
            $insert['minprice'] = $minprice;
            $insert['maxprice'] = $maxprice;
            $insert['margintype'] = $margintype;
            $insert['profit'] = $profit;

            $this->msprofit->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menambahkan data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil ditambahkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function edit_margin()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->msprofit->get(array(
                'userid' => getCurrentIdUser(),
                'id' => $id
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('settings/margin/edit', array(
                    'margin' => $row
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_edit_margin()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = stringEncryption('decrypt', getPost('id'));
            $category = getPost('category');
            $minprice = getPost('minprice', 0);
            $maxprice = getPost('maxprice', 0);
            $margintype = getPost('margintype');
            $profit = getPost('profit', 0);

            if ($category == null) {
                throw new Exception('Nama pembayaran wajib diisi');
            } else if ($category != 'Prabayar' && $category != 'Pascabayar' && $category != 'Media Sosial') {
                throw new Exception('Kategori tidak ditemukan');
            } else if ($margintype != 'Nominal' && $margintype != 'Persentase') {
                throw new Exception('Tipe Margin/Profit tidak ditemukan');
            } else if (!is_numeric($minprice)) {
                throw new Exception('Minimal Harga harus berupa angka');
            } else if (!is_numeric($maxprice)) {
                throw new Exception('Maksimal Harga harus berupa angka');
            } else if ($profit == null) {
                throw new Exception('Profit wajib diisi');
            } else if (!is_numeric($profit)) {
                throw new Exception('Profit harus berupa angka');
            }

            if (($category == 'Prabayar' || $category == 'Pascabayar') && ($this->user->companycategory != 'PPOB & SMM' && $this->user->companycategory != 'PPOB')) {
                throw new Exception('Kategori ini tidak dapat digunakan');
            } else if ($category == 'Media Sosial' && ($this->user->companycategory != 'PPOB & SMM' && $this->user->companycategory != 'SMM')) {
                throw new Exception('Kategori ini tidak dapat digunakan');
            }

            if ($margintype == 'Persentase') {
                if ($profit > 100) {
                    throw new Exception('Jumlah profit tidak boleh lebih dari 100%');
                }
            }

            if (getCurrentUser(getCurrentIdUser())->licenseid == null) {
                if ($margintype == 'Persentase') {
                    throw new Exception('Anda tidak dapat memilih tipe margin ini');
                }
            }

            $get = $this->msprofit->total(array(
                'userid' => getCurrentIdUser(),
                'id' => $id
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $update = array();
            $update['category'] = $category;
            $update['minprice'] = $minprice;
            $update['maxprice'] = $maxprice;
            $update['margintype'] = $margintype;
            $update['profit'] = $profit;

            $this->msprofit->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete_margin()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->msprofit->total(array(
                'userid' => getCurrentIdUser(),
                'id' => $id
            ));

            if ($get == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $this->msprofit->delete(array(
                'id' => $id
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menghapus data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil dihapus');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function smtp()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - SMTP Email';
        $data['content'] = 'settings/smtpemail';
        $data['smtp'] = json_decode(stringEncryption('decrypt', getCurrentUser(getCurrentIdUser())->smtpemailconfig));

        return $this->load->view('master', $data);
    }

    public function process_smtp()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $host = removeSymbol(getPost('host'));
            $username = removeSymbol(getPost('username'));
            $password = removeSymbol(getPost('password'));
            $port = getPost('port');

            $config = array(
                'host' => $host,
                'username' => $username,
                'password' => $password,
                'port' => $port
            );

            $update = array();
            $update['smtpemailconfig'] = stringEncryption('encrypt', json_encode($config));

            $this->msusers->update(array(
                'id' => getCurrentIdUser(),
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil disimpan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function logmessage()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Log Error';
        $data['content'] = 'settings/logmessage';

        return $this->load->view('master', $data);
    }

    public function datatables_logmessage()
    {
        try {
            if (isLogin() && isUser() && getCurrentUser()->licenseid != null) {
                $data = array();

                $datatables = $this->datatables->make('ErrorLogger', 'QueryDatatables', 'SearchDatatables');

                foreach ($datatables->getData(array('userid' => getCurrentIdUser())) as $key => $value) {
                    $detail = array();

                    $detail[] = "<span class=\"badge badge-danger\">$value->type</span>";
                    $detail[] = $value->message;
                    $detail[] = DateFormat($value->createddate, 'd F Y H:i:s');

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function request_domain()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $user = getCurrentUser(getCurrentIdUser());

            if ($user->domain == null) {
                throw new Exception('Anda belum mengisi domain');
            }

            $get = $this->domainrequestns->total(array(
                'userid' => $user->id,
                'status' => 'Pending'
            ));

            if ($get > 0) {
                throw new Exception('Anda sudah melakukan permintaan domain');
            }

            $insert = array();
            $insert['userid'] = $user->id;
            $insert['domain'] = $user->domain;
            $insert['status'] = 'Pending';
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();

            $this->domainrequestns->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengirim permintaan domain');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Permintaan anda sedang diproses');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function seo()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - SEO';
        $data['content'] = 'settings/seo';
        $data['seo'] = json_decode(stringEncryption('decrypt', getCurrentUser(getCurrentIdUser())->seostructure));

        return $this->load->view('master', $data);
    }

    public function process_seo()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $description = getPost('description');
            $keywords = getPost('keywords');
            $schemaorg = getPost('schemaorg');

            if ($description == null) {
                throw new Exception('Deskripsi tidak boleh kosong');
            } else if ($keywords == null) {
                throw new Exception('Kata kunci tidak boleh kosong');
            } else {
                $description = sanitize_meta_description($description);
                $keywords = json_decode($keywords);

                $keyword = array();
                foreach ($keywords as $key => $value) {
                    $keyword[] = $value->value;
                }

                $keywords = implode(',', $keyword);

                if ($schemaorg != null) {
                    $schemaorg = json_decode($schemaorg, true);

                    if (validate_schema($schemaorg) == false) {
                        throw new Exception('Schema tidak valid');
                    }
                }
            }

            $seostructure = array(
                'description' => $description,
                'keywords' => $keywords,
                'schemaorg' => $schemaorg
            );

            $update = array();
            $update['seostructure'] = stringEncryption('encrypt', json_encode($seostructure));

            $this->msusers->update(array(
                'id' => getCurrentIdUser(),
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil disimpan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function struk()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Struk';
        $data['content'] = 'settings/struk';
        $data['strukcontent'] = getCurrentUser(getCurrentIdUser())->strukcontent;

        return $this->load->view('master', $data);
    }

    public function process_struk()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $data = getPost('data', array());

            usort($data, function ($a, $b) {
                return $a['order'] <=> $b['order'];
            });

            $struk_content = array();
            foreach ($data as $key => $value) {
                if ($value['checked'] == 'true' && array_key_exists($value['id'], strukContent())) {
                    $struk_content[] = $value['id'];
                }
            }

            $update = array();
            $update['strukcontent'] = json_encode($struk_content);

            $this->msusers->update(array(
                'id' => getCurrentIdUser(),
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil disimpan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function whatsapp()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $currentuser = getCurrentUser(getCurrentIdUser());
        $whatsapp_notification = $currentuser->whatsappnotification != null ? json_decode($currentuser->whatsappnotification) : null;

        $apikeys_whatsapp = $this->apikeys_whatsapp->get(array(
            'userid' => getCurrentIdUser()
        ))->row();

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Whatsapp';
        $data['content'] = 'settings/whatsapp';
        $data['whatsapp_notification'] = $whatsapp_notification;
        $data['apikeys_whatsapp'] = $apikeys_whatsapp;

        return $this->load->view('master', $data);
    }

    public function process_whatsapp()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $success_notification = getPost('success_notification');
            $pending_notification = getPost('pending_notification');
            $failed_notification = getPost('failed_notification');

            $currentuser = getCurrentUser();

            $whatsapp_notification = array();
            if ($currentuser->whatsappnotification != null) {
                $whatsapp_notification = json_decode($currentuser->whatsappnotification, true);
            }

            $data = array();
            $data['success'] = $success_notification;
            $data['pending'] = $pending_notification;
            $data['failed'] = $failed_notification;
            $data['deposit_success'] = $whatsapp_notification['deposit_success'] ?? null;
            $data['deposit_pending'] = $whatsapp_notification['deposit_pending'] ?? null;
            $data['deposit_failed'] = $whatsapp_notification['deposit_failed'] ?? null;

            $update = array();
            $update['whatsappnotification'] = json_encode($data);

            $this->msusers->update(array(
                'id' => getCurrentIdUser()
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menyimpan pengaturan');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menyimpan pengaturan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_whatsapp_deposit()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $success_notification = getPost('success_notification');
            $pending_notification = getPost('pending_notification');
            $failed_notification = getPost('failed_notification');

            $currentuser = getCurrentUser();

            $whatsapp_notification = array();
            if ($currentuser->whatsappnotification != null) {
                $whatsapp_notification = json_decode($currentuser->whatsappnotification, true);
            }

            $data = array();
            $data['success'] = $whatsapp_notification['success'] ?? null;
            $data['pending'] = $whatsapp_notification['pending'] ?? null;
            $data['failed'] = $whatsapp_notification['failed'] ?? null;
            $data['deposit_success'] = $success_notification;
            $data['deposit_pending'] = $pending_notification;
            $data['deposit_failed'] = $failed_notification;

            $update = array();
            $update['whatsappnotification'] = json_encode($data);

            $this->msusers->update(array(
                'id' => getCurrentIdUser()
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menyimpan pengaturan');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menyimpan pengaturan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function configuration_whatsapp()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $vendor = getPost('vendor');
            $apikey = getPost('apikey');
            $devicecode = getPost('devicecode');

            if ($vendor == null) {
                throw new Exception('Vendor tidak boleh kosong');
            } else if ($vendor != 'WABlasinGateway') {
                throw new Exception('Vendor tidak valid');
            } else if ($apikey == null) {
                throw new Exception('API Key tidak boleh kosong');
            } else if ($devicecode == null) {
                throw new Exception('Device Code tidak boleh kosong');
            }

            $execute = array();
            $execute['userid'] = getCurrentIdUser();
            $execute['vendor'] = $vendor;
            $execute['apikey'] = stringEncryption('encrypt', $apikey);
            $execute['devicecode'] = stringEncryption('encrypt', $devicecode);

            $get = $this->apikeys_whatsapp->get(array(
                'userid' => getCurrentIdUser(),
            ));

            if ($get->num_rows() == 0) {
                $this->apikeys_whatsapp->insert($execute);
            } else {
                $row = $get->row();

                $this->apikeys_whatsapp->update(array(
                    'id' => $row->id
                ), $execute);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menyimpan pengaturan');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menyimpan pengaturan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function set_multivendor()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->multivendor == 1) {
                throw new Exception('Fitur Multi Vendor sudah aktif');
            }

            $update = array();
            $update['multivendor'] = 1;
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $this->msusers->update(array(
                'id' => getCurrentIdUser()
            ), $update);

            $this->apikeys->delete(array(
                'userid' => getCurrentIdUser()
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengaktifkan fitur Multi Vendor');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil mengaktifkan fitur Multi Vendor');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function set_singlevendor()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->multivendor == 0) {
                throw new Exception('Fitur Multi Vendor sudah dinonaktifkan');
            }

            $update = array();
            $update['multivendor'] = 0;
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $this->msusers->update(array(
                'id' => getCurrentIdUser()
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menonaktifkan fitur Multi Vendor');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menonaktifkan fitur Multi Vendor');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function configuration_apikey()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->multivendor != 1) {
                throw new Exception('Fitur Multi Vendor sudah dinonaktifkan');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);

            $where = array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            );

            $get = $this->msvendor->get($where);

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('settings/multivendor/apikey', array(
                    'vendor' => $row,
                    'default_config' => json_decode($row->default_config ?? '[]'),
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_configuration_apikey()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->multivendor != 1) {
                throw new Exception('Fitur Multi Vendor sudah dinonaktifkan');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);

            $where = array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            );

            $get = $this->msvendor->get($where);

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            $default_config = array();
            foreach (json_decode($row->parameter) as $key => $value) {
                $default_config[$value] = getPost($value);
            }

            $update = array();
            $update['default_config'] = json_encode($default_config);
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $this->msvendor->update($where, $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menyimpan konfigurasi');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menyimpan konfigurasi');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_status_apikey()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->multivendor != 1) {
                throw new Exception('Fitur Multi Vendor sudah dinonaktifkan');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);

            $where = array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            );

            $get = $this->msvendor->get($where);

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            $isactive = $row->isactive == 1 ? 0 : 1;

            $update = array();
            $update['isactive'] = $isactive;
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $this->msvendor->update($where, $update);

            $update = array();
            $update['vendorenabled'] = $isactive;

            $this->msproduct->update(array(
                'vendorid' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah status');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil mengubah status');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_unique_nominal()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda belum memiliki lisensi, silahkan beli lisensi terlebih dahulu');
            }

            $startvalue = getPost('startvalue', 0);
            $endvalue = getPost('endvalue', 0);

            $this->form_validation->set_rules('startvalue', 'Start Value', array(
                'required',
                'numeric'
            ));

            $this->form_validation->set_rules('endvalue', 'End Value', array(
                'required',
                'numeric'
            ));

            if ($this->form_validation->run() === FALSE) {
                throw new Exception(strip_tags(validation_errors()));
            }

            $update = array();
            $update['uniquenominal_start'] = $startvalue;
            $update['uniquenominal_end'] = $endvalue;
            $update['updateddate'] = getCurrentDate();

            $this->msusers->update(array(
                'id' => getCurrentIdUser()
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function firebase()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $currentuser = getCurrentUser(getCurrentIdUser());
        $firebase_notification = $currentuser->firebasenotification != null ? json_decode($currentuser->firebasenotification) : null;

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Firebase';
        $data['content'] = 'settings/firebase';
        $data['firebase_notification'] = $firebase_notification;

        return $this->load->view('master', $data);
    }

    public function process_firebase_transaction()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $transaction_success = getPost('transaction_success');
            $transaction_pending = getPost('transaction_pending');
            $transaction_failed = getPost('transaction_failed');

            $currentuser = getCurrentUser();

            $firebase_notification = array();
            if ($currentuser->firebasenotification != null) {
                $firebase_notification = json_decode($currentuser->firebasenotification, true);
            }

            $data = array();
            $data['transaction_success'] = $transaction_success;
            $data['transaction_pending'] = $transaction_pending;
            $data['transaction_failed'] = $transaction_failed;
            $data['deposit_success'] = $firebase_notification['deposit_success'] ?? null;
            $data['deposit_pending'] = $firebase_notification['deposit_pending'] ?? null;
            $data['deposit_failed'] = $firebase_notification['deposit_failed'] ?? null;

            $update = array();
            $update['firebasenotification'] = json_encode($data);

            $this->msusers->update(array(
                'id' => getCurrentIdUser()
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menyimpan format notifikasi transaksi');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menyimpan format notifikasi transaksi');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_firebase_deposit()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $deposit_success = getPost('deposit_success');
            $deposit_pending = getPost('deposit_pending');
            $deposit_failed = getPost('deposit_failed');

            $currentuser = getCurrentUser();

            $firebase_notification = array();
            if ($currentuser->firebasenotification != null) {
                $firebase_notification = json_decode($currentuser->firebasenotification, true);
            }

            $data = array();
            $data['transaction_success'] = $firebase_notification['transaction_success'] ?? null;
            $data['transaction_pending'] = $firebase_notification['transaction_pending'] ?? null;
            $data['transaction_failed'] = $firebase_notification['transaction_failed'] ?? null;
            $data['deposit_success'] = $deposit_success;
            $data['deposit_pending'] = $deposit_pending;
            $data['deposit_failed'] = $deposit_failed;

            $update = array();
            $update['firebasenotification'] = json_encode($data);

            $this->msusers->update(array(
                'id' => getCurrentIdUser()
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menyimpan format notifikasi deposit');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil menyimpan format notifikasi deposit');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
