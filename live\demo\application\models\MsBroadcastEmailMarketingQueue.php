<?php
defined('BASEPATH') or die('No direct script access allowed!');

class MsBroadcastEmailMarketingQueue extends MY_Model
{
    protected $table = 'msbroadcastemailmarketingqueue';
    public $SearchDatatables = array('recipient_email');

    public function QueryDatatables()
    {
        $this->db->select('a.*, b.email as recipient_email, b.name as recipient_name')
            ->from($this->table . ' a')
            ->join('msusers b', 'a.recipientid = b.id', 'LEFT')
            ->order_by('a.createddate', 'DESC');

        return $this;
    }
}
