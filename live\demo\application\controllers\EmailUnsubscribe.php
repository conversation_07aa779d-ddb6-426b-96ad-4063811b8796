<?php
defined('BASEPATH') or exit('No direct script access allowed');

class EmailUnsubscribe extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('MsEmailUnsubscribe', 'emailunsubscribe');
        $this->load->model('MsUsers', 'msusers');
    }

    /**
     * Unsubscribe page
     */
    public function index()
    {
        $token = $this->input->get('token');

        if (!$token) {
            show_404();
            return;
        }

        // Verify token
        $token_data = $this->verifyUnsubscribeToken($token);

        if (!$token_data) {
            $data = array();
            $data['title'] = 'Link Tidak Valid';
            $data['content'] = 'email_unsubscribe/invalid';
            return $this->load->view('email_unsubscribe/layout', $data);
        }

        // Get user and merchant data
        $user = $this->msusers->get(array('id' => $token_data['userid']))->row();
        $merchant = $this->msusers->get(array('id' => $token_data['merchantid']))->row();

        if (!$user || !$merchant) {
            $data = array();
            $data['title'] = 'Data Tidak Ditemukan';
            $data['content'] = 'email_unsubscribe/invalid';
            return $this->load->view('email_unsubscribe/layout', $data);
        }

        // Check if already unsubscribed
        $already_unsubscribed = $this->isUnsubscribed($token_data['userid'], $token_data['merchantid']);

        $data = array();
        $data['title'] = 'Berhenti Berlangganan Email';
        $data['content'] = 'email_unsubscribe/form';
        $data['user'] = $user;
        $data['merchant'] = $merchant;
        $data['token'] = $token;
        $data['already_unsubscribed'] = $already_unsubscribed;

        return $this->load->view('email_unsubscribe/layout', $data);
    }

    /**
     * Process unsubscribe
     */
    public function process()
    {
        try {
            $token = $this->input->post('token');
            $reason = $this->input->post('reason');

            if (!$token) {
                throw new Exception('Token tidak valid');
            }

            // Verify token
            $token_data = $this->verifyUnsubscribeToken($token);

            if (!$token_data) {
                throw new Exception('Token tidak valid atau sudah kadaluarsa');
            }

            // Get user data
            $user = $this->msusers->get(array('id' => $token_data['userid']))->row();

            if (!$user) {
                throw new Exception('Data user tidak ditemukan');
            }

            // Check if already unsubscribed
            if ($this->isUnsubscribed($token_data['userid'], $token_data['merchantid'])) {
                throw new Exception('Anda sudah berhenti berlangganan sebelumnya');
            }

            $this->db->trans_begin();

            // Insert unsubscribe record
            $insert = array(
                'userid' => $token_data['userid'],
                'merchantid' => $token_data['merchantid'],
                'email' => $user->email,
                'reason' => $reason,
                'unsubscribe_token' => $token,
                'createddate' => getCurrentDate(),
                'createdby' => $token_data['userid']
            );

            // Add broadcast_id if available in token
            if (isset($token_data['broadcast_id'])) {
                $insert['broadcast_id'] = $token_data['broadcast_id'];
            }

            $this->emailunsubscribe->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal memproses permintaan unsubscribe');
            }

            $this->db->trans_commit();

            // Redirect to success page
            redirect(base_url('email-unsubscribe/success?userid=' . $this->userid));
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            $this->session->set_flashdata('error', $ex->getMessage());
            redirect(base_url('email-unsubscribe?token=' . urlencode($token)) . '&userid=' . $this->userid);
        }
    }

    /**
     * Success page
     */
    public function success()
    {
        $data = array();
        $data['title'] = 'Berhasil Berhenti Berlangganan';
        $data['content'] = 'email_unsubscribe/success';

        return $this->load->view('email_unsubscribe/layout', $data);
    }

    /**
     * Check if user has unsubscribed from merchant's emails
     */
    private function isUnsubscribed($userid, $merchantid)
    {
        $result = $this->emailunsubscribe->get(array(
            'userid' => $userid,
            'merchantid' => $merchantid
        ));

        return $result->num_rows() > 0;
    }

    /**
     * Verify unsubscribe token
     */
    private function verifyUnsubscribeToken($token)
    {
        try {
            $decrypted = stringEncryption('decrypt', $token);
            $data = json_decode($decrypted, true);

            if (!$data || !isset($data['userid']) || !isset($data['merchantid'])) {
                return false;
            }

            // Token valid for 30 days
            if (isset($data['timestamp']) && (time() - $data['timestamp']) > (30 * 24 * 60 * 60)) {
                return false;
            }

            return $data;
        } catch (Exception $e) {
            return false;
        }
    }
}
