<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property MsPaymentGateway $paymentgateway
 * @property Deposits $deposits
 * @property MsUsers $msusers
 * @property TrOrder $trorder
 * @property HistoryBalance $historybalance
 * @property CI_DB_mysqli_driver $db
 * @property CI_Input $input
 * @property ApiKeys_WhatsApp $apikeys_whatsapp
 */
class Callback extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsPaymentGateway', 'paymentgateway');
        $this->load->model('Deposits', 'deposits');
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('TrOrder', 'trorder');
        $this->load->model('ApiKeys_WhatsApp', 'apikeys_whatsapp');
        $this->load->model('HistoryBalance', 'historybalance');
    }

    public function tripay()
    {
        try {
            $this->db->trans_begin();

            $json = file_get_contents('php://input');

            $callbackSignature = isset($_SERVER['HTTP_X_CALLBACK_SIGNATURE']) ? $_SERVER['HTTP_X_CALLBACK_SIGNATURE'] : '';

            $tripay = $this->paymentgateway->get(array(
                'userid' => $this->merchant->id,
                'type' => 'Payment Gateway',
                'vendor' => 'Tripay'
            ));

            if ($tripay->num_rows() > 0) {
                $row = $tripay->row();

                $detail = stringEncryption('decrypt', $row->detail);

                if ($detail != null) {
                    $detail = json_decode($detail);

                    $privateKey = $detail->privatekey;
                    $signature = hash_hmac('sha256', $json, $privateKey);

                    if ($signature != $callbackSignature) {
                        throw new Exception('Invalid Signature');
                    }

                    $data = json_decode($json);

                    if (JSON_ERROR_NONE !== json_last_error()) {
                        throw new Exception('Invalid JSON');
                    }

                    if ($_SERVER['HTTP_X_CALLBACK_EVENT'] != 'payment_status') {
                        throw new Exception('Invalid Event');
                    }

                    if ($data->is_closed_payment == 1) {
                        $get = $this->deposits->select('a.*')
                            ->join('msusers b', 'b.id = a.userid')
                            ->get(array(
                                'a.merchantid' => $this->merchant->id,
                                'a.code' => $data->merchant_ref,
                                'a.servercode' => $data->reference,
                                'a.status' => 'Pending'
                            ));

                        if ($get->num_rows() == 0) {
                            $get = $this->trorder->get(array(
                                'merchantid_order' => $this->merchant->id,
                                'clientcode' => $data->merchant_ref,
                                'servercode_payment' => $data->reference,
                                'status_payment' => 'pending',
                                'gatewayvendor' => 'Tripay'
                            ));

                            if ($get->num_rows() == 0) {
                                throw new Exception('Order not found');
                            } else {
                                $order = $get->row();

                                if ($data->status == 'PAID') {
                                    $update = array();
                                    $update['status_payment'] = 'sukses';
                                    $update['updateddate'] = getCurrentDate();

                                    $this->trorder->update(array(
                                        'id' => $order->id
                                    ), $update);

                                    pullTransaction($order->userid ?? $order->clientip);

                                    if ($this->db->trans_status() === FALSE) {
                                        throw new Exception('Payment Failed');
                                    }

                                    $this->db->trans_commit();

                                    return JSONResponse(array(
                                        'success' => true,
                                        'message' => 'Payment Success'
                                    ));
                                } else {
                                    $this->trorder->update(array(
                                        'id' => $order->id
                                    ), array(
                                        'status_payment' => strtolower($data->status),
                                        'updateddate' => getCurrentDate()
                                    ));

                                    pullTransaction($order->userid ?? $order->clientip);

                                    if ($this->db->trans_status() === FALSE) {
                                        throw new Exception('Payment Failed');
                                    }

                                    $this->db->trans_commit();

                                    return JSONResponse(array(
                                        'success' => false,
                                        'message' => 'Payment Failed'
                                    ));
                                }
                            }
                        } else {
                            $deposit = $get->row();

                            if ($data->status == 'PAID') {
                                $currentbalance = getCurrentBalance($deposit->userid, true);

                                $update = array();
                                $update['status'] = 'Success';

                                $this->deposits->update(array(
                                    'id' => $deposit->id
                                ), $update);

                                $this->send_notification($deposit->id, $this->merchant->id, $deposit->phonenumber, 'deposit');

                                $check_history_balance = $this->historybalance->total(array(
                                    'depositid' => $deposit->id,
                                    'type' => 'IN',
                                    'userid' => $deposit->userid
                                ));

                                if ($check_history_balance == 0) {
                                    $balance = null;
                                    if ($deposit->isbonus == 1) {
                                        $balance = $deposit->nominal + $deposit->nominalbonus - ($deposit->fee ?? 0);
                                    } else {
                                        $balance = $deposit->nominal - ($deposit->fee ?? 0);
                                    }

                                    $inserthistorybalance = array();
                                    $inserthistorybalance['userid'] = $deposit->userid;
                                    $inserthistorybalance['type'] = 'IN';
                                    $inserthistorybalance['nominal'] = $balance;
                                    $inserthistorybalance['currentbalance'] = $currentbalance;
                                    $inserthistorybalance['depositid'] = $deposit->id;
                                    $inserthistorybalance['createdby'] = $deposit->userid;
                                    $inserthistorybalance['createddate'] = getCurrentDate();

                                    $this->historybalance->insert($inserthistorybalance);

                                    $balance = $currentbalance + $balance;

                                    $updateUser = array();
                                    $updateUser['balance'] = $balance;

                                    $this->msusers->update(array(
                                        'id' => $deposit->userid
                                    ), $updateUser);
                                }

                                if ($this->db->trans_status() === FALSE) {
                                    throw new Exception('Payment Failed');
                                }

                                $this->db->trans_commit();

                                return JSONResponse(array(
                                    'success' => true,
                                    'message' => 'Payment Success'
                                ));
                            } else {
                                $this->deposits->update(array(
                                    'id' => $deposit->id
                                ), array(
                                    'status' => ucfirst(strtolower($data->status))
                                ));

                                $this->send_notification($deposit->id, $this->merchant->id, $deposit->phonenumber, 'deposit');

                                if ($this->db->trans_status() === FALSE) {
                                    throw new Exception('Payment Failed');
                                }

                                $this->db->trans_commit();

                                return JSONResponse(array(
                                    'success' => false,
                                    'message' => 'Payment Failed'
                                ));
                            }
                        }
                    }
                } else {
                    throw new Exception('Invalid API Key');
                }
            } else {
                throw new Exception('Payment Gateway not found');
            }
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponse(array(
                'success' => false,
                'message' => $ex->getMessage()
            ));
        }
    }

    private function send_notification($orderid, $userid, $phonenumber, $type = 'order')
    {
        // Kirim Firebase notification untuk order
        if ($type == 'order') {
            sendFirebaseNotificationOrder($orderid, $userid);
        }

        $apikeys_whatsapp = $this->apikeys_whatsapp->get(array(
            'userid' => $userid,
        ));

        if ($apikeys_whatsapp->num_rows() == 0) {
            return false;
        }

        $row = $apikeys_whatsapp->row();

        $wablasingateway = new WABlasinGateway(stringEncryption('decrypt', $row->apikey), stringEncryption('decrypt', $row->devicecode));

        if ($type == 'order') {
            $messagenotification = replaceParameterNotification($orderid, $userid);
        } else {
            $messagenotification = replaceParameterNotificationDeposit($orderid, $userid);
        }

        if ($messagenotification != null && $phonenumber != null) {
            $phonenumber = changePrefixPhone($phonenumber);

            $send = $wablasingateway->sendMessage($phonenumber, $messagenotification);

            if (!isset($send->RESULT) || (isset($send->RESULT) && $send->RESULT != 'OK')) {
                log_message_user('error', '[WHATSAPP SEND MESSAGE] Send notification failed: ' . json_encode($send), $userid);
            }

            return true;
        }

        return false;
    }

    public function digiflazz()
    {
        try {
            $this->db->trans_begin();

            if ($this->merchant->multivendor == 1) {
                throw new Exception('Invalid Merchant');
            }

            $currentapikey = getCurrentAPIKeys('PPOB', $this->merchant->id);

            if ($currentapikey == null) {
                throw new Exception('API Key not found');
            }

            if ($currentapikey->vendor != 'Digiflazz') {
                throw new Exception('Invalid Vendor');
            }

            $secretvalue = stringEncryption('decrypt', $currentapikey->secretvalue);

            $post = file_get_contents('php://input');
            $content = json_decode($post, false);

            $signature = hash_hmac('sha1', $post, $secretvalue);

            if ($this->input->get_request_header('X-Hub-Signature') != 'sha1=' . $signature) {
                throw new Exception('Invalid Signature');
            }

            if (isset($content->data)) {
                $data = $content->data;
                $ref_id = $data->ref_id;
                $sn = $data->sn;
                $status = $data->status;
                $message = $data->message;

                $merchantid = $this->merchant->id;

                $order = $this->trorder->select('a.*, b.phonenumber')
                    ->join('msusers b', 'b.id = a.userid', 'LEFT')
                    ->join('msproduct c', 'c.id = a.serviceid', 'LEFT')
                    ->get(array(
                        'a.servercode' => $ref_id,
                        "(b.merchantid = '$merchantid' OR a.merchantid_order = '$merchantid') =" => true,
                        'a.status !=' => 'gagal'
                    ));

                if ($order->num_rows() == 0) {
                    throw new Exception('Order not found');
                }

                $row = $order->row();

                $update = array();
                $update['note'] = $message;
                $update['status'] = strtolower($status);
                $update['sn'] = $sn;

                $this->trorder->update(array(
                    'id' => $row->id
                ), $update);

                pullTransaction($row->userid ?? $row->clientip);

                if ($status == 'Gagal' && $row->userid != null) {
                    $check_history_balance = $this->historybalance->total(array(
                        'orderid' => $row->id,
                        'type' => 'IN',
                        'userid' => $row->userid
                    ));

                    if ($check_history_balance == 0) {
                        $currentbalance = getCurrentBalance($row->userid, true);

                        $inserthistorybalance = array();
                        $inserthistorybalance['userid'] = $row->userid;
                        $inserthistorybalance['type'] = 'IN';

                        if ($row->paymenttype == 'Otomatis' && $row->fee != null) {
                            $inserthistorybalance['nominal'] = $row->price - round($row->fee);
                        } else {
                            $inserthistorybalance['nominal'] = $row->price;
                        }

                        $inserthistorybalance['currentbalance'] = $currentbalance;
                        $inserthistorybalance['orderid'] = $row->id;
                        $inserthistorybalance['createdby'] = $row->userid;
                        $inserthistorybalance['createddate'] = getCurrentDate();

                        $this->historybalance->insert($inserthistorybalance);

                        $updateUser = array();
                        if ($row->paymenttype == 'Otomatis' && $row->fee != null) {
                            $updateUser['balance'] = $currentbalance + ($row->price - round($row->fee));
                        } else {
                            $updateUser['balance'] = $currentbalance + $row->price;
                        }

                        $this->msusers->update(array(
                            'id' => $row->userid
                        ), $updateUser);
                    }
                }

                if ($this->db->trans_status() === FALSE) {
                    throw new Exception('Update Order Failed');
                }

                $this->db->trans_commit();

                $this->send_notification($row->id, $this->merchant->id, $row->phonenumber ?? $row->phonenumber_order);
            } else {
                throw new Exception('Invalid Data');
            }
        } catch (Exception $ex) {
            $this->db->trans_rollback();
        }
    }

    public function paydisini()
    {
        try {
            $this->db->trans_begin();

            $key = getPost('key');
            $pay_id = getPost('pay_id');
            $unique_code = getPost('unique_code');
            $status = getPost('status');
            $signature = getPost('signature');

            $paymentgateway = $this->paymentgateway->get(array(
                'userid' => $this->merchant->id,
                'type' => 'Payment Gateway',
                "(isdisabled IS NULL OR isdisabled = 0) =" => true,
            ))->row();

            if ($paymentgateway == null) {
                throw new Exception('Payment Gateway not found');
            } else {
                $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                $apikey = $detail->apikey;

                $signature_server = md5($apikey . $unique_code . 'CallbackStatus');

                if ($signature_server != $signature) {
                    throw new Exception('Invalid Signature');
                }

                $get = $this->deposits->select('a.*')
                    ->join('msusers b', 'b.id = a.userid')
                    ->get(array(
                        'a.merchantid' => $this->merchant->id,
                        'a.status' => 'Pending',
                        'a.paydisinicode' => $unique_code,
                        'a.paymenttype' => 'Otomatis',
                        'a.gatewayvendor' => 'PayDisini',
                        'a.servercode' => $pay_id,
                    ));

                if ($get->num_rows() == 0) {
                    $get = $this->trorder->get(array(
                        'merchantid_order' => $this->merchant->id,
                        'paydisinicode' => $unique_code,
                        'servercode_payment' => $pay_id,
                        'status_payment' => 'pending',
                        'gatewayvendor' => 'PayDisini'
                    ));

                    if ($get->num_rows() == 0) {
                        throw new Exception('Order not found');
                    } else {
                        $order = $get->row();

                        if ($status == 'Success') {
                            $update = array();
                            $update['status_payment'] = 'sukses';
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $order->id
                            ), $update);

                            pullTransaction($order->userid ?? $order->clientip);

                            if ($this->db->trans_status() === FALSE) {
                                throw new Exception('Payment Failed');
                            }

                            $this->db->trans_commit();

                            return JSONResponse(array(
                                'success' => true,
                            ));
                        } else {
                            $this->trorder->update(array(
                                'id' => $order->id
                            ), array(
                                'status_payment' => strtolower($status),
                                'updateddate' => getCurrentDate()
                            ));

                            pullTransaction($order->userid ?? $order->clientip);

                            if ($this->db->trans_status() === FALSE) {
                                throw new Exception('Payment Failed');
                            }

                            $this->db->trans_commit();

                            return JSONResponse(array(
                                'success' => true,
                            ));
                        }
                    }
                } else {
                    $deposit = $get->row();

                    if ($status == 'Success') {
                        $update = array();
                        $update['status'] = 'Success';

                        $this->deposits->update(array(
                            'id' => $deposit->id
                        ), $update);

                        $this->send_notification($deposit->id, $this->merchant->id, $deposit->phonenumber, 'deposit');

                        $check_history_balance = $this->historybalance->total(array(
                            'depositid' => $deposit->id,
                            'type' => 'IN',
                            'userid' => $deposit->userid
                        ));

                        if ($check_history_balance == 0) {
                            $currentbalance = getCurrentBalance($deposit->userid, true);

                            $balance = null;
                            if ($deposit->isbonus == 1) {
                                $balance = $deposit->nominal + $deposit->nominalbonus - ($deposit->fee ?? 0);
                            } else {
                                $balance = $deposit->nominal - ($deposit->fee ?? 0);
                            }

                            $inserthistorybalance = array();
                            $inserthistorybalance['userid'] = $deposit->userid;
                            $inserthistorybalance['type'] = 'IN';
                            $inserthistorybalance['nominal'] = $balance;
                            $inserthistorybalance['currentbalance'] = $currentbalance;
                            $inserthistorybalance['depositid'] = $deposit->id;
                            $inserthistorybalance['createdby'] = $deposit->userid;
                            $inserthistorybalance['createddate'] = getCurrentDate();

                            $this->historybalance->insert($inserthistorybalance);

                            $balance = $currentbalance + $balance;

                            $updateUser = array();
                            $updateUser['balance'] = $balance;

                            $this->msusers->update(array(
                                'id' => $deposit->userid
                            ), $updateUser);
                        }

                        if ($this->db->trans_status() === FALSE) {
                            throw new Exception('Payment Failed');
                        }

                        $this->db->trans_commit();

                        return JSONResponse(array(
                            'success' => true,
                        ));
                    } else {
                        $this->db->trans_rollback();

                        return JSONResponse(array(
                            'success' => true,
                        ));
                    }
                }
            }
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponse(array(
                'success' => false,
            ));
        }
    }

    public function ipaymu()
    {
        try {
            $this->db->trans_begin();

            $trx_id = getPost('trx_id');
            $status = getPost('status');
            $status_code = getPost('status_code');
            $sid = getPost('sid');
            $reference_id = getPost('reference_id');

            $get = $this->deposits->select('a.*')
                ->join('msusers b', 'b.id = a.userid')
                ->get(array(
                    'a.merchantid' => $this->merchant->id,
                    'a.status' => 'Pending',
                    'a.code' => $reference_id,
                    'a.paymenttype' => 'Otomatis',
                    'a.gatewayvendor' => 'iPaymu',
                    'a.servercode' => $trx_id,
                ));

            if ($get->num_rows() == 0) {
                $get = $this->trorder->get(array(
                    'merchantid_order' => $this->merchant->id,
                    'clientcode' => $reference_id,
                    'servercode_payment' => $trx_id,
                    'status_payment' => 'pending',
                    'gatewayvendor' => 'iPaymu'
                ));

                if ($get->num_rows() == 0) {
                    throw new Exception('Order not found');
                } else {
                    $order = $get->row();

                    if ($status == "berhasil" && $status_code == 1) {
                        $update = array();
                        $update['status_payment'] = 'sukses';
                        $update['updateddate'] = getCurrentDate();

                        $this->trorder->update(array(
                            'id' => $order->id
                        ), $update);

                        pullTransaction($order->userid ?? $order->clientip);

                        if ($this->db->trans_status() === FALSE) {
                            throw new Exception('Payment Failed');
                        }

                        $this->db->trans_commit();

                        return JSONResponse(array(
                            'success' => true,
                            'message' => 'Payment Success'
                        ));
                    } else {
                        $this->trorder->update(array(
                            'id' => $order->id
                        ), array(
                            'status_payment' => strtolower($status),
                            'updateddate' => getCurrentDate()
                        ));

                        pullTransaction($order->userid ?? $order->clientip);

                        if ($this->db->trans_status() === FALSE) {
                            throw new Exception('Payment Failed');
                        }

                        $this->db->trans_commit();

                        return JSONResponse(array(
                            'success' => false,
                            'message' => 'Payment Failed'
                        ));
                    }
                }
            } else {
                $deposit = $get->row();

                if ($status == "berhasil" && $status_code == 1) {
                    $update = array();
                    $update['status'] = 'Success';

                    $this->deposits->update(array(
                        'id' => $deposit->id
                    ), $update);

                    $check_history_balance = $this->historybalance->total(array(
                        'depositid' => $deposit->id,
                        'type' => 'IN',
                        'userid' => $deposit->userid
                    ));

                    if ($check_history_balance == 0) {
                        $currentbalance = getCurrentBalance($deposit->userid, true);

                        $balance = null;
                        if ($deposit->isbonus == 1) {
                            $balance = $deposit->nominal + $deposit->nominalbonus - ($deposit->fee ?? 0);
                        } else {
                            $balance = $deposit->nominal - ($deposit->fee ?? 0);
                        }

                        $inserthistorybalance = array();
                        $inserthistorybalance['userid'] = $deposit->userid;
                        $inserthistorybalance['type'] = 'IN';
                        $inserthistorybalance['nominal'] = $balance;
                        $inserthistorybalance['currentbalance'] = $currentbalance;
                        $inserthistorybalance['depositid'] = $deposit->id;
                        $inserthistorybalance['createdby'] = $deposit->userid;
                        $inserthistorybalance['createddate'] = getCurrentDate();

                        $this->historybalance->insert($inserthistorybalance);

                        $balance = $currentbalance + $balance;

                        $updateUser = array();
                        $updateUser['balance'] = $balance;

                        $this->msusers->update(array(
                            'id' => $deposit->userid
                        ), $updateUser);

                        $this->send_notification($deposit->id, $this->merchant->id, $deposit->phonenumber, 'deposit');
                    }

                    if ($this->db->trans_status() === FALSE) {
                        throw new Exception('Payment Failed');
                    }

                    $this->db->trans_commit();

                    return JSONResponse(array(
                        'success' => true,
                        'message' => 'Payment Success'
                    ));
                } else {
                    throw new Exception('Payment Failed');
                }
            }
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponse(array(
                'success' => false,
                'message' => $ex->getMessage()
            ));
        }
    }

    public function duitku()
    {
        try {
            $this->db->trans_begin();

            $paymentgateway = $this->paymentgateway->get(array(
                'userid' => $this->merchant->id,
                'type' => 'Payment Gateway',
                'vendor' => 'Duitku'
            ))->row();

            if ($paymentgateway == null) {
                throw new Exception('Payment Gateway not found');
            }

            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));

            if (isset($detail->apikey) && isset($detail->merchantcode)) {
                $apikey = $detail->apikey;
                $merchantcode = $detail->merchantcode;

                $duitku_config = new \Duitku\Config($apikey, $merchantcode, ENVIRONMENT == 'development', true, false);
                $callback = \Duitku\Api::callback($duitku_config);

                $response = json_decode($callback);

                if ($response->resultCode == "00") {
                    $merchantCode = $response->merchantCode;
                    $amount = $response->amount;
                    $merchantOrderId = $response->merchantOrderId;
                    $reference = $response->reference;
                    $signature = $response->signature;

                    $validate_signature = md5($merchantCode . $amount . $merchantOrderId . $apikey);

                    if ($signature == $validate_signature) {
                        $get = $this->deposits->select('a.*')
                            ->join('msusers b', 'b.id = a.userid')
                            ->get(array(
                                'a.merchantid' => $this->merchant->id,
                                'a.status' => 'Pending',
                                'a.paymenttype' => 'Otomatis',
                                'a.gatewayvendor' => 'Duitku',
                                'a.servercode' => $reference,
                            ));

                        if ($get->num_rows() == 0) {
                            $get = $this->trorder->get(array(
                                'merchantid_order' => $this->merchant->id,
                                'servercode_payment' => $reference,
                                'status_payment' => 'pending',
                                'gatewayvendor' => 'Duitku'
                            ));

                            if ($get->num_rows() == 0) {
                                throw new Exception('Order not found');
                            } else {
                                $order = $get->row();

                                $update = array();
                                $update['status_payment'] = 'sukses';
                                $update['updateddate'] = getCurrentDate();

                                $this->trorder->update(array(
                                    'id' => $order->id
                                ), $update);

                                pullTransaction($order->userid ?? $order->clientip);

                                if ($this->db->trans_status() === FALSE) {
                                    throw new Exception('Payment Failed');
                                }

                                $this->db->trans_commit();

                                return JSONResponse(array(
                                    'success' => true,
                                    'message' => 'Payment Success'
                                ));
                            }
                        } else {
                            $deposit = $get->row();

                            $update = array();
                            $update['status'] = 'Success';

                            $this->deposits->update(array(
                                'id' => $deposit->id
                            ), $update);

                            $this->send_notification($deposit->id, $this->merchant->id, $deposit->phonenumber, 'deposit');

                            $check_history_balance = $this->historybalance->total(array(
                                'depositid' => $deposit->id,
                                'type' => 'IN',
                                'userid' => $deposit->userid
                            ));

                            if ($check_history_balance == 0) {
                                $currentbalance = getCurrentBalance($deposit->userid, true);

                                $balance = null;
                                if ($deposit->isbonus == 1) {
                                    $balance = $deposit->nominal + $deposit->nominalbonus - ($deposit->fee ?? 0);
                                } else {
                                    $balance = $deposit->nominal - ($deposit->fee ?? 0);
                                }

                                $inserthistorybalance = array();
                                $inserthistorybalance['userid'] = $deposit->userid;
                                $inserthistorybalance['type'] = 'IN';
                                $inserthistorybalance['nominal'] = $balance;
                                $inserthistorybalance['currentbalance'] = $currentbalance;
                                $inserthistorybalance['depositid'] = $deposit->id;
                                $inserthistorybalance['createdby'] = $deposit->userid;
                                $inserthistorybalance['createddate'] = getCurrentDate();

                                $this->historybalance->insert($inserthistorybalance);

                                $balance = $currentbalance + $balance;

                                $updateUser = array();
                                $updateUser['balance'] = $balance;

                                $this->msusers->update(array(
                                    'id' => $deposit->userid
                                ), $updateUser);
                            }

                            if ($this->db->trans_status() === FALSE) {
                                throw new Exception('Payment Failed');
                            }

                            $this->db->trans_commit();

                            return JSONResponse(array(
                                'success' => true,
                                'message' => 'Payment Success'
                            ));
                        }
                    } else {
                        throw new Exception('Invalid Signature');
                    }
                } else {
                    throw new Exception('Invalid Signature');
                }
            } else {
                throw new Exception('Invalid API Key');
            }
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            log_message_user('error', '[DUITKU CALLBACK] ' . $ex->getMessage(), $this->merchant->id);

            return JSONResponse(array(
                'success' => false,
                'message' => 'Payment Failed'
            ));
        }
    }
}
