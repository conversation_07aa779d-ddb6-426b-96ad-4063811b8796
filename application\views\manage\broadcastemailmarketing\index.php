<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">
            Broadcast Email Marketing
        </h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Management</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Broadcast Email Marketing</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->

    <!--begin::Button-->
    <a href="<?= base_url(uri_string() . '/add') ?>" class="btn btn-dark fw-bold">Buat Broadcast</a>
    <!--end::Button-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <!--begin::Info Section-->
    <div class="card mb-7 shadow-sm">
        <div class="card-body p-9">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2 class="fw-bold text-dark mb-3">Kelola Broadcast Email Marketing</h2>
                    <p class="text-gray-600 fs-6 mb-3">
                        Kirim email marketing massal kepada pelanggan Anda dengan mudah. Buat kampanye email yang menarik,
                        pilih penerima, dan pantau status pengiriman email Anda dalam satu dashboard.
                    </p>
                    <div class="notice d-flex bg-light-primary rounded border-primary border border-dashed p-4">
                        <i class="fa fa-clock fs-2tx text-primary me-4"></i>
                        <div class="d-flex flex-stack flex-grow-1">
                            <div class="fw-semibold">
                                <div class="fs-6 text-gray-700">
                                    <strong>Sistem Pengiriman Otomatis:</strong><br>
                                    Email broadcast dikirim secara bertahap dengan maksimal 100 email setiap 10 menit untuk menjaga
                                    deliverability dan menghindari spam filter. Status pengiriman akan diperbarui secara real-time.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <i class="fa fa-paper-plane fs-3x text-primary"></i>
                </div>
            </div>
        </div>
    </div>
    <!--end::Info Section-->

    <?php if ($this->session->flashdata('error')) : ?>
        <div class="alert alert-danger d-flex align-items-center p-5 mb-7">
            <i class="fa fa-exclamation-triangle fs-2hx text-danger me-4"></i>
            <div class="d-flex flex-column">
                <h4 class="mb-1 text-danger">Error</h4>
                <span><?= $this->session->flashdata('error') ?></span>
            </div>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-12">
            <div class="card mb-5">
                <div class="card-body">
                    <table class="table table-striped table-row-bordered gy-5 datatables-broadcast">
                        <thead>
                            <tr class="fw-semibold fs-6 text-muted">
                                <th>Judul Broadcast</th>
                                <th>Subject Email</th>
                                <th>Penerima</th>
                                <th>Status</th>
                                <th>Tanggal Dibuat</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>

                        <tbody>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $('.datatables-broadcast').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            stateSave: true,
            ordering: false,
            ajax: {
                url: '<?= base_url(uri_string() . '/datatables') ?>',
            }
        });
    };

    function deleteBroadcast(id) {
        return Swal.fire({
            title: 'Pernyataan',
            text: 'Apakah anda yakin ingin menghapus broadcast ini?',
            icon: "info",
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-primary",
                cancelButton: 'btn btn-danger'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/delete') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id,
                        '<?= $this->security->get_csrf_token_name() ?>': '<?= $this->security->get_csrf_hash() ?>'
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();
                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }

    function cancelBroadcast(id) {
        return Swal.fire({
            title: 'Batalkan Broadcast',
            text: 'Apakah anda yakin ingin membatalkan pengiriman broadcast ini?',
            icon: "warning",
            showCancelButton: true,
            confirmButtonText: 'Ya, Batalkan',
            cancelButtonText: 'Tidak',
            customClass: {
                confirmButton: "btn btn-danger",
                cancelButton: 'btn btn-secondary'
            }
        }).then(function(result) {
            if (result.isConfirmed) {
                $.ajax({
                    url: '<?= base_url(uri_string() . '/cancel') ?>',
                    method: 'POST',
                    dataType: 'json',
                    data: {
                        id: id,
                        '<?= $this->security->get_csrf_token_name() ?>': '<?= $this->security->get_csrf_hash() ?>'
                    },
                    success: function(response) {
                        if (response.RESULT == 'OK') {
                            return Swal.fire({
                                title: 'Berhasil',
                                text: response.MESSAGE,
                                icon: 'success'
                            }).then(function(result2) {
                                return window.location.reload();
                            });
                        } else {
                            return Swal.fire({
                                title: 'Gagal',
                                text: response.MESSAGE,
                                icon: 'error'
                            });
                        }
                    }
                }).fail(function() {
                    return Swal.fire({
                        title: 'Gagal',
                        text: 'Server sedang sibuk silahkan coba lagi nanti',
                        icon: 'error'
                    });
                })
            }
        });
    }
</script>