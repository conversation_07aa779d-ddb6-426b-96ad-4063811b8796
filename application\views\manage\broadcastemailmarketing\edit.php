<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">
            Edit Broadcast Email Marketing
        </h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('manage/broadcastemailmarketing') ?>" class="text-gray-600 text-hover-primary">Broadcast Email Marketing</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Edit Broadcast</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->

    <!--begin::Button-->
    <a href="<?= base_url('manage/broadcastemailmarketing') ?>" class="btn btn-danger fw-bold">Kembali</a>
    <!--end::Button-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <!--begin::Form Column-->
        <div class="col-md-8">
            <div class="card mb-5">
                <div class="card-header">
                    <h3 class="card-title">Form Edit Broadcast</h3>
                </div>

                <form id="form-edit-broadcast" autocomplete="off">
                    <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>" />
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-10">
                                    <label for="title" class="required form-label">Judul Broadcast</label>
                                    <input type="text" class="form-control" id="title" name="title" value="<?= $broadcast->title ?>" placeholder="Masukkan judul broadcast" <?= $broadcast->status == 'sending' ? 'disabled' : '' ?>>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-10">
                                    <label for="subject" class="required form-label">Subject Email</label>
                                    <input type="text" class="form-control" id="subject" name="subject" value="<?= $broadcast->subject ?>" placeholder="Masukkan subject email" <?= $broadcast->status == 'sending' ? 'disabled' : '' ?>>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-10">
                                    <label class="form-label">Penerima</label>
                                    <div class="card border">
                                        <div class="card-body">
                                            <div class="fw-bold text-primary">
                                                <i class="fa fa-users me-2"></i>
                                                <?= $broadcast->recipient_count ?> penerima dipilih
                                            </div>
                                            <div class="text-muted fs-7 mt-2">
                                                Penerima tidak dapat diubah setelah broadcast dibuat
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-10">
                                    <label for="content" class="required form-label">Konten Email</label>
                                    <textarea id="content" name="content" placeholder="Masukkan konten email..." <?= $broadcast->status == 'sending' ? 'disabled' : '' ?>><?= $broadcast->content ?></textarea>

                                    <!--begin::Parameter Table-->
                                    <div class="card border border-dashed border-gray-300 mt-3">
                                        <div class="card-header">
                                            <h3 class="card-title fs-6 fw-bold">Daftar Parameter Email Marketing</h3>
                                        </div>
                                        <div class="card-body p-4">
                                            <div class="table-responsive">
                                                <table class="table table-row-bordered table-row-gray-100 gy-4 gs-4">
                                                    <thead>
                                                        <tr class="fw-bold fs-7 text-gray-800 border-bottom-2 border-gray-200">
                                                            <th class="min-w-100px">Parameter</th>
                                                            <th>Deskripsi</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody class="fs-7">
                                                        <tr>
                                                            <td class="fw-bold">${NAME}</td>
                                                            <td>Menampilkan nama lengkap pelanggan</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-bold">${EMAIL}</td>
                                                            <td>Menampilkan alamat email pelanggan</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-bold">${BALANCE}</td>
                                                            <td>Menampilkan saldo saat ini pelanggan</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-bold">${USERNAME}</td>
                                                            <td>Menampilkan username pelanggan</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-bold">${PHONENUMBER}</td>
                                                            <td>Menampilkan nomor telepon pelanggan</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-bold">${COMPANYNAME}</td>
                                                            <td>Menampilkan nama perusahaan</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-bold">${COMPANYADDRESS}</td>
                                                            <td>Menampilkan alamat perusahaan</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-bold">${UNSUBSCRIBE_LINK}</td>
                                                            <td>Link untuk berhenti berlangganan email</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-bold">${HOOK_URL}</td>
                                                            <td>Link tracking untuk menganalisa ketertarikan pelanggan (redirect ke domain merchant)</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <!--end::Parameter Table-->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <?php if ($broadcast->status == 'sending') : ?>
                            <div class="alert alert-warning d-flex align-items-center p-5 mb-5">
                                <i class="fa fa-exclamation-triangle fs-2hx text-warning me-4"></i>
                                <div class="d-flex flex-column">
                                    <h4 class="mb-1 text-warning">Broadcast Sedang Dikirim</h4>
                                    <span>Broadcast ini sedang dalam proses pengiriman dan tidak dapat diedit. Batalkan pengiriman terlebih dahulu jika ingin melakukan perubahan.</span>
                                </div>
                            </div>
                            <div class="d-flex justify-content-end">
                                <span class="text-muted">Form tidak dapat diedit saat status sending</span>
                            </div>
                        <?php else : ?>
                            <div class="d-flex justify-content-end">
                                <?php if ($broadcast->status == 'draft') : ?>
                                    <button type="submit" name="send_now" value="0" class="btn btn-warning me-3">
                                        <span class="indicator-label">Update Draft</span>
                                        <span class="indicator-progress">Please wait...
                                            <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                        </span>
                                    </button>
                                    <button type="submit" name="send_now" value="1" class="btn btn-primary">
                                        <span class="indicator-label">Update & Kirim</span>
                                        <span class="indicator-progress">Please wait...
                                            <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                        </span>
                                    </button>
                                <?php else : ?>
                                    <button type="submit" name="send_now" value="0" class="btn btn-primary">
                                        <span class="indicator-label">Update Broadcast</span>
                                        <span class="indicator-progress">Please wait...
                                            <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                        </span>
                                    </button>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </form>
            </div>
        </div>
        <!--end::Form Column-->

        <!--begin::Preview Column-->
        <div class="col-md-4">
            <div class="card mb-5">
                <div class="card-header">
                    <h3 class="card-title">Preview Email</h3>
                </div>
                <div class="card-body">
                    <div class="border rounded p-4" style="background-color: #f8f9fa; min-height: 400px;">
                        <div class="mb-4">
                            <div class="fw-bold text-muted mb-2">Subject:</div>
                            <div id="preview-subject" class="fw-bold"><?= $broadcast->subject ?></div>
                        </div>

                        <div class="mb-4">
                            <div class="fw-bold text-muted mb-2">Kepada:</div>
                            <div id="preview-recipients" class="text-muted"><?= $broadcast->recipient_count ?> penerima</div>
                        </div>

                        <hr>

                        <div>
                            <div class="fw-bold text-muted mb-2">Konten:</div>
                            <div id="preview-content" class="border rounded p-3" style="background-color: white; min-height: 300px;">
                                <?= $broadcast->content ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--end::Preview Column-->
    </div>
</div>

<script>
    var editor;

    $(document).ready(function() {
        // Initialize ClassicEditor
        ClassicEditor
            .create(document.querySelector('#content'))
            .then(response => {
                editor = response;

                // Disable editor if status is sending
                <?php if ($broadcast->status == 'sending') : ?>
                    editor.enableReadOnlyMode('sending-mode');
                <?php endif; ?>

                // Listen for content changes
                editor.model.document.on('change:data', () => {
                    updatePreview();
                });

                // Initialize preview after editor is ready
                updatePreview();
            })
            .catch(responseError => {
                console.error('Error initializing editor:', responseError);
            });

        // Bind events
        $('#title, #subject').on('input', updatePreview);
    });

    // Update preview
    function updatePreview() {
        var subject = $('#subject').val() || '<?= $broadcast->subject ?>';
        var content = '';

        // Get content from ClassicEditor if available
        if (editor) {
            content = editor.getData() || '<?= $broadcast->content ?>';
        } else {
            content = '<?= $broadcast->content ?>';
        }

        $('#preview-subject').text(subject);
        $('#preview-content').html(content);
    }

    // Form submission
    $('#form-edit-broadcast').submit(function(e) {
        e.preventDefault();

        // Prevent submission if status is sending
        <?php if ($broadcast->status == 'sending') : ?>
            return Swal.fire({
                title: 'Tidak Dapat Diedit',
                text: 'Broadcast yang sedang dikirim tidak dapat diedit.',
                icon: 'warning'
            });
        <?php endif; ?>

        var btn = $(e.originalEvent.submitter);
        var sendNow = btn.val();

        btn.attr('data-kt-indicator', 'on');

        // Create FormData object
        var formData = new FormData(this);

        // Add content from ClassicEditor
        if (editor) {
            formData.append('content', editor.getData());
        }

        // Add send_now parameter
        formData.append('send_now', sendNow);

        $.ajax({
            url: '<?= base_url(uri_string() . '/process') ?>',
            method: 'POST',
            dataType: 'json',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                btn.removeAttr('data-kt-indicator');

                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Berhasil',
                        text: response.MESSAGE,
                        icon: 'success'
                    }).then(function(result) {
                        return window.location.href = '<?= base_url('manage/broadcastemailmarketing') ?>';
                    });
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            btn.removeAttr('data-kt-indicator');

            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        });
    });
</script>