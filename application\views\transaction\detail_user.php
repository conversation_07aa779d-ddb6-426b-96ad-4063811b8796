<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog modal-lg">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Detail Pembeli</h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <div class="modal-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-7">
                        <label class="col-form-label fw-bold fs-6 pt-0 pb-1">Nama</label>
                        <p><?= $user->name ?></p>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-7">
                        <label class="col-form-label fw-bold fs-6 pt-0 pb-1">Email</label>
                        <p><?= $user->email ?></p>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-7">
                        <label class="col-form-label fw-bold fs-6 pt-0 pb-1">Level Akses</label>
                        <p><?= ucfirst($user->level ?? 'Member') ?></p>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-7">
                        <label class="col-form-label fw-bold fs-6 pt-0 pb-1">Nomor Handphone</label>
                        <p><?= $user->phone ?? '-' ?></p>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-7">
                        <label class="col-form-label fw-bold fs-6 pt-0 pb-1">Saldo</label>
                        <p class="text-success">Rp <?= IDR($user->balance) ?></p>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-7">
                        <label class="col-form-label fw-bold fs-6 pt-0 pb-1">Tanggal Terdaftar</label>
                        <p><?= tgl_indo(date('Y-m-d', strtotime($user->createddate))) ?>
                            <?= date('H:i:s', strtotime($user->createddate)) ?></p>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-7">
                        <label class="col-form-label fw-bold fs-6 pt-0 pb-1">Tanggal Transaksi Terakhir</label>
                        <p><?= $lastTransactionDate ? tgl_indo(date('Y-m-d', strtotime($lastTransactionDate))) . ' ' . date('H:i:s', strtotime($lastTransactionDate)) : 'Belum ada transaksi' ?>
                        </p>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="mb-7">
                        <label class="col-form-label fw-bold fs-6 pt-0 pb-1">Jumlah/Total Transaksi Keseluruhan</label>
                        <p class="text-primary fw-bold"><?= number_format($totalTransactions, 0, ',', '.') ?> Transaksi
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal-footer">
            <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
        </div>
    </div>
</div>