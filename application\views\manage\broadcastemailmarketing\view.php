<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">
            Detail Broadcast Email Marketing
        </h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('manage/broadcastemailmarketing') ?>" class="text-gray-600 text-hover-primary">Broadcast Email Marketing</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Detail Broadcast</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->

    <!--begin::Button-->
    <a href="<?= base_url('manage/broadcastemailmarketing') ?>" class="btn btn-light fw-bold">Kembali</a>
    <!--end::Button-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <!--begin::Broadcast Info-->
        <div class="col-md-8">
            <div class="card mb-5">
                <div class="card-header">
                    <h3 class="card-title">Informasi Broadcast</h3>
                </div>
                <div class="card-body">
                    <div class="row mb-7">
                        <div class="col-md-3">
                            <label class="fw-bold text-muted">Judul Broadcast:</label>
                        </div>
                        <div class="col-md-9">
                            <span class="fw-bold"><?= $broadcast->title ?></span>
                        </div>
                    </div>

                    <div class="row mb-7">
                        <div class="col-md-3">
                            <label class="fw-bold text-muted">Subject Email:</label>
                        </div>
                        <div class="col-md-9">
                            <span><?= $broadcast->subject ?></span>
                        </div>
                    </div>

                    <div class="row mb-7">
                        <div class="col-md-3">
                            <label class="fw-bold text-muted">Status:</label>
                        </div>
                        <div class="col-md-9">
                            <?php if ($broadcast->status == 'sent') : ?>
                                <span class="badge badge-light-success">Terkirim</span>
                            <?php elseif ($broadcast->status == 'draft') : ?>
                                <span class="badge badge-light-warning">Draft</span>
                            <?php elseif ($broadcast->status == 'cancelled') : ?>
                                <span class="badge badge-light-danger">Dibatalkan</span>
                            <?php else : ?>
                                <span class="badge badge-light-info">Mengirim</span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="row mb-7">
                        <div class="col-md-3">
                            <label class="fw-bold text-muted">Jumlah Penerima:</label>
                        </div>
                        <div class="col-md-9">
                            <span class="badge badge-light-primary"><?= $broadcast->recipient_count ?> penerima</span>
                        </div>
                    </div>

                    <div class="row mb-7">
                        <div class="col-md-3">
                            <label class="fw-bold text-muted">Tanggal Dibuat:</label>
                        </div>
                        <div class="col-md-9">
                            <span><?= date('d M Y H:i', strtotime($broadcast->createddate)) ?></span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-3">
                            <label class="fw-bold text-muted">Konten Email:</label>
                        </div>
                        <div class="col-md-9">
                            <div class="border rounded p-5" style="background-color: #f9f9f9;">
                                <?= $broadcast->content ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--end::Broadcast Info-->

        <!--begin::Recipients List-->
        <div class="col-md-4">
            <div class="card mb-5">
                <div class="card-header">
                    <h3 class="card-title">Daftar Penerima</h3>
                </div>
                <div class="card-body">
                    <?php if (!empty($recipients)) : ?>
                        <div style="max-height: 400px; overflow-y: auto;">
                            <?php foreach ($recipients as $recipient) : ?>
                                <div class="d-flex align-items-center mb-4">
                                    <div class="symbol symbol-40px me-3">
                                        <div class="symbol-label bg-light-primary text-primary fw-bold">
                                            <?= strtoupper(substr($recipient->name, 0, 1)) ?>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold"><?= $recipient->name ?></div>
                                        <div class="text-muted fs-7"><?= $recipient->email ?></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else : ?>
                        <div class="text-center text-muted py-5">
                            <i class="fa fa-users fs-3x mb-3"></i>
                            <p>Tidak ada penerima</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!--begin::Statistics-->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Statistik Pengiriman</h3>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <span class="text-muted">Total Penerima:</span>
                        <span class="fw-bold"><?= $broadcast->recipient_count ?></span>
                    </div>

                    <?php if ($broadcast->status != 'draft') : ?>
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <span class="text-muted">Email Terkirim:</span>
                            <span class="fw-bold text-success"><?= $queue_stats['sent'] ?></span>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <span class="text-muted">Email Gagal:</span>
                            <span class="fw-bold text-danger"><?= $queue_stats['failed'] ?></span>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <span class="text-muted">Unsubscribe:</span>
                            <span class="fw-bold text-warning"><?= $queue_stats['unsubscribed'] ?></span>
                        </div>

                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <span class="text-muted">Klik Email:</span>
                            <span class="fw-bold text-info"><?= $queue_stats['clicked'] ?></span>
                        </div>

                        <?php if ($queue_stats['pending'] > 0) : ?>
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <span class="text-muted">Menunggu Kirim:</span>
                                <span class="fw-bold text-warning"><?= $queue_stats['pending'] ?></span>
                            </div>
                        <?php endif; ?>

                        <?php if ($queue_stats['processing'] > 0) : ?>
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <span class="text-muted">Sedang Diproses:</span>
                                <span class="fw-bold text-info"><?= $queue_stats['processing'] ?></span>
                            </div>
                        <?php endif; ?>

                        <?php if ($queue_stats['cancelled'] > 0) : ?>
                            <div class="d-flex justify-content-between align-items-center mb-4">
                                <span class="text-muted">Dibatalkan:</span>
                                <span class="fw-bold text-muted"><?= $queue_stats['cancelled'] ?></span>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>

                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <span class="text-muted">Status:</span>
                        <span class="fw-bold">
                            <?php if ($broadcast->status == 'sent') : ?>
                                Terkirim
                            <?php elseif ($broadcast->status == 'draft') : ?>
                                Draft
                            <?php elseif ($broadcast->status == 'cancelled') : ?>
                                Dibatalkan
                            <?php else : ?>
                                Mengirim
                            <?php endif; ?>
                        </span>
                    </div>

                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted">Dibuat:</span>
                        <span class="fw-bold"><?= date('d/m/Y', strtotime($broadcast->createddate)) ?></span>
                    </div>
                </div>
            </div>
            <!--end::Statistics-->
        </div>
        <!--end::Recipients List-->
    </div>

    <?php if ($broadcast->status != 'draft' && !empty($email_queue)) : ?>
        <!--begin::Email Delivery Details-->
        <div class="row mt-5">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Detail Pengiriman Email</h3>
                    </div>
                    <div class="card-body">
                        <!-- Nav tabs -->
                        <ul class="nav nav-tabs nav-line-tabs nav-line-tabs-2x mb-5 fs-6">
                            <li class="nav-item">
                                <a class="nav-link active" data-bs-toggle="tab" href="#tab_sent">
                                    <span class="text-success">Berhasil Terkirim</span>
                                    <span class="badge badge-light-success ms-2"><?= $queue_stats['sent'] ?></span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-bs-toggle="tab" href="#tab_failed">
                                    <span class="text-danger">Gagal Terkirim</span>
                                    <span class="badge badge-light-danger ms-2"><?= $queue_stats['failed'] ?></span>
                                </a>
                            </li>
                            <?php if ($queue_stats['pending'] > 0) : ?>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#tab_pending">
                                        <span class="text-warning">Menunggu</span>
                                        <span class="badge badge-light-warning ms-2"><?= $queue_stats['pending'] ?></span>
                                    </a>
                                </li>
                            <?php endif; ?>
                            <?php if ($queue_stats['processing'] > 0) : ?>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#tab_processing">
                                        <span class="text-info">Sedang Diproses</span>
                                        <span class="badge badge-light-info ms-2"><?= $queue_stats['processing'] ?></span>
                                    </a>
                                </li>
                            <?php endif; ?>
                            <?php if ($queue_stats['cancelled'] > 0) : ?>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#tab_cancelled">
                                        <span class="text-danger">Dibatalkan</span>
                                        <span class="badge badge-light-danger ms-2"><?= $queue_stats['cancelled'] ?></span>
                                    </a>
                                </li>
                            <?php endif; ?>
                            <?php if ($queue_stats['unsubscribed'] > 0) : ?>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#tab_unsubscribed">
                                        <span class="text-warning">Unsubscribe</span>
                                        <span class="badge badge-light-warning ms-2"><?= $queue_stats['unsubscribed'] ?></span>
                                    </a>
                                </li>
                            <?php endif; ?>
                            <?php if ($queue_stats['clicked'] > 0) : ?>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#tab_clicked">
                                        <span class="text-info">Klik Email</span>
                                        <span class="badge badge-light-info ms-2"><?= $queue_stats['clicked'] ?></span>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>

                        <!-- Tab content -->
                        <div class="tab-content">
                            <!-- Sent emails tab -->
                            <div class="tab-pane fade show active" id="tab_sent">
                                <?php
                                $sent_emails = array_filter($email_queue, function ($item) {
                                    return $item->status == 'sent';
                                });
                                ?>
                                <?php if (!empty($sent_emails)) : ?>
                                    <div class="table-responsive">
                                        <table class="table table-row-bordered gy-5">
                                            <thead>
                                                <tr class="fw-semibold fs-6 text-muted">
                                                    <th>Nama Penerima</th>
                                                    <th>Email</th>
                                                    <th>Tanggal Terkirim</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($sent_emails as $email) : ?>
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <div class="symbol symbol-30px me-3">
                                                                    <div class="symbol-label bg-light-success text-success fw-bold">
                                                                        <?= strtoupper(substr($email->recipient_name, 0, 1)) ?>
                                                                    </div>
                                                                </div>
                                                                <span class="fw-bold"><?= $email->recipient_name ?></span>
                                                            </div>
                                                        </td>
                                                        <td><?= $email->recipient_email ?></td>
                                                        <td>
                                                            <span class="text-success">
                                                                <i class="fa fa-check-circle text-success me-1"></i>
                                                                <?= date('d M Y H:i', strtotime($email->sentdate)) ?>
                                                            </span>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else : ?>
                                    <div class="text-center text-muted py-10">
                                        <i class="fa fa-inbox fs-3x text-muted mb-3"></i>
                                        <p>Belum ada email yang berhasil terkirim</p>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Failed emails tab -->
                            <div class="tab-pane fade" id="tab_failed">
                                <?php
                                $failed_emails = array_filter($email_queue, function ($item) {
                                    return $item->status == 'failed';
                                });
                                ?>
                                <?php if (!empty($failed_emails)) : ?>
                                    <div class="table-responsive">
                                        <table class="table table-row-bordered gy-5">
                                            <thead>
                                                <tr class="fw-semibold fs-6 text-muted">
                                                    <th>Nama Penerima</th>
                                                    <th>Email</th>
                                                    <th>Pesan Error</th>
                                                    <th>Tanggal Gagal</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($failed_emails as $email) : ?>
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <div class="symbol symbol-30px me-3">
                                                                    <div class="symbol-label bg-light-danger text-danger fw-bold">
                                                                        <?= strtoupper(substr($email->recipient_name, 0, 1)) ?>
                                                                    </div>
                                                                </div>
                                                                <span class="fw-bold"><?= $email->recipient_name ?></span>
                                                            </div>
                                                        </td>
                                                        <td><?= $email->recipient_email ?></td>
                                                        <td>
                                                            <span class="text-danger">
                                                                <?= $email->error_message ?: 'Gagal mengirim email' ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <span class="text-danger">
                                                                <i class="fa fa-times-circle text-danger me-1"></i>
                                                                <?= date('d M Y H:i', strtotime($email->updateddate)) ?>
                                                            </span>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else : ?>
                                    <div class="text-center text-muted py-10">
                                        <i class="fa fa-check-circle fs-3x text-success mb-3"></i>
                                        <p>Tidak ada email yang gagal terkirim</p>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Pending emails tab -->
                            <?php if ($queue_stats['pending'] > 0) : ?>
                                <div class="tab-pane fade" id="tab_pending">
                                    <?php
                                    $pending_emails = array_filter($email_queue, function ($item) {
                                        return $item->status == 'pending';
                                    });
                                    ?>
                                    <div class="table-responsive">
                                        <table class="table table-row-bordered gy-5">
                                            <thead>
                                                <tr class="fw-semibold fs-6 text-muted">
                                                    <th>Nama Penerima</th>
                                                    <th>Email</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($pending_emails as $email) : ?>
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <div class="symbol symbol-30px me-3">
                                                                    <div class="symbol-label bg-light-warning text-warning fw-bold">
                                                                        <?= strtoupper(substr($email->recipient_name, 0, 1)) ?>
                                                                    </div>
                                                                </div>
                                                                <span class="fw-bold"><?= $email->recipient_name ?></span>
                                                            </div>
                                                        </td>
                                                        <td><?= $email->recipient_email ?></td>
                                                        <td>
                                                            <span class="badge badge-light-warning">
                                                                <i class="fa fa-clock me-1 text-warning"></i>
                                                                Menunggu Pengiriman
                                                            </span>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Processing emails tab -->
                            <?php if ($queue_stats['processing'] > 0) : ?>
                                <div class="tab-pane fade" id="tab_processing">
                                    <?php
                                    $processing_emails = array_filter($email_queue, function ($item) {
                                        return $item->status == 'processing';
                                    });
                                    ?>
                                    <div class="table-responsive">
                                        <table class="table table-row-bordered gy-5">
                                            <thead>
                                                <tr class="fw-semibold fs-6 text-muted">
                                                    <th>Nama Penerima</th>
                                                    <th>Email</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($processing_emails as $email) : ?>
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <div class="symbol symbol-30px me-3">
                                                                    <div class="symbol-label bg-light-info text-info fw-bold">
                                                                        <?= strtoupper(substr($email->recipient_name, 0, 1)) ?>
                                                                    </div>
                                                                </div>
                                                                <span class="fw-bold"><?= $email->recipient_name ?></span>
                                                            </div>
                                                        </td>
                                                        <td><?= $email->recipient_email ?></td>
                                                        <td>
                                                            <span class="badge badge-light-info">
                                                                <i class="fa fa-spinner fa-spin me-1"></i>
                                                                Sedang Diproses
                                                            </span>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Cancelled emails tab -->
                            <?php if ($queue_stats['cancelled'] > 0) : ?>
                                <div class="tab-pane fade" id="tab_cancelled">
                                    <?php
                                    $cancelled_emails = array_filter($email_queue, function ($item) {
                                        return $item->status == 'cancelled';
                                    });
                                    ?>
                                    <div class="table-responsive">
                                        <table class="table table-row-bordered gy-5">
                                            <thead>
                                                <tr class="fw-semibold fs-6 text-muted">
                                                    <th>Nama Penerima</th>
                                                    <th>Email</th>
                                                    <th>Status</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($cancelled_emails as $email) : ?>
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <div class="symbol symbol-30px me-3">
                                                                    <div class="symbol-label bg-light-secondary text-muted fw-bold">
                                                                        <?= strtoupper(substr($email->recipient_name, 0, 1)) ?>
                                                                    </div>
                                                                </div>
                                                                <span class="fw-bold"><?= $email->recipient_name ?></span>
                                                            </div>
                                                        </td>
                                                        <td><?= $email->recipient_email ?></td>
                                                        <td>
                                                            <span class="badge badge-light-danger">
                                                                <i class="fa fa-ban me-1 text-danger"></i>
                                                                Dibatalkan
                                                            </span>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Unsubscribed emails tab -->
                            <?php if ($queue_stats['unsubscribed'] > 0) : ?>
                                <div class="tab-pane fade" id="tab_unsubscribed">
                                    <div class="table-responsive">
                                        <table class="table table-row-bordered gy-5">
                                            <thead>
                                                <tr class="fw-semibold fs-6 text-muted">
                                                    <th>Nama User</th>
                                                    <th>Email</th>
                                                    <th>Alasan</th>
                                                    <th>Tanggal Unsubscribe</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($unsubscribe_data as $unsubscribe) : ?>
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <div class="symbol symbol-30px me-3">
                                                                    <div class="symbol-label bg-light-warning text-warning fw-bold">
                                                                        <?= strtoupper(substr($unsubscribe->user_name, 0, 1)) ?>
                                                                    </div>
                                                                </div>
                                                                <span class="fw-bold"><?= $unsubscribe->user_name ?></span>
                                                            </div>
                                                        </td>
                                                        <td><?= $unsubscribe->email ?></td>
                                                        <td>
                                                            <span class="text-muted">
                                                                <?= $unsubscribe->reason ?: 'Tidak ada alasan' ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <span class="text-warning">
                                                                <i class="fa fa-user-times text-warning me-1"></i>
                                                                <?= date('d M Y H:i', strtotime($unsubscribe->createddate)) ?>
                                                            </span>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Clicked emails tab -->
                            <?php if ($queue_stats['clicked'] > 0) : ?>
                                <div class="tab-pane fade" id="tab_clicked">
                                    <div class="table-responsive">
                                        <table class="table table-row-bordered gy-5">
                                            <thead>
                                                <tr class="fw-semibold fs-6 text-muted">
                                                    <th>Nama User</th>
                                                    <th>Email</th>
                                                    <th>IP Address</th>
                                                    <th>Tanggal Klik</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($click_data as $click) : ?>
                                                    <tr>
                                                        <td>
                                                            <div class="d-flex align-items-center">
                                                                <div class="symbol symbol-30px me-3">
                                                                    <div class="symbol-label bg-light-info text-info fw-bold">
                                                                        <?= strtoupper(substr($click->user_name, 0, 1)) ?>
                                                                    </div>
                                                                </div>
                                                                <span class="fw-bold"><?= $click->user_name ?></span>
                                                            </div>
                                                        </td>
                                                        <td><?= $click->user_email ?></td>
                                                        <td>
                                                            <span class="text-muted">
                                                                <?= $click->ip_address ?: 'Tidak diketahui' ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <span class="text-info">
                                                                <i class="fa fa-mouse-pointer text-info me-1"></i>
                                                                <?= date('d M Y H:i', strtotime($click->createddate)) ?>
                                                            </span>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--end::Email Delivery Details-->
    <?php endif; ?>
</div>