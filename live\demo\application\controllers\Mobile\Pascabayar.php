<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property MobileSession $mobilesession
 * @property MsUsers $msusers
 * @property DisabledCategory $disabledcategory
 * @property CI_Output $output
 * @property MsProduct $msproduct
 * @property TrOrder $trorder
 * @property CI_DB_mysqli_driver $db
 * @property MsRole $msrole
 * @property MsRoleDiscount $msrolediscount
 * @property MsRoleDiscountAdv $msrolediscountadv
 * @property HistoryBalance $historybalance
 * @property ApiKeys_WhatsApp $apikeys_whatsapp
 */
class Pascabayar extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MobileSession', 'mobilesession');
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('DisabledCategory', 'disabledcategory');
        $this->load->model('MsProduct', 'msproduct');
        $this->load->model('TrOrder', 'trorder');
        $this->load->model('MsRole', 'msrole');
        $this->load->model('MsRoleDiscount', 'msrolediscount');
        $this->load->model('MsRoleDiscountAdv', 'msrolediscountadv');
        $this->load->model('HistoryBalance', 'historybalance');
        $this->load->model('ApiKeys_WhatsApp', 'apikeys_whatsapp');
    }

    private function send_notification($orderid, $userid, $phonenumber)
    {
        // Kirim Firebase notification untuk order
        sendFirebaseNotificationOrder($orderid, $userid);

        $apikeys_whatsapp = $this->apikeys_whatsapp->get(array(
            'userid' => $userid,
        ));

        if ($apikeys_whatsapp->num_rows() == 0) {
            return false;
        }

        $row = $apikeys_whatsapp->row();

        $wablasingateway = new WABlasinGateway(stringEncryption('decrypt', $row->apikey), stringEncryption('decrypt', $row->devicecode));

        $messagenotification = replaceParameterNotification($orderid, $userid);
        if ($messagenotification != null && $phonenumber != null) {
            $phonenumber = changePrefixPhone($phonenumber);

            $send = $wablasingateway->sendMessage($phonenumber, $messagenotification);

            if (!isset($send->RESULT) || (isset($send->RESULT) && $send->RESULT != 'OK')) {
                log_message_user('error', '[WHATSAPP SEND MESSAGE] Send notification failed: ' . json_encode($send), $userid);
            }

            return true;
        }

        return false;
    }

    private function validateToken($token)
    {
        if ($token == null) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $token = stringEncryption('decrypt', $token);

        if ($token == null) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $token = json_decode($token);

        if (json_last_error() != JSON_ERROR_NONE) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $validate = $this->mobilesession->total(array(
            'token' => getPost('token')
        ));

        if ($validate == 0) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $user = $this->msusers->get(array(
            'id' => $token->id,
            'merchantid' => $this->merchant->id,
        ));

        if ($user->num_rows() == 0) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $row = $user->row();

        return array(
            'status' => true,
            'message' => 'Data berhasil diambil',
            'data' => $row
        );
    }

    public function inquiry()
    {
        $token = getPost('token');
        $product = getPost('product');
        $target = getPost('target');
        $denominasi = getPost('denominasi');

        $validate = $this->validateToken($token);

        if ($validate['status'] == false) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => $validate['message']
            ));
        }

        if ($product == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Produk tidak boleh kosong'
            ));
        }

        if ($target == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Target tidak boleh kosong'
            ));
        }

        $apikeys = getCurrentAPIKeys('PPOB', $this->merchant->id);
        $vendor = getCurrentVendor('PPOB', $this->merchant->id);

        $disabled = $this->disabledcategory->result(array(
            'userid' => $this->merchant->id,
            'category_apikey' => 'PPOB'
        ));

        $disabled_category = array();
        foreach ($disabled as $key => $value) {
            $disabled_category[] = $value->categoryname;
        }

        $get = $this->msproduct->where_not_in('category', $disabled_category)
            ->get(array(
                'id' => $product,
                'userid' => $this->merchant->id,
                'vendor' => $vendor,
                'subcategory_apikey' => 'PASCABAYAR'
            ));

        if ($get->num_rows() == 0) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Produk tidak ditemukan'
            ));
        }

        $productRow = $get->row();

        if ($productRow->status != 1) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Produk tidak aktif'
            ));
        }

        if ($vendor == 'Digiflazz') {
            if ($productRow->brand == 'E-MONEY') {
                if ($denominasi == null) {
                    $this->output->set_status_header(400);
                    $this->output->set_content_type('application/json');

                    return JSONResponse(array(
                        'status' => false,
                        'message' => 'Denominasi tidak boleh kosong'
                    ));
                } else if (!is_numeric($denominasi)) {
                    $this->output->set_status_header(400);
                    $this->output->set_content_type('application/json');

                    return JSONResponse(array(
                        'status' => false,
                        'message' => 'Denominasi harus berupa angka'
                    ));
                }
            }

            $refid = generateTransactionNumber('PASCABAYAR');

            $digiflazz = new Digiflazz(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));
            $cek = $digiflazz->cek_tagihan($productRow->code, $target, $refid, ENVIRONMENT == 'development', $denominasi);

            $result = json_decode($cek);

            if (isset($result->data->status)) {
                if ($result->data->status != 'Gagal') {
                    $this->output->set_status_header(200);
                    $this->output->set_content_type('application/json');

                    return JSONResponse(array(
                        'status' => true,
                        'message' => 'Data berhasil diambil',
                        'data' => $result->data,
                        'refid' => $refid,
                        'profit' => $productRow->profit,
                        'productname' => strtoupper($productRow->productname),
                    ));
                } else {
                    if ($result != null) {
                        log_message_user('error', '[DIGIFLAZZ TAGIHAN CHECK] Response: ' . json_encode($result), $this->merchant->id);
                    }

                    $responsecode = getResponseCodeDigiflazz($result->data->rc);

                    $this->output->set_status_header(400);
                    $this->output->set_content_type('application/json');

                    return JSONResponse(array(
                        'status' => false,
                        'message' => $responsecode
                    ));
                }
            } else {
                $this->output->set_status_header(400);
                $this->output->set_content_type('application/json');

                return JSONResponse(array(
                    'status' => false,
                    'message' => 'Gagal mengecek tagihan'
                ));
            }
        } else {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Vendor tidak menyediakan layanan ini'
            ));
        }
    }

    public function product()
    {
        $brand = getPost('brand');
        $vendor = getCurrentVendor('PPOB', $this->merchant->id);

        if ($brand == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Brand tidak boleh kosong'
            ));
        }

        $product = $this->msproduct->select('a.id, a.productname, a.code, a.brand, a.vendor')
            ->order_by('a.productname', 'ASC')
            ->result(array(
                'a.userid' => $this->merchant->id,
                'a.vendor' => $vendor,
                'a.brand' => $brand,
                'a.subcategory_apikey' => 'PASCABAYAR'
            ));

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => true,
            'message' => 'Data berhasil diambil',
            'data' => $product
        ));
    }

    public function payment()
    {
        try {
            $this->db->trans_begin();

            $token = getPost('token');
            $product = getPost('product');
            $target = getPost('target');
            $pin = getPost('pin');
            $apikeys = getCurrentAPIKeys('PPOB', $this->merchant->id);
            $vendor = getCurrentVendor('PPOB', $this->merchant->id);
            $denominasi = getPost('denominasi');

            $validate = $this->validateToken($token);

            if ($validate['status'] == false) {
                throw new Exception($validate['message']);
            }

            if ($product == null) {
                throw new Exception('Produk tidak boleh kosong');
            }

            if ($target == null) {
                throw new Exception('Tujuan tidak boleh kosong');
            }

            if ($pin == null) {
                throw new Exception('PIN Transaksi tidak boleh kosong');
            }

            $user = $validate['data'];

            if (stringEncryption('encrypt', $pin) != $user->pin) {
                throw new Exception('PIN Transaksi yang anda masukkan salah');
            }

            $disabled = $this->disabledcategory->result(array(
                'userid' => $this->merchant->id,
                'category_apikey' => 'PPOB',
            ));

            $disabled_category = array();
            foreach ($disabled as $key => $value) {
                $disabled_category[] = $value->categoryname;
            }

            $getproduct = $this->msproduct->where_not_in('category', $disabled_category)
                ->get(array(
                    'id' => $product,
                    'userid' => $this->merchant->id,
                    'vendor' => $vendor,
                    'subcategory_apikey' => 'PASCABAYAR'
                ));

            if ($getproduct->num_rows() == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            $productRow = $getproduct->row();

            if ($productRow->status != 1) {
                throw new Exception('Produk tidak tersedia');
            }

            if ($vendor == 'Digiflazz') {
                if ($productRow->brand == 'E-MONEY') {
                    if ($denominasi == null) {
                        throw new Exception('Denominasi tidak boleh kosong');
                    } else if (!is_numeric($denominasi)) {
                        throw new Exception('Denominasi harus berupa angka');
                    }
                }

                $refid = generateTransactionNumber('PASCABAYAR');

                $digiflazz = new Digiflazz(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));
                $cek = $digiflazz->cek_tagihan($productRow->code, $target, $refid, $denominasi);

                $result = json_decode($cek);

                if (isset($result->data->status)) {
                    if ($result->data->status != 'Gagal') {
                        $customer_name = null;

                        if (isset($result->data->customer_name)) {
                            $customer_name = $result->data->customer_name;
                        }

                        $getroleid = $user;

                        if ($getroleid->roleid == null) {
                            $getrole = $this->msrole->get(array(
                                'createdby' => $this->merchant->id,
                                'isdefault' => '1'
                            ))->row();
                        } else {
                            $getrole = $this->msrole->get(array(
                                'id' => $getroleid->roleid,
                                'createdby' => $this->merchant->id,
                            ))->row();
                        }

                        if ($getrole != null) {
                            if ($getrole->discounttype == 'Simple') {
                                $getrolediscount = $this->msrolediscount->get(array(
                                    'userid' => $this->merchant->id,
                                    'roleid' => $getroleid->roleid
                                ))->row();

                                $discount = $getrolediscount->trxdiscount ?? 0;
                            } else {
                                $discount = $this->msrolediscountadv->get(array(
                                    'createdby' => $this->merchant->id,
                                    'roleid' => $getrole->id,
                                    'servicetype' => 'Pascabayar'
                                ))->result();
                            }
                        } else {
                            $getrole = new stdClass();
                            $getrole->discounttype = 'Simple';
                            $discount = 0;
                        }

                        if ($getrole->discounttype == 'Simple') {
                            $fixprice = $result->data->selling_price - $discount;
                        } else {
                            $found = false;

                            foreach ($discount as $val) {
                                if ($found) continue;

                                if ($val->startrange <= $result->data->selling_price && $val->endrange >= $result->data->selling_price) {
                                    if ($val->discounttype == 'Persentase') {
                                        $fixprice = $result->data->selling_price - ($result->data->selling_price * $val->nominal / 100);

                                        $found = true;
                                    } else {
                                        $fixprice = $result->data->selling_price - $val->nominal;

                                        $found = true;
                                    }
                                }
                            }

                            if ($found == false) {
                                $fixprice = $result->data->selling_price;
                            }
                        }

                        if ($user->balance < ($fixprice + $productRow->profit)) {
                            throw new Exception('Saldo anda tidak mencukupi');
                        } else {
                            $update = array();
                            $update['balance'] = $user->balance - ($fixprice + $productRow->profit);

                            $this->msusers->update(array(
                                'id' => $user->id
                            ), $update);
                        }

                        $potonganprofit = $result->data->selling_price - $fixprice;

                        $insert = array();
                        $insert['userid'] = $user->id;
                        $insert['clientcode'] = $refid;
                        $insert['serviceid'] = $productRow->id;
                        $insert['target'] = $target;
                        $insert['price'] = $fixprice + $productRow->profit;
                        $insert['profit'] = ($productRow->profit - $potonganprofit);
                        $insert['currentsaldo'] = $user->balance;
                        $insert['status'] = 'pending';
                        $insert['note'] = 'Transaksi Sukses';
                        $insert['type'] = 'PPOB';
                        $insert['category_apikey'] = $productRow->category_apikey;
                        $insert['subcategory_apikey'] = $productRow->subcategory_apikey;
                        $insert['orderplatform'] = 'apps';
                        $insert['productcode'] = $productRow->code;
                        $insert['vendor'] = $productRow->vendor;
                        $insert['productname_order'] = $productRow->productname;
                        $insert['customer_name'] = $customer_name;
                        $insert['merchantid_order'] = $this->merchant->id;
                        $insert['status_payment'] = 'sukses';
                        $insert['vendorid'] = $productRow->vendorid;

                        $this->trorder->insert($insert);
                        $orderid = $this->db->insert_id();

                        $insertlog = array();
                        $insertlog['userid'] = $user->id;
                        $insertlog['type'] = 'OUT';
                        $insertlog['nominal'] = $fixprice + $productRow->profit;
                        $insertlog['currentbalance'] = $user->balance;
                        $insertlog['orderid'] = $orderid;
                        $insertlog['createdby'] = $user->id;
                        $insertlog['createddate'] = getCurrentDate();

                        $this->historybalance->insert($insertlog);

                        if ($this->db->trans_status() === FALSE) {
                            throw new Exception('Transaksi gagal');
                        }

                        pullTransaction($this->merchant->id);

                        $bayar = $digiflazz->bayar_tagihan($productRow->code, $target, $refid, ENVIRONMENT == 'development');
                        $result = json_decode($bayar);

                        if (isset($result->data->status)) {
                            if ($result->data->status != 'Gagal') {
                                $update = array();
                                $update['servercode'] = $result->data->ref_id;
                                $update['note'] = $result->data->message;
                                $update['sn'] = $result->data->sn ?? '';
                                $update['status'] = strtolower($result->data->status);
                                $update['jsonresponse'] = json_encode($result);

                                $this->trorder->update(array(
                                    'id' => $orderid
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($orderid, $this->merchant->id, $user->phonenumber);

                                $this->output->set_status_header(200);
                                $this->output->set_content_type('application/json');

                                return JSONResponse(array(
                                    'status' => true,
                                    'message' => 'Transaksi berhasil dilakukan',
                                    'transactionid' => $orderid
                                ));
                            } else {
                                $responsecode = getResponseCodeDigiflazz($result->data->rc);
                                throw new Exception($responsecode);
                            }
                        } else {
                            throw new Exception('Gagal melakukan pembayaran');
                        }
                    } else {
                        $responsecode = getResponseCodeDigiflazz($result->data->rc);
                        throw new Exception($responsecode);
                    }
                } else {
                    if ($result != null) {
                        log_message_user('error', '[DIGIFLAZZ TAGIHAN PAY] Response: ' . json_encode($result), $this->merchant->id);
                    }

                    throw new Exception('Gagal mengecek tagihan');
                }
            } else {
                throw new Exception('Vendor tidak mendukung');
            }
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => $ex->getMessage()
            ));
        }
    }
}
