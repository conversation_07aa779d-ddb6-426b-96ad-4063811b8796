<?php

use Dompdf\Dompdf;

defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsUsers $msusers
 * @property MobileSession $mobilesession
 * @property CI_Output $output
 * @property TrOrder $trorder
 */
class Transaction extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsUsers', 'msusers');
        $this->load->model('MobileSession', 'mobilesession');
        $this->load->model('TrOrder', 'trorder');
    }

    private function validateToken($token)
    {
        if ($token == null) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $token = stringEncryption('decrypt', $token);

        if ($token == null) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $token = json_decode($token);

        if (json_last_error() != JSON_ERROR_NONE) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $validate = $this->mobilesession->total(array(
            'token' => stringEncryption('encrypt', json_encode($token)),
        ));

        if ($validate == 0) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $user = $this->msusers->get(array(
            'id' => $token->id,
            'merchantid' => $this->merchant->id,
        ));

        if ($user->num_rows() == 0) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $row = $user->row();

        return array(
            'status' => true,
            'message' => 'Data berhasil diambil',
            'data' => $row
        );
    }

    private function compareDates($tanggalKedua)
    {
        // Mendapatkan tanggal hari ini
        $tanggalHariIni = date('Y-m-d');

        // Mengubah format tanggal kedua menjadi Y-m-d
        $tanggalKedua = date('Y-m-d', strtotime($tanggalKedua));

        if ($tanggalHariIni == $tanggalKedua) {
            $output = 'Hari Ini';
        } else if (strtotime($tanggalHariIni) - strtotime($tanggalKedua) == 86400) {
            $output = 'Kemarin';
        } else {
            $output = date('d F Y', strtotime($tanggalKedua));
        }

        return $output;
    }

    public function prabayar()
    {
        $token = getPost('token');
        $filter = getPost('filter');
        $start_date = getPost('start_date');
        $end_date = getPost('end_date');

        $validate = $this->validateToken($token);

        if ($validate['status'] == false) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse($validate);
        }

        $currentuser = $validate['data'];
        $userid = $currentuser->id;

        $where = array(
            'a.userid' => $userid,
            'a.category_apikey' => 'PPOB',
            'a.subcategory_apikey' => 'PRABAYAR'
        );

        if ($filter == 'today') {
            $where['DATE(a.createddate) ='] = date('Y-m-d');
        } else if ($filter == 'week') { // 7 Hari Terakhir
            $where['DATE(a.createddate) >='] = date('Y-m-d', strtotime('-7 days'));
            $where['DATE(a.createddate) <='] = date('Y-m-d');
        } else if ($filter == 'month') { // 30 Hari Terakhir
            $where['DATE(a.createddate) >='] = date('Y-m-d', strtotime('-30 days'));
            $where['DATE(a.createddate) <='] = date('Y-m-d');
        } else if ($filter == 'custom') {
            $where['DATE(a.createddate) >='] = $start_date;
            $where['DATE(a.createddate) <='] = $end_date;
        }

        $transaction = $this->trorder->select('a.id, b.productname, b.category, a.price, a.status, a.createddate, a.productname_order')
            ->join('msusers c', 'c.id = a.userid', 'LEFT')
            ->join('msproduct b', 'b.id = a.serviceid', 'LEFT')
            ->order_by('a.id', 'DESC')
            ->result($where);

        $group_by_date = array();
        foreach ($transaction as $key => $value) {
            $date = date('Y-m-d', strtotime($value->createddate));

            $group_by_date[$date][] = $value;
        }

        foreach ($group_by_date as $key => $value) {
            $group_by_date[$key] = array(
                'date' => $this->compareDates($key),
                'data' => $value
            );
        }

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => true,
            'message' => 'Data berhasil diambil',
            'data' => $group_by_date
        ));
    }

    public function pascabayar()
    {
        $token = getPost('token');
        $filter = getPost('filter');
        $start_date = getPost('start_date');
        $end_date = getPost('end_date');

        $validate = $this->validateToken($token);

        if ($validate['status'] == false) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse($validate);
        }

        $currentuser = $validate['data'];
        $userid = $currentuser->id;

        $where = array(
            'a.userid' => $userid,
            'a.category_apikey' => 'PPOB',
            'a.subcategory_apikey' => 'PASCABAYAR'
        );

        if ($filter == 'today') {
            $where['DATE(a.createddate) ='] = date('Y-m-d');
        } else if ($filter == 'week') { // 7 Hari Terakhir
            $where['DATE(a.createddate) >='] = date('Y-m-d', strtotime('-7 days'));
            $where['DATE(a.createddate) <='] = date('Y-m-d');
        } else if ($filter == 'month') { // 30 Hari Terakhir
            $where['DATE(a.createddate) >='] = date('Y-m-d', strtotime('-30 days'));
            $where['DATE(a.createddate) <='] = date('Y-m-d');
        } else if ($filter == 'custom') {
            $where['DATE(a.createddate) >='] = $start_date;
            $where['DATE(a.createddate) <='] = $end_date;
        }

        $transaction = $this->trorder->select('a.id, b.productname, b.category, a.price, a.status, a.createddate, a.productname_order')
            ->join('msusers c', 'c.id = a.userid', 'LEFT')
            ->join('msproduct b', 'b.id = a.serviceid', 'LEFT')
            ->order_by('a.id', 'DESC')
            ->result($where);

        $group_by_date = array();
        foreach ($transaction as $key => $value) {
            $date = date('Y-m-d', strtotime($value->createddate));

            $group_by_date[$date][] = $value;
        }

        foreach ($group_by_date as $key => $value) {
            $group_by_date[$key] = array(
                'date' => $this->compareDates($key),
                'data' => $value
            );
        }

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => true,
            'message' => 'Data berhasil diambil',
            'data' => $group_by_date
        ));
    }

    public function medsos()
    {
        $token = getPost('token');
        $filter = getPost('filter');
        $start_date = getPost('start_date');
        $end_date = getPost('end_date');

        $validate = $this->validateToken($token);

        if ($validate['status'] == false) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse($validate);
        }

        $currentuser = $validate['data'];
        $userid = $currentuser->id;

        $where = array(
            'a.userid' => $userid,
            'a.category_apikey' => 'SMM',
        );

        if ($filter == 'today') {
            $where['DATE(a.createddate) ='] = date('Y-m-d');
        } else if ($filter == 'week') { // 7 Hari Terakhir
            $where['DATE(a.createddate) >='] = date('Y-m-d', strtotime('-7 days'));
            $where['DATE(a.createddate) <='] = date('Y-m-d');
        } else if ($filter == 'month') { // 30 Hari Terakhir
            $where['DATE(a.createddate) >='] = date('Y-m-d', strtotime('-30 days'));
            $where['DATE(a.createddate) <='] = date('Y-m-d');
        } else if ($filter == 'custom') {
            $where['DATE(a.createddate) >='] = $start_date;
            $where['DATE(a.createddate) <='] = $end_date;
        }

        $transaction = $this->trorder->select('a.id, b.productname, b.category, a.price, a.status, a.createddate, a.productname_order')
            ->join('msusers c', 'c.id = a.userid', 'LEFT')
            ->join('msproduct b', 'b.id = a.serviceid', 'LEFT')
            ->order_by('a.id', 'DESC')
            ->result($where);

        $group_by_date = array();
        foreach ($transaction as $key => $value) {
            $date = date('Y-m-d', strtotime($value->createddate));

            $group_by_date[$date][] = $value;
        }

        foreach ($group_by_date as $key => $value) {
            $group_by_date[$key] = array(
                'date' => $this->compareDates($key),
                'data' => $value
            );
        }

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => true,
            'message' => 'Data berhasil diambil',
            'data' => $group_by_date
        ));
    }

    public function detail()
    {
        $transactionid = getPost('transactionid');
        $token = getPost('token');

        if ($transactionid == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Transaction ID tidak boleh kosong'
            ));
        }

        $validate = $this->validateToken($token);

        if ($validate['status'] == false) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse($validate);
        }

        $currentuser = $validate['data'];

        $transaction = $this->trorder->select('a.*, b.productname, b.category, b.category_apikey, b.subcategory_apikey, d.asseturl')
            ->join('msusers e', 'e.id = a.userid', 'LEFT')
            ->join('msproduct b', 'b.id = a.serviceid', 'LEFT')
            ->join('categoryimage c', 'c.userid = b.userid AND c.categoryname = b.category', 'LEFT')
            ->join('msicons d', 'd.id = c.assetid', 'LEFT')
            ->row(array(
                'a.id' => $transactionid,
                'a.userid' => $currentuser->id
            ));

        if ($transaction == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Data tidak ditemukan'
            ));
        }

        $createddate = new DateTime($transaction->createddate);
        $updateddate = new DateTime($transaction->updateddate);
        $waktu_proses = $createddate->diff($updateddate);

        $timeprocess = "-";
        if (strtolower($transaction->status) == 'success' || strtolower($transaction->status) == 'sukses' || strtolower($transaction->status) == 'completed') {
            $timeprocess = $waktu_proses->format("%a Hari %h Jam %i Menit %s Detik");
        }

        $transaction->timeprocess = $timeprocess;

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => true,
            'message' => 'Data berhasil diambil',
            'data' => $transaction
        ));
    }

    public function struk()
    {
        $token = getPost('token');

        $validate = $this->validateToken($token);

        if ($validate['status'] == false) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse($validate);
        }

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => true,
            'message' => 'Data berhasil diambil',
            'data' => json_decode($this->merchant->strukcontent ?? '[]'),
            'alias' => strukContent()
        ));
    }

    public function download_struk()
    {
        $token = getGet('token');
        $transactionid = getGet('transactionid');
        $price = getGet('price');

        if ($transactionid == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Transaction ID tidak boleh kosong'
            ));
        }

        $validate = $this->validateToken($token);

        if ($validate['status'] == false) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse($validate);
        }

        $currentuser = $validate['data'];

        $get = $this->trorder->select('a.createddate, a.status, a.clientcode, c.productname, a.price, a.updateddate, a.target, c.category_apikey, a.sn, d.domain, d.companyaddress, d.companyicon, d.companyname, d.strukcontent, a.startcount, a.remain, a.qty')
            ->join('msusers b', 'b.id = a.userid', 'LEFT')
            ->join('msproduct c', 'c.id = a.serviceid', 'LEFT')
            ->join('msusers d', 'd.id = b.merchantid', 'LEFT')
            ->get(array(
                'a.id' => $transactionid,
                'a.userid' => $currentuser->id
            ));

        if ($get->num_rows() == 0) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Data tidak ditemukan'
            ));
        }

        $row = $get->row();

        $data = array();
        $data['title'] = 'Cetak Struk ' . $row->clientcode;
        $data['transaction'] = $row;
        $data['price'] = $price;

        $dompdf = new Dompdf();
        $dompdf->loadHtml($this->load->view('transaction/print', $data, true));

        $dompdf->setPaper('A5', 'P');

        $dompdf->render();

        $dompdf->stream("Struk Transaksi", array("Attachment" => false));
    }
}
