<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property MsUsers $msusers
 * @property HistoryBalance $historybalance
 * @property CI_DB_mysqli_driver $db
 */
class Addons extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsUsers', 'msusers');
        $this->load->model('HistoryBalance', 'historybalance');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Addons';
        $data['content'] = 'user/addons/index';

        return $this->load->view('master', $data);
    }

    public function buy_application()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda belum memiliki lisensi, Silahkan beli lisensi terlebih dahulu');
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('user/addons/buy_application', array(
                    'user' => getCurrentUser()
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function buy_emailmarketing()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda belum memiliki lisensi, Silahkan beli lisensi terlebih dahulu');
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('user/addons/buy_emailmarketing', array(
                    'user' => getCurrentUser()
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_buy_application()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda belum memiliki lisensi, Silahkan beli lisensi terlebih dahulu');
            } else if (getCurrentUser()->unlock_app == 1) {
                throw new Exception('Anda telah memiliki aplikasi android');
            }

            $currentbalance = getCurrentBalance(null, true);

            if ($currentbalance < 1500000) {
                throw new Exception('Saldo anda tidak mencukupi');
            }

            $inserthistorybalance = array();
            $inserthistorybalance['userid'] = getCurrentIdUser();
            $inserthistorybalance['type'] = 'OUT';
            $inserthistorybalance['nominal'] = 1500000;
            $inserthistorybalance['currentbalance'] = $currentbalance;
            $inserthistorybalance['description'] = 'Pembelian Aplikasi Android';
            $inserthistorybalance['createdby'] = getCurrentIdUser();
            $inserthistorybalance['createddate'] = getCurrentDate();

            $this->historybalance->insert($inserthistorybalance);

            $update = array();
            $update['balance'] = $currentbalance - 1500000;
            $update['unlock_app'] = 1;

            $this->msusers->update(array(
                'id' => getCurrentIdUser()
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal membeli aplikasi android');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Berhasil membeli aplikasi android');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_buy_emailmarketing()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda belum memiliki lisensi, Silahkan beli lisensi terlebih dahulu');
            } else if (getCurrentUser()->access_emailmarketing == 1) {
                throw new Exception('Anda telah memiliki addon Email Marketing');
            }

            $currentbalance = getCurrentBalance(null, true);

            if ($currentbalance < 500000) {
                throw new Exception('Saldo anda tidak mencukupi');
            }

            $inserthistorybalance = array();
            $inserthistorybalance['userid'] = getCurrentIdUser();
            $inserthistorybalance['type'] = 'OUT';
            $inserthistorybalance['nominal'] = 500000;
            $inserthistorybalance['currentbalance'] = $currentbalance;
            $inserthistorybalance['description'] = 'Pembelian Addon Email Marketing';
            $inserthistorybalance['createdby'] = getCurrentIdUser();
            $inserthistorybalance['createddate'] = getCurrentDate();

            $this->historybalance->insert($inserthistorybalance);

            $update = array();
            $update['balance'] = $currentbalance - 500000;
            $update['access_emailmarketing'] = 1;

            $this->msusers->update(array(
                'id' => getCurrentIdUser()
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal membeli addon Email Marketing');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Addon Email Marketing berhasil dibeli');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
