<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property MsBroadcastEmailMarketing $broadcastemailmarketing
 * @property MsUsers $msusers
 * @property Datatables $datatables
 * @property CI_DB_mysqli_driver $db
 */
class BroadcastEmailMarketing extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsBroadcastEmailMarketing', 'broadcastemailmarketing');
        $this->load->model('MsBroadcastEmailMarketingQueue', 'broadcastemailmarketingqueue');
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('MsEmailUnsubscribe', 'emailunsubscribe');
        $this->load->model('MsEmailMarketingClick', 'emailmarketingclick');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->access_emailmarketing != 1) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Broadcast Email Marketing';
        $data['content'] = 'manage/broadcastemailmarketing/index';

        return $this->load->view('master', $data);
    }

    public function datatables_broadcast()
    {
        try {
            if (isLogin() && isUser() && getCurrentUser()->licenseid != null && getCurrentUser()->access_emailmarketing == 1) {
                $data = array();

                $datatables = $this->datatables->make('MsBroadcastEmailMarketing', 'QueryDatatables', 'SearchDatatables');

                $where = array(
                    'a.userid' => getCurrentIdUser()
                );

                foreach ($datatables->getData($where) as $key => $value) {
                    $detail = array();
                    $detail[] = $value->title;
                    $detail[] = $value->subject;
                    $detail[] = $value->recipient_count . ' penerima';
                    $detail[] = $value->status == 'sent' ? '<span class="badge badge-light-success">Terkirim</span>' : ($value->status == 'draft' ? '<span class="badge badge-light-warning">Draft</span>' : ($value->status == 'cancelled' ? '<span class="badge badge-light-danger">Dibatalkan</span>' :
                        '<span class="badge badge-light-info">Mengirim</span>'));
                    $detail[] = date('d M Y H:i', strtotime($value->createddate));

                    // Action buttons based on status
                    $actions = "<a href=\"" . base_url('manage/broadcastemailmarketing/view/' . $value->id) . "\" class=\"btn btn-icon btn-info btn-sm mb-1\">
                        <i class=\"fa fa-eye\"></i>
                    </a>";

                    if ($value->status == 'sending') {
                        // Only show cancel button for sending status
                        $actions .= " <a href=\"javascript:;\" class=\"btn btn-icon btn-danger btn-sm mb-1\" onclick=\"cancelBroadcast('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-ban\"></i>
                        </a>";
                    } else if ($value->status == 'draft') {
                        // Show edit and delete buttons for draft status
                        $actions .= " <a href=\"" . base_url('manage/broadcastemailmarketing/edit/' . $value->id) . "\" class=\"btn btn-icon btn-warning btn-sm mb-1\">
                            <i class=\"fa fa-edit\"></i>
                        </a>

                        <a href=\"javascript:;\" class=\"btn btn-icon btn-danger btn-sm mb-1\" onclick=\"deleteBroadcast('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-trash\"></i>
                        </a>";
                    }
                    // For sent and cancelled status, only show view button

                    $detail[] = $actions;

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->access_emailmarketing != 1) {
            return redirect(base_url('dashboard'));
        }

        // Get all users for recipient selection
        $users = $this->msusers->result(array(
            'merchantid' => getCurrentIdUser(),
            'role' => 'User',
            'isdeleted' => null
        ));

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Buat Broadcast Email Marketing';
        $data['content'] = 'manage/broadcastemailmarketing/add';
        $data['users'] = $users;

        return $this->load->view('master', $data);
    }

    public function process_add_broadcast()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->access_emailmarketing != 1) {
                throw new Exception('Anda tidak memiliki akses Email Marketing');
            }

            $title = getPost('title');
            $subject = getPost('subject');
            $content = getPost('content');
            $recipients = getPost('recipients'); // array of user IDs
            $send_now = getPost('send_now');

            if ($title == null) {
                throw new Exception('Judul broadcast tidak boleh kosong');
            }

            if ($subject == null) {
                throw new Exception('Subject email tidak boleh kosong');
            }

            if ($content == null) {
                throw new Exception('Konten email tidak boleh kosong');
            }

            if (empty($recipients)) {
                throw new Exception('Pilih minimal satu penerima');
            }

            $recipient_count = count($recipients);
            $recipient_list = implode(',', $recipients);

            $insert = array();
            $insert['userid'] = getCurrentIdUser();
            $insert['title'] = $title;
            $insert['subject'] = $subject;
            $insert['content'] = $content;
            $insert['recipients'] = $recipient_list;
            $insert['recipient_count'] = $recipient_count;
            $insert['status'] = $send_now == 1 ? 'sending' : 'draft';
            $insert['createdby'] = getCurrentIdUser();
            $insert['createddate'] = getCurrentDate();

            $this->broadcastemailmarketing->insert($insert);
            $broadcast_id = $this->db->insert_id();

            if (!$broadcast_id) {
                throw new Exception('Gagal membuat broadcast');
            }

            // Create email queue if sending now
            if ($send_now == 1) {
                $this->create_email_queue($broadcast_id, $recipients);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal membuat broadcast');
            }

            $this->db->trans_commit();

            $message = $send_now == 1 ? 'Broadcast berhasil dibuat dan sedang dikirim' : 'Broadcast berhasil disimpan sebagai draft';
            return JSONResponseDefault('OK', $message);
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function view($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->access_emailmarketing != 1) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->broadcastemailmarketing->get(array(
            'id' => $id,
            'userid' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('manage/broadcastemailmarketing'));
        }

        $broadcast = $get->row();

        // Get recipient details
        $recipient_ids = explode(',', $broadcast->recipients);
        $recipients = array();
        foreach ($recipient_ids as $user_id) {
            $user = $this->msusers->get(array('id' => $user_id))->row();
            if ($user) {
                $recipients[] = $user;
            }
        }

        // Get email queue details if broadcast has been sent or is sending
        $email_queue = array();
        $unsubscribe_data = array();
        $click_data = array();
        $queue_stats = array(
            'total' => 0,
            'sent' => 0,
            'failed' => 0,
            'pending' => 0,
            'processing' => 0,
            'cancelled' => 0,
            'unsubscribed' => 0,
            'clicked' => 0
        );

        if ($broadcast->status != 'draft') {
            // Get queue data with recipient information
            $queue_data = $this->broadcastemailmarketingqueue->select('a.*, b.name as recipient_name, b.email as recipient_email')
                ->join('msusers b', 'a.recipientid = b.id', 'LEFT')
                ->result(array('a.broadcastid' => $broadcast->id));

            foreach ($queue_data as $queue) {
                $email_queue[] = $queue;
                $queue_stats['total']++;
                $queue_stats[$queue->status]++;
            }

            // Get unsubscribe data for this broadcast
            $unsubscribe_list = $this->emailunsubscribe->select('a.*, b.name as user_name')
                ->join('msusers b', 'a.userid = b.id', 'LEFT')
                ->result(array(
                    'a.broadcast_id' => $broadcast->id,
                    'a.merchantid' => getCurrentIdUser()
                ));

            foreach ($unsubscribe_list as $unsubscribe) {
                $unsubscribe_data[] = $unsubscribe;
                $queue_stats['unsubscribed']++;
            }

            // Get click data for this broadcast
            $click_list = $this->emailmarketingclick->select('a.*, b.name as user_name')
                ->join('msusers b', 'a.userid = b.id', 'LEFT')
                ->result(array(
                    'a.broadcast_id' => $broadcast->id,
                    'a.merchantid' => getCurrentIdUser()
                ));

            foreach ($click_list as $click) {
                $click_data[] = $click;
                $queue_stats['clicked']++;
            }
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Detail Broadcast Email Marketing';
        $data['content'] = 'manage/broadcastemailmarketing/view';
        $data['broadcast'] = $broadcast;
        $data['recipients'] = $recipients;
        $data['email_queue'] = $email_queue;
        $data['unsubscribe_data'] = $unsubscribe_data;
        $data['click_data'] = $click_data;
        $data['queue_stats'] = $queue_stats;

        return $this->load->view('master', $data);
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->access_emailmarketing != 1) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->broadcastemailmarketing->get(array(
            'id' => $id,
            'userid' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('manage/broadcastemailmarketing'));
        }

        $broadcast = $get->row();

        // Prevent access to edit page if status is sending
        if ($broadcast->status == 'sending') {
            $this->session->set_flashdata('error', 'Broadcast yang sedang dikirim tidak dapat diedit. Batalkan pengiriman terlebih dahulu jika ingin melakukan perubahan.');
            return redirect(base_url('manage/broadcastemailmarketing'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Edit Broadcast Email Marketing';
        $data['content'] = 'manage/broadcastemailmarketing/edit';
        $data['broadcast'] = $broadcast;

        return $this->load->view('master', $data);
    }

    public function delete()
    {
        return $this->process_delete_broadcast();
    }

    public function cancel()
    {
        return $this->process_cancel_broadcast();
    }

    public function process_edit_broadcast($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->access_emailmarketing != 1) {
                throw new Exception('Anda tidak memiliki akses Email Marketing');
            }

            $title = getPost('title');
            $subject = getPost('subject');
            $content = getPost('content');
            $send_now = getPost('send_now');

            if ($title == null) {
                throw new Exception('Judul broadcast tidak boleh kosong');
            }

            if ($subject == null) {
                throw new Exception('Subject email tidak boleh kosong');
            }

            if ($content == null) {
                throw new Exception('Konten email tidak boleh kosong');
            }

            $get = $this->broadcastemailmarketing->get(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $broadcast = $get->row();

            // Prevent editing if status is sending
            if ($broadcast->status == 'sending') {
                throw new Exception('Broadcast yang sedang dikirim tidak dapat diedit. Batalkan pengiriman terlebih dahulu jika ingin melakukan perubahan.');
            }

            $update = array();
            $update['title'] = $title;
            $update['subject'] = $subject;
            $update['content'] = $content;

            // Update status if send_now is requested and current status is draft
            if ($send_now == 1 && $broadcast->status == 'draft') {
                $update['status'] = 'sending';
            }

            $update['updatedby'] = getCurrentIdUser();
            $update['updateddate'] = getCurrentDate();

            $this->broadcastemailmarketing->update(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ), $update);

            // Create email queue if changing from draft to sending
            if ($send_now == 1 && $broadcast->status == 'draft') {
                $recipients = explode(',', $broadcast->recipients);
                $this->create_email_queue($id, $recipients);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah broadcast');
            }

            $this->db->trans_commit();

            $message = $send_now == 1 && $broadcast->status == 'draft' ? 'Broadcast berhasil diubah dan sedang dikirim' : 'Broadcast berhasil diubah';
            return JSONResponseDefault('OK', $message);
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete_broadcast()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->access_emailmarketing != 1) {
                throw new Exception('Anda tidak memiliki akses Email Marketing');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->broadcastemailmarketing->get(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $broadcast = $get->row();

            // Prevent deletion if status is sending
            if ($broadcast->status == 'sending') {
                throw new Exception('Broadcast yang sedang dikirim tidak dapat dihapus. Gunakan tombol batalkan untuk membatalkan pengiriman.');
            }

            $this->broadcastemailmarketing->delete(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menghapus broadcast');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Broadcast berhasil dihapus');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_cancel_broadcast()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->access_emailmarketing != 1) {
                throw new Exception('Anda tidak memiliki akses Email Marketing');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->broadcastemailmarketing->get(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $broadcast = $get->row();

            // Only allow cancellation if status is sending
            if ($broadcast->status != 'sending') {
                throw new Exception('Hanya broadcast yang sedang dikirim yang dapat dibatalkan');
            }

            $update = array();
            $update['status'] = 'cancelled';
            $update['updatedby'] = getCurrentIdUser();
            $update['updateddate'] = getCurrentDate();

            $this->broadcastemailmarketing->update(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ), $update);

            // Cancel pending emails in queue
            $this->broadcastemailmarketingqueue->update(
                array(
                    'broadcastid' => $id,
                    'status' => 'pending'
                ),
                array(
                    'status' => 'cancelled',
                    'updateddate' => getCurrentDate(),
                    'updatedby' => getCurrentIdUser()
                )
            );

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal membatalkan broadcast');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Broadcast berhasil dibatalkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    private function create_email_queue($broadcast_id, $recipients)
    {
        try {
            foreach ($recipients as $recipient_id) {
                // Check if recipient exists and has valid email
                $recipient = $this->msusers->get(array(
                    'id' => $recipient_id,
                    'isdeleted' => null
                ))->row();
                if (!$recipient || !$recipient->email || !filter_var($recipient->email, FILTER_VALIDATE_EMAIL)) {
                    continue; // Skip invalid recipients
                }

                $queue_data = array(
                    'broadcastid' => $broadcast_id,
                    'recipientid' => $recipient_id,
                    'status' => 'pending',
                    'createddate' => getCurrentDate(),
                    'createdby' => getCurrentIdUser()
                );

                $this->broadcastemailmarketingqueue->insert($queue_data);
            }
        } catch (Exception $e) {
            log_message('error', '[BROADCAST EMAIL MARKETING] Error creating email queue: ' . $e->getMessage());
            throw new Exception('Gagal membuat antrian email: ' . $e->getMessage());
        }
    }
}
