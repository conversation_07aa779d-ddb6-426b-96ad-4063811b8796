<?php
defined('BASEPATH') or die('No direct script access allowed!');

use Dompdf\Dompdf;

/**
 * @property TrOrder $trorder
 * @property MsUsers $msusers
 * @property HistoryBalance $historybalance
 * @property Datatables $datatables
 * @property CI_DB_mysqli_driver|CI_DB_query_builder $db
 * @property ApiKeys_WhatsApp $apikeys_whatsapp
 */
class Transaction extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('TrOrder', 'trorder');
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('ApiKeys_WhatsApp', 'apikeys_whatsapp');
        $this->load->model('HistoryBalance', 'historybalance');
    }

    public function prabayar()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory == 'SMM') {
            return redirect(base_url('dashboard'));
        }

        $currentiduser = getCurrentIdUser();

        $status = $this->trorder->select('a.status')
            ->join('msusers b', 'b.id = a.userid', 'LEFT')
            ->join('msproduct c', 'c.id = a.serviceid')
            ->where('c.subcategory_apikey', 'PRABAYAR')
            ->group_by('a.status')
            ->order_by('a.status')
            ->result(array(
                "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true,
            ));

        $status_payment = $this->trorder->select('a.status_payment')
            ->join('msusers b', 'b.id = a.userid', 'LEFT')
            ->join('msproduct c', 'c.id = a.serviceid')
            ->where('c.subcategory_apikey', 'PRABAYAR')
            ->group_by('a.status_payment')
            ->order_by('a.status_payment')
            ->result(array(
                'a.status_payment !=' => null,
                "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true,
            ));

        $platform = $this->trorder->select('a.orderplatform')
            ->join('msusers b', 'b.id = a.userid', 'LEFT')
            ->join('msproduct c', 'c.id = a.serviceid')
            ->where('c.subcategory_apikey', 'PRABAYAR')
            ->group_by('a.orderplatform')
            ->order_by('a.orderplatform')
            ->result(array(
                'a.orderplatform !=' => null,
                "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true,
            ));

        $where_vendor = "";
        if (getCurrentUser()->multivendor != 1) {
            $currentvendor = getCurrentVendor('PPOB', $currentiduser);
            $where_vendor = "AND vendor = '$currentvendor' AND vendorid IS NULL";
        } else {
            $where_vendor = "AND vendorid IS NOT NULL AND vendorenabled = 1";
        }

        $category = $this->db->query("SELECT * FROM (SELECT category FROM msproduct WHERE userid = '" . $currentiduser . "' $where_vendor AND subcategory_apikey = 'PRABAYAR' GROUP BY category UNION ALL SELECT name AS category FROM mscategory WHERE servicetype = 'PPOB' AND userid = '" . $currentiduser . "') a ORDER BY a.category ASC")->result();

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Transaksi Prabayar';
        $data['content'] = 'transaction/prabayar';
        $data['status'] = $status;
        $data['status_payment'] = $status_payment;
        $data['category'] = $category;
        $data['platform'] = $platform;

        return $this->load->view('master', $data);
    }

    public function datatables_prabayar()
    {
        try {
            if (isLogin() && (isUser() && $this->user->companycategory != 'SMM' && getCurrentUser()->licenseid != null && getCurrentUser()->licenseid != 3)) {
                $data = array();
                $date = getPost('date');
                $transactionstatus = getPost('status');
                $paymentstatus = getPost('paymentstatus');
                $platform = getPost('platform');
                $transactioncode = getPost('transactioncode');
                $category = getPost('category');
                $brand = getPost('brand');
                $product = getPost('product');

                $datatable = $this->datatables->make('TrOrder', 'QueryDatatables', 'SearchDatatables');
                $currentiduser = getCurrentIdUser();

                $where = array(
                    "(c.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true,
                    "(a.subcategory_apikey = 'PRABAYAR' OR b.subcategory_apikey = 'PRABAYAR') =" => true,
                    'a.type' => 'PPOB'
                );

                if ($date != null && isset(explode(' - ', $date)[0]) && isset(explode(' - ', $date)[1])) {
                    $startdate = explode(' - ', $date)[0];
                    $startdate = date('Y-m-d', strtotime($startdate));

                    $enddate = explode(' - ', $date)[1];
                    $enddate = date('Y-m-d', strtotime($enddate));

                    $where['DATE(a.createddate) >='] = $startdate;
                    $where['DATE(a.createddate) <='] = $enddate;
                } else {
                    $where['DATE(a.createddate) >='] = date('Y-m-01');
                    $where['DATE(a.createddate) <='] = date('Y-m-t');
                }

                if ($transactioncode != null) {
                    $where['a.clientcode'] = $transactioncode;
                }

                if ($transactionstatus != null) {
                    $where['a.status'] = $transactionstatus;
                }

                if ($paymentstatus != null) {
                    $where['a.status_payment'] = $paymentstatus;
                }

                if ($platform != null) {
                    $where['a.orderplatform'] = $platform;
                }

                if ($category != null) {
                    $where['b.category'] = $category;
                }

                if ($brand != null) {
                    $where['b.brand'] = $brand;
                }

                if ($product != null) {
                    $where['b.id'] = $product;
                }

                foreach ($datatable->getData($where) as $key => $value) {
                    if (strtolower($value->status) == 'pending' || strtolower($value->status) == 'waiting') {
                        $status = "<span class=\"badge badge-warning\">Pending</span>";
                    } else if (strtolower($value->status) == 'success' || strtolower($value->status) == 'sukses') {
                        $status = "<span class=\"badge badge-success\">Success</span>";
                    } else {
                        $status = "<span class=\"badge badge-danger\">" . ucfirst($value->status) . "</span>";
                    }

                    if (strtolower($value->status_payment ?? '') == 'pending') {
                        $statuspayment = "<span class=\"badge badge-warning\">Pending</span>";
                    } else if (strtolower($value->status_payment ?? '') == 'sukses') {
                        $statuspayment = "<span class=\"badge badge-success\">Sukses</span>";
                    } else if (strtolower($value->status_payment ?? '') == 'gagal' || strtolower($value->status_payment ?? '') == 'expired') {
                        $statuspayment = "<span class=\"badge badge-danger\">Gagal</span>";
                    } else {
                        $statuspayment = "<span class=\"badge badge-secondary\">" . ucwords(strtolower($value->status_payment ?? '-')) . "</span>";
                    }

                    $createddate = new DateTime($value->createddate);
                    $updateddate = new DateTime($value->updateddate);

                    $waktu_proses = $createddate->diff($updateddate);

                    if ($value->hostvendor == null && $value->status == 'pending') {
                        $action = "<button type=\"button\" class=\"btn btn-icon btn-success btn-sm mb-1 me-1\" onclick=\"approveTransaction('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-check\"></i>
                        </button>
                        
                        <button type=\"button\" class=\"btn btn-icon btn-danger btn-sm mb-1 me-1\" onclick=\"rejectTransaction('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-times\"></i>
                        </button>";
                    } else if ($value->hostvendor != null && $value->status == 'pending' && $value->status_payment == 'pending' && ($value->paymenttype == 'Manual' || ($value->paymenttype == 'Otomatis' && $value->gatewayvendor == 'Notification Handler Services'))) {
                        $action = "<button type=\"button\" class=\"btn btn-icon btn-success btn-sm mb-1 me-1\" onclick=\"approveTransactionNologin('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-check\"></i>
                        </button>
                        
                        <button type=\"button\" class=\"btn btn-icon btn-danger btn-sm mb-1 me-1\" onclick=\"rejectTransaction('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-times\"></i>
                        </button>";
                    } else if (strtolower($value->status) == 'success'  || strtolower($value->status) == 'sukses') {
                        $action = "<button type=\"button\" class=\"btn btn-icon btn-danger btn-sm mb-1 me-1\" onclick=\"refundTransaction('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-undo\"></i>
                        </button>";
                    } else if ($value->queuetransaction == 1) {
                        $action = "<button type=\"button\" class=\"btn btn-icon btn-danger btn-sm mb-1 me-1\" onclick=\"rejectTransaction('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-times\"></i>
                        </button>";
                    } else if (($value->status == 'gagal' || $value->status == 'failed' || $value->status == 'error') && $value->userid == null && $value->hostvendor != null) {
                        $action = "<button type=\"button\" class=\"btn btn-warning btn-sm mb-1 me-1\" onclick=\"reorderTransaction('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-redo\"></i>
                            Re-Order Transaksi
                        </button>
                        
                        <button type=\"button\" class=\"btn btn-warning btn-sm mb-1 me-1\" onclick=\"reorderTransaction('" . stringEncryption('encrypt', $value->id) . "', 'wrong')\">
                            <i class=\"fa fa-redo\"></i>
                            Re-Order Transaksi (Salah Tujuan)
                        </button>";
                    } else {
                        $action = "";
                    }

                    $action .= "<a href=\"" . base_url('transaction/prabayar/detail/' . stringEncryption('encrypt', $value->id)) . "\" class=\"btn btn-primary btn-sm mb-1 me-1\">
                        <i class=\"fa fa-eye\"></i>
                        Detail
                    </a>";

                    $buyer = null;
                    if ($value->userid != null) {
                        $buyerName = $value->buyername ?? '- Guest -';
                        $buyer = "<a href=\"javascript:;\" class=\"fw-bold\" onclick=\"modalUserDetail('$value->userid')\">$buyerName</a>";

                        if ($value->phonenumber_order != null) {
                            $buyer .= "<br><a href=\"https://wa.me/$value->phonenumber_order\" target=\"_blank\" class=\"text-success\">
                                <i class=\"fa fa-whatsapp\"></i> WhatsApp
                            </a>";
                        }
                    } else if ($value->phonenumber_order != null) {
                        $buyer = "<a href=\"https://wa.me/$value->phonenumber_order\" target=\"_blank\">
                            " . ($value->buyername ?? '- Guest -') . "
                        </a>";
                    } else {
                        $buyer = $value->buyername ?? '- Guest -';
                    }

                    $balancebefore = $value->userid ? $value->currentsaldo : 0;
                    $balanceafter = $value->userid ? $value->currentsaldo - $value->price : 0;

                    if (strtolower($value->status) == 'success' || strtolower($value->status) == 'sukses') {
                        $processtime = $waktu_proses->format("(%a Hari %h Jam %i Menit %s Detik)");
                    } else {
                        $processtime = "";
                    }

                    $detail = array();
                    $detail[] = DateFormat($value->createddate, 'd F Y H:i:s') . "<br>" . DateFormat($value->updateddate, 'd F Y H:i:s') . "<br>$processtime";
                    $detail[] = "<a href=\"javascript:;\" class=\"fw-bold\" onclick=\"modalStruk('" . stringEncryption('encrypt', $value->id) . "')\">$value->clientcode</a><br>" . ($value->servercode ?? '-');
                    $detail[] = "<b>" . ($value->code ?? $value->productcode) . "</b><br>" . ($value->productname ?? ($value->productname_order ?? '- Layanan telah dihapus -'));
                    $detail[] = $value->target;
                    $detail[] = "Rp " . IDR($value->price) . "<br>" . "<span class=\"text-success\">Rp " . IDR($value->profit) . "</span>";
                    $detail[] = "<b>" . strtoupper($value->orderplatform) . "</b> - " . $buyer . "<br>" . "<span class=\"text-danger\">Rp " . IDR($balancebefore) . " <i class=\"fa fa-arrow-right\"></i> Rp " . IDR($balanceafter) . "</span>";
                    $detail[] = !empty($value->sn) ? $value->sn : '-';
                    $detail[] = $status;
                    $detail[] = $statuspayment;
                    $detail[] = $action;

                    $data[] = $detail;
                }

                return $datatable->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_approve()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = stringEncryption('decrypt', getPost('id'));
            $sn = getPost('sn');
            $currentiduser = getCurrentIdUser();

            if ($id == null) {
                throw new Exception('ID Transaksi tidak ditemukan');
            } else if ($sn != null) {
                $sn = removeSymbol($sn);
            }

            $get = $this->trorder->select('b.phonenumber, a.phonenumber_order, a.status_payment, c.vendor, a.userid, a.clientip')
                ->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->join('msproduct c', 'c.id = a.serviceid', 'LEFT')
                ->get(array(
                    'a.id' => $id,
                    'a.status' => 'pending',
                    "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true
                ));

            if ($get->num_rows() == 0) {
                throw new Exception('Transaksi tidak ditemukan');
            }

            $row = $get->row();

            if ($row->vendor == null && $sn == null) {
                throw new Exception('Silahkan masukkan SN terlebih dahulu');
            }

            $update = array();
            if ($row->status_payment == 'pending') {
                $update['status_payment'] = 'sukses';
            }

            if ($row->vendor == null) {
                $update['status'] = 'success';
            }

            if ($sn != null) {
                $update['sn'] = $sn;
            }

            $this->trorder->update(array(
                'id' => $id
            ), $update);

            pullTransaction($row->userid ?? $row->clientip);

            $this->send_notification($id, getCurrentIdUser(), $row->phonenumber ?? $row->phonenumber_order);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal melakukan approve transaksi');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Transaksi berhasil diapprove');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_reject()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = stringEncryption('decrypt', getPost('id'));
            $currentiduser = getCurrentIdUser();

            if ($id == null) {
                throw new Exception('ID Transaksi tidak ditemukan');
            }

            $get = $this->trorder->select('a.queuetransaction, a.userid, a.price, b.phonenumber, a.phonenumber_order, c.vendor, a.status_payment, a.clientip, a.paymenttype, a.fee')
                ->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->join('msproduct c', 'c.id = a.serviceid', 'LEFT')
                ->get(array(
                    'a.id' => $id,
                    'a.status' => 'pending',
                    "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true
                ));

            if ($get->num_rows() == 0) {
                throw new Exception('Transaksi tidak ditemukan');
            }

            $row = $get->row();

            $update = array();
            $update['status'] = 'failed';

            if ($row->status_payment == 'pending') {
                $update['status_payment'] = 'gagal';
            }

            if ($row->queuetransaction == 1) {
                $update['queuetransaction'] = 0;
            }

            $this->trorder->update(array(
                'id' => $id
            ), $update);

            pullTransaction($row->userid ?? $row->clientip);

            if ($row->userid != null && $row->status_payment == 'sukses') {
                $check_history_balance = $this->historybalance->total(array(
                    'userid' => $row->userid,
                    'type' => 'IN',
                    'orderid' => $id,
                ));

                if ($check_history_balance == 0) {
                    $currentbalance = getCurrentBalance($row->userid, true);

                    $inserthistorybalance = array();
                    $inserthistorybalance['userid'] = $row->userid;
                    $inserthistorybalance['type'] = 'IN';

                    if ($row->paymenttype == 'Otomatis') {
                        $inserthistorybalance['nominal'] = $row->price - round($row->fee ?? 0);
                    } else {
                        $inserthistorybalance['nominal'] = $row->price;
                    }

                    $inserthistorybalance['currentbalance'] = $currentbalance;
                    $inserthistorybalance['orderid'] = $id;
                    $inserthistorybalance['createdby'] = $row->userid;
                    $inserthistorybalance['createddate'] = getCurrentDate();

                    $this->historybalance->insert($inserthistorybalance);

                    $updateUser = array();

                    if ($row->paymenttype == 'Otomatis') {
                        $updateUser['balance'] = $currentbalance + ($row->price - round($row->fee ?? 0));
                    } else {
                        $updateUser['balance'] = $currentbalance + $row->price;
                    }

                    $this->msusers->update(array(
                        'id' => $row->userid
                    ), $updateUser);
                }
            }

            $this->send_notification($id, getCurrentIdUser(), $row->phonenumber ?? $row->phonenumber_order);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal melakukan reject transaksi');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Transaksi berhasil ditolak');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function smm()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory == 'PPOB') {
            return redirect(base_url('dashboard'));
        }

        $currentiduser = getCurrentIdUser();

        $status = $this->trorder->select('a.status')
            ->join('msusers b', 'b.id = a.userid', 'LEFT')
            ->join('msproduct c', 'c.id = a.serviceid')
            ->where('c.subcategory_apikey', 'SMM')
            ->group_by('a.status')
            ->order_by('a.status', 'ASC')
            ->result(array(
                "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true
            ));

        $status_payment = $this->trorder->select('a.status_payment')
            ->join('msusers b', 'b.id = a.userid', 'LEFT')
            ->join('msproduct c', 'c.id = a.serviceid')
            ->where('c.subcategory_apikey', 'SMM')
            ->group_by('a.status_payment')
            ->order_by('a.status_payment', 'ASC')
            ->result(array(
                'a.status_payment !=' => null,
                "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true
            ));

        $platform = $this->trorder->select('a.orderplatform')
            ->join('msusers b', 'b.id = a.userid', 'LEFT')
            ->join('msproduct c', 'c.id = a.serviceid')
            ->where('c.subcategory_apikey', 'SMM')
            ->group_by('a.orderplatform')
            ->order_by('a.orderplatform', 'ASC')
            ->result(array(
                'a.orderplatform !=' => null,
                "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true
            ));

        $where_vendor = "";
        if (getCurrentUser()->multivendor != 1) {
            $currentvendor = getCurrentVendor('SMM', getCurrentIdUser());
            $where_vendor = "AND vendor = '$currentvendor' AND vendorid IS NULL";
        } else {
            $where_vendor = "AND vendorid IS NOT NULL AND vendorenabled = 1";
        }

        $category = $this->db->query("SELECT * FROM (SELECT category FROM msproduct WHERE userid = '" . getCurrentIdUser() . "' $where_vendor AND subcategory_apikey = 'SMM' GROUP BY category UNION ALL SELECT name AS category FROM mscategory WHERE servicetype = 'SMM' AND userid = '" . getCurrentIdUser() . "') a ORDER BY a.category ASC")->result();

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Transaksi Media Sosial';
        $data['content'] = 'transaction/smm';
        $data['status'] = $status;
        $data['status_payment'] = $status_payment;
        $data['platform'] = $platform;
        $data['category'] = $category;

        return $this->load->view('master', $data);
    }

    public function datatables_smm()
    {
        try {
            if (isLogin() && (isUser() && $this->user->companycategory != 'PPOB' && getCurrentUser()->licenseid != null && getCurrentUser()->licenseid != 2)) {
                $data = array();
                $date = getPost('date');
                $transactionstatus = getPost('status');
                $paymentstatus = getPost('paymentstatus');
                $transactioncode = getPost('transactioncode');
                $platform = getPost('platform');
                $category = getPost('category');
                $product = getPost('product');

                $datatable = $this->datatables->make('TrOrder', 'QueryDatatables', 'SearchDatatables');
                $currentiduser = getCurrentIdUser();

                $where = array(
                    "(c.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true,
                    "(a.subcategory_apikey = 'SMM' OR b.subcategory_apikey = 'SMM') =" => true,
                    'a.type' => 'SMM'
                );

                if ($date != null && isset(explode(' - ', $date)[0]) && isset(explode(' - ', $date)[1])) {
                    $startdate = explode(' - ', $date)[0];
                    $startdate = date('Y-m-d', strtotime($startdate));

                    $enddate = explode(' - ', $date)[1];
                    $enddate = date('Y-m-d', strtotime($enddate));

                    $where['DATE(a.createddate) >='] = $startdate;
                    $where['DATE(a.createddate) <='] = $enddate;
                } else {
                    $where['DATE(a.createddate) >='] = date('Y-m-01');
                    $where['DATE(a.createddate) <='] = date('Y-m-t');
                }

                if ($transactioncode != null) {
                    $where['a.clientcode'] = $transactioncode;
                }

                if ($transactionstatus != null) {
                    $where['a.status'] = $transactionstatus;
                }

                if ($paymentstatus != null) {
                    $where['a.status_payment'] = $paymentstatus;
                }

                if ($platform != null) {
                    $where['a.orderplatform'] = $platform;
                }

                if ($category != null) {
                    $where['b.category'] = $category;
                }

                if ($product != null) {
                    $where['b.id'] = $product;
                }

                foreach ($datatable->getData($where) as $key => $value) {
                    if (strtolower($value->status) == 'pending') {
                        $status = "<span class=\"badge badge-warning\">Pending</span>";
                    } else if (strtolower($value->status) == 'success' || strtolower($value->status) == 'completed') {
                        $status = "<span class=\"badge badge-success\">Success</span>";
                    } else if (strtolower($value->status) == 'in progress') {
                        $status = "<span class=\"badge badge-info\">In Progress</span>";
                    } else if (strtolower($value->status) == 'processing') {
                        $status = "<span class=\"badge badge-info\">Processing</span>";
                    } else {
                        $status = "<span class=\"badge badge-danger\">" . ucfirst($value->status) . "</span>";
                    }

                    if (strtolower($value->status_payment ?? '') == 'pending') {
                        $statuspayment = "<span class=\"badge badge-warning\">Pending</span>";
                    } else if (strtolower($value->status_payment ?? '') == 'sukses') {
                        $statuspayment = "<span class=\"badge badge-success\">Sukses</span>";
                    } else if (strtolower($value->status_payment ?? '') == 'gagal') {
                        $statuspayment = "<span class=\"badge badge-danger\">Gagal</span>";
                    } else {
                        $statuspayment = "<span class=\"badge badge-success\">Sukses</span>";
                    }

                    $createddate = new DateTime($value->createddate);
                    $updateddate = new DateTime($value->updateddate);

                    $waktu_proses = $createddate->diff($updateddate);

                    if (strtolower($value->status) == 'success' || strtolower($value->status) == 'completed') {
                        $action = "<button type=\"button\" class=\"btn btn-icon btn-danger btn-sm mb-1\" onclick=\"refundTransaction('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-undo\"></i>
                        </button> ";
                    } else if ($value->hostvendor == null && $value->status == 'pending' && $value->paymenttype != 'Otomatis') {
                        $action = "<button type=\"button\" class=\"btn btn-icon btn-danger btn-sm mb-1\" onclick=\"rejectTransaction('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-times\"></i>
                        </button>

                        <button type=\"button\" class=\"btn btn-icon btn-success btn-sm mb-1\" onclick=\"approveTransaction('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-check\"></i>
                        </button>";
                    } else if ($value->hostvendor != null && $value->status == 'pending' && $value->status_payment == 'pending' && ($value->paymenttype == 'Manual' || ($value->paymenttype == 'Otomatis' && $value->gatewayvendor == 'Notification Handler Services'))) {
                        $action = "<button type=\"button\" class=\"btn btn-icon btn-success btn-sm mb-1\" onclick=\"approveTransaction('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-check\"></i>
                        </button>
                        
                        <button type=\"button\" class=\"btn btn-icon btn-danger btn-sm mb-1\" onclick=\"rejectTransaction('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-times\"></i>
                        </button>";
                    } else if ($value->queuetransaction == 1) {
                        $action = "<button type=\"button\" class=\"btn btn-icon btn-danger btn-sm mb-1\" onclick=\"rejectTransaction('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-times\"></i>
                        </button>";
                    } else {
                        $action = "";
                    }

                    $action .= "<a href=\"" . base_url('transaction/smm/detail/' . stringEncryption('encrypt', $value->id)) . "\" class=\"btn btn-primary btn-sm mb-1\">
                        <i class=\"fa fa-eye\"></i>
                        Detail
                    </a>";

                    $buyer = null;
                    if ($value->userid != null) {
                        // Jika ada userid, buat link untuk modal detail user
                        $buyerName = $value->buyername ?? '- Guest -';
                        $buyer = "<a href=\"javascript:;\" class=\"fw-bold\" onclick=\"modalUserDetail('$value->userid')\">$buyerName</a>";

                        // Tambahkan link WhatsApp jika ada nomor telepon
                        if ($value->phonenumber_order != null) {
                            $buyer .= "<br><a href=\"https://wa.me/$value->phonenumber_order\" target=\"_blank\" class=\"text-success\">
                                <i class=\"fa fa-whatsapp\"></i> WhatsApp
                            </a>";
                        }
                    } else if ($value->phonenumber_order != null) {
                        $buyer = "<a href=\"https://wa.me/$value->phonenumber_order\" target=\"_blank\">
                            " . ($value->buyername ?? '- Guest -') . "
                        </a>";
                    } else {
                        $buyer = $value->buyername ?? '- Guest -';
                    }

                    $balancebefore = $value->userid ? $value->currentsaldo : 0;
                    $balanceafter = $value->userid ? $value->currentsaldo - $value->price : 0;

                    if (strtolower($value->status) == 'success' || strtolower($value->status) == 'completed') {
                        $processtime = $waktu_proses->format("(%a Hari %h Jam %i Menit %s Detik)");
                    } else {
                        $processtime = "";
                    }

                    $detail = array();
                    $detail[] = DateFormat($value->createddate, 'd F Y H:i:s') . "<br>" . DateFormat($value->updateddate, 'd F Y H:i:s') . "<br>$processtime";
                    $detail[] = "<a href=\"javascript:;\" class=\"fw-bold\" onclick=\"modalStruk('" . stringEncryption('encrypt', $value->id) . "')\">$value->clientcode</a><br>" . ($value->servercode ?? '-');
                    $detail[] = "<b>" . ($value->code ?? $value->productcode) . "</b><br>" . ($value->productname ?? ($value->productname_order ?? '- Layanan telah dihapus -'));
                    $detail[] = $value->target;
                    $detail[] = "Rp " . IDR($value->price) . " - <b>" . IDR($value->qty) . "</b><br>" . "<span class=\"text-success\">Rp " . IDR($value->profit) . "</span>";
                    $detail[] = "<b>" . strtoupper($value->orderplatform) . "</b> - " . $buyer . "<br>" . "<span class=\"text-danger\">Rp " . IDR($balancebefore) . " <i class=\"fa fa-arrow-right\"></i> Rp " . IDR($balanceafter) . "</span>";
                    $detail[] = IDR($value->startcount);
                    $detail[] = IDR($value->remain);
                    $detail[] = $status;
                    $detail[] = $statuspayment;
                    $detail[] = $action;

                    $data[] = $detail;
                }

                return $datatable->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function pascabayar()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory == 'SMM') {
            return redirect(base_url('dashboard'));
        }

        $currentiduser = getCurrentIdUser();

        $status = $this->trorder->select('a.status')
            ->join('msusers b', 'b.id = a.userid', 'LEFT')
            ->join('msproduct c', 'c.id = a.serviceid')
            ->where('c.subcategory_apikey', 'PASCABAYAR')
            ->group_by('a.status')
            ->result(array(
                "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true
            ));

        $status_payment = $this->trorder->select('a.status_payment')
            ->join('msusers b', 'b.id = a.userid', 'LEFT')
            ->join('msproduct c', 'c.id = a.serviceid')
            ->where('c.subcategory_apikey', 'PASCABAYAR')
            ->group_by('a.status_payment')
            ->result(array(
                'a.status_payment !=' => null,
                "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true
            ));

        $platform = $this->trorder->select('a.orderplatform')
            ->join('msusers b', 'b.id = a.userid', 'LEFT')
            ->join('msproduct c', 'c.id = a.serviceid')
            ->where('c.subcategory_apikey', 'PASCABAYAR')
            ->group_by('a.orderplatform')
            ->result(array(
                'a.orderplatform !=' => null,
                "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true
            ));

        $where_vendor = "";
        if (getCurrentUser()->multivendor != 1) {
            $currentvendor = getCurrentVendor('PPOB', $currentiduser);
            $where_vendor = "AND vendor = '$currentvendor' AND vendorid IS NULL";
        } else {
            $where_vendor = "AND vendorid IS NOT NULL AND vendorenabled = 1";
        }

        $category = $this->db->query("SELECT category FROM msproduct WHERE userid = '" . $currentiduser . "' $where_vendor AND subcategory_apikey = 'PASCABAYAR' GROUP BY category ORDER BY category ASC")->result();

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Transaksi Pascabayar';
        $data['content'] = 'transaction/pascabayar';
        $data['status'] = $status;
        $data['status_payment'] = $status_payment;
        $data['platform'] = $platform;
        $data['category'] = $category;

        return $this->load->view('master', $data);
    }

    public function datatables_pascabayar()
    {
        try {
            if (isLogin() && (isUser() && $this->user->companycategory != 'SMM' && getCurrentUser()->licenseid != null && getCurrentUser()->licenseid != 3)) {
                $data = array();
                $date = getPost('date');
                $transactionstatus = getPost('status');
                $transactionpaymentstatus = getPost('paymentstatus');
                $transactioncode = getPost('transactioncode');
                $platform = getPost('platform');
                $category = getPost('category');
                $brand = getPost('brand');
                $product = getPost('product');

                $datatable = $this->datatables->make('TrOrder', 'QueryDatatables', 'SearchDatatables');
                $currentiduser = getCurrentIdUser();

                $where = array(
                    "(c.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true,
                    "(a.subcategory_apikey = 'PASCABAYAR' OR b.subcategory_apikey = 'PASCABAYAR') =" => true,
                    'a.type' => 'PPOB'
                );

                if ($date != null && isset(explode(' - ', $date)[0]) && isset(explode(' - ', $date)[1])) {
                    $startdate = explode(' - ', $date)[0];
                    $startdate = date('Y-m-d', strtotime($startdate));

                    $enddate = explode(' - ', $date)[1];
                    $enddate = date('Y-m-d', strtotime($enddate));

                    $where['DATE(a.createddate) >='] = $startdate;
                    $where['DATE(a.createddate) <='] = $enddate;
                } else {
                    $where['DATE(a.createddate) >='] = date('Y-m-01');
                    $where['DATE(a.createddate) <='] = date('Y-m-t');
                }

                if ($transactioncode != null) {
                    $where['a.clientcode'] = $transactioncode;
                }

                if ($transactionstatus != null) {
                    $where['a.status'] = $transactionstatus;
                }

                if ($transactionpaymentstatus != null) {
                    $where['a.status_payment'] = $transactionpaymentstatus;
                }

                if ($platform != null) {
                    $where['a.orderplatform'] = $platform;
                }

                if ($category != null) {
                    $where['b.category'] = $category;
                }

                if ($brand != null) {
                    $where['b.brand'] = $brand;
                }

                if ($product != null) {
                    $where['b.id'] = $product;
                }

                foreach ($datatable->getData($where) as $key => $value) {
                    if (strtolower($value->status) == 'pending') {
                        $status = "<span class=\"badge badge-warning\">Pending</span>";
                    } else if (strtolower($value->status) == 'success' || strtolower($value->status) == 'sukses') {
                        $status = "<span class=\"badge badge-success\">Success</span>";
                    } else {
                        $status = "<span class=\"badge badge-danger\">" . ucfirst($value->status) . "</span>";
                    }

                    if (strtolower($value->status_payment ?? '') == 'pending') {
                        $statuspayment = "<span class=\"badge badge-warning\">Pending</span>";
                    } else if (strtolower($value->status_payment ?? '') == 'sukses') {
                        $statuspayment = "<span class=\"badge badge-success\">Sukses</span>";
                    } else if (strtolower($value->status_payment ?? '') == 'gagal' || strtolower($value->status_payment ?? '') == 'expired') {
                        $statuspayment = "<span class=\"badge badge-danger\">Gagal</span>";
                    } else {
                        $statuspayment = "<span class=\"badge badge-secondary\">" . ucwords(strtolower($value->status_payment ?? '-')) . "</span>";
                    }

                    $createddate = new DateTime($value->createddate);
                    $updateddate = new DateTime($value->updateddate);

                    $waktu_proses = $createddate->diff($updateddate);

                    if (strtolower($value->status) == 'success'  || strtolower($value->status) == 'sukses') {
                        $action = "<button type=\"button\" class=\"btn btn-icon btn-danger btn-sm mb-1 me-1\" onclick=\"refundTransaction('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-undo\"></i>
                        </button>";
                    } else {
                        $action = "";
                    }

                    $action .= "<a href=\"" . base_url('transaction/pascabayar/detail/' . stringEncryption('encrypt', $value->id)) . "\" class=\"btn btn-primary btn-sm mb-1 me-1 me-1\">
                        <i class=\"fa fa-eye\"></i>
                        Detail
                    </a>";

                    $buyer = null;
                    if ($value->userid != null) {
                        // Jika ada userid, buat link untuk modal detail user
                        $buyerName = $value->buyername ?? '- Guest -';
                        $buyer = "<a href=\"javascript:;\" class=\"fw-bold\" onclick=\"modalUserDetail('$value->userid')\">$buyerName</a>";

                        // Tambahkan link WhatsApp jika ada nomor telepon
                        if ($value->phonenumber_order != null) {
                            $buyer .= "<br><a href=\"https://wa.me/$value->phonenumber_order\" target=\"_blank\" class=\"text-success\">
                                <i class=\"fa fa-whatsapp\"></i> WhatsApp
                            </a>";
                        }
                    } else if ($value->phonenumber_order != null) {
                        $buyer = "<a href=\"https://wa.me/$value->phonenumber_order\" target=\"_blank\">
                            " . ($value->buyername ?? '- Guest -') . "
                        </a>";
                    } else {
                        $buyer = $value->buyername ?? '- Guest -';
                    }

                    $balancebefore = $value->userid ? $value->currentsaldo : 0;
                    $balanceafter = $value->userid ? $value->currentsaldo - $value->price : 0;

                    if (strtolower($value->status) == 'success' || strtolower($value->status) == 'sukses') {
                        $processtime = $waktu_proses->format("(%a Hari %h Jam %i Menit %s Detik)");
                    } else {
                        $processtime = "";
                    }

                    $detail = array();
                    $detail[] = DateFormat($value->createddate, 'd F Y H:i:s') . "<br>" . DateFormat($value->updateddate, 'd F Y H:i:s') . "<br>$processtime";
                    $detail[] = "<a href=\"javascript:;\" class=\"fw-bold\" onclick=\"modalStruk('" . stringEncryption('encrypt', $value->id) . "')\">$value->clientcode</a><br>" . ($value->servercode ?? '-');
                    $detail[] = "<b>" . ($value->code ?? $value->productcode) . "</b><br>" . ($value->productname ?? ($value->productname_order ?? '- Layanan telah dihapus -'));
                    $detail[] = $value->target;
                    $detail[] = "Rp " . IDR($value->price) . "<br>" . "<span class=\"text-success\">Rp " . IDR($value->profit) . "</span>";
                    $detail[] = "<b>" . strtoupper($value->orderplatform) . "</b> - " . $buyer . "<br>" . "<span class=\"text-danger\">Rp " . IDR($balancebefore) . " <i class=\"fa fa-arrow-right\"></i> Rp " . IDR($balanceafter) . "</span>";
                    $detail[] = !empty($value->sn) ? $value->sn : '-';
                    $detail[] = $status;
                    $detail[] = $statuspayment;
                    $detail[] = $action;

                    $data[] = $detail;
                }

                return $datatable->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function print($id)
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = stringEncryption('decrypt', $id);
            $currentiduser = getCurrentIdUser();

            $get = $this->trorder->select('a.createddate, a.status, a.clientcode, c.productname, a.price, a.updateddate, a.target, c.category_apikey, a.sn, d.domain, d.companyaddress, d.companyicon, d.companyname, d.strukcontent, a.startcount, a.remain, a.qty')
                ->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->join('msproduct c', 'c.id = a.serviceid', 'LEFT')
                ->join('msusers d', 'd.id = b.merchantid', 'LEFT')
                ->get(array(
                    'a.id' => $id,
                    "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true,
                ));

            if ($get->num_rows() == 0) {
                throw new Exception('Transaksi tidak ditemukan');
            }

            $row = $get->row();

            $data = array();
            $data['title'] = 'Server PPOB & SMM - Cetak Struk ' . $row->clientcode;
            $data['transaction'] = $row;

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('transaction/modalprint', $data, true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_print($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $id = stringEncryption('decrypt', $id);
        $price = getGet('price');
        $currentiduser = getCurrentIdUser();

        $get = $this->trorder->select('a.createddate, a.status, a.clientcode, c.productname, a.price, a.updateddate, a.target, c.category_apikey, a.sn, d.domain, d.companyaddress, d.companyicon, d.companyname, d.strukcontent, a.startcount, a.remain, a.qty')
            ->join('msusers b', 'b.id = a.userid', 'LEFT')
            ->join('msproduct c', 'c.id = a.serviceid', 'LEFT')
            ->join('msusers d', 'd.id = b.merchantid', 'LEFT')
            ->get(array(
                'a.id' => $id,
                "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true,
            ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('dashboard'));
        }

        $row = $get->row();

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Cetak Struk ' . $row->clientcode;
        $data['transaction'] = $row;
        $data['price'] = $price;

        $dompdf = new Dompdf();
        $dompdf->loadHtml($this->load->view('transaction/print', $data, true));

        $dompdf->setPaper('A5', 'P');

        $dompdf->render();

        $dompdf->stream("Struk Transaksi Prabayar", array("Attachment" => false));
    }

    private function send_notification($orderid, $userid, $phonenumber)
    {
        // Kirim Firebase notification untuk order
        sendFirebaseNotificationOrder($orderid, $userid);

        $apikeys_whatsapp = $this->apikeys_whatsapp->get(array(
            'userid' => $userid,
        ));

        if ($apikeys_whatsapp->num_rows() == 0) {
            return false;
        }

        $row = $apikeys_whatsapp->row();

        $wablasingateway = new WABlasinGateway(stringEncryption('decrypt', $row->apikey), stringEncryption('decrypt', $row->devicecode));

        $messagenotification = replaceParameterNotification($orderid, $userid);
        if ($messagenotification != null && $phonenumber != null) {
            $phonenumber = changePrefixPhone($phonenumber);

            $send = $wablasingateway->sendMessage($phonenumber, $messagenotification);

            if (!isset($send->RESULT) || (isset($send->RESULT) && $send->RESULT != 'OK')) {
                log_message_user('error', '[WHATSAPP SEND MESSAGE] Send notification failed: ' . json_encode($send), $userid);
            }

            return true;
        }

        return false;
    }

    public function process_refund()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = stringEncryption('decrypt', getPost('id'));

            if ($id == null) {
                throw new Exception('ID Transaksi tidak ditemukan');
            }

            $get = $this->trorder->select('a.userid, a.price, a.clientip, b.phonenumber, a.phonenumber_order, a.paymenttype, a.fee')
                ->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->get(array(
                    'a.id' => $id,
                    "(a.status = 'Completed' OR a.status = 'Success' OR a.status = 'sukses') =" => true,
                ));

            if ($get->num_rows() == 0) {
                throw new Exception('Transaksi tidak ditemukan');
            }

            $row = $get->row();

            $update = array();
            $update['status'] = 'gagal';

            $this->trorder->update(array(
                'id' => $id
            ), $update);

            pullTransaction($row->userid ?? $row->clientip);

            if ($row->userid != null) {
                $check_history_balance = $this->historybalance->total(array(
                    'userid' => $row->userid,
                    'type' => 'IN',
                    'orderid' => $id,
                ));

                if ($check_history_balance == 0) {
                    $currentbalance = getCurrentBalance($row->userid, true);

                    $inserthistorybalance = array();
                    $inserthistorybalance['userid'] = $row->userid;
                    $inserthistorybalance['type'] = 'IN';

                    if ($row->paymenttype == 'Otomatis' && $row->fee != null) {
                        $inserthistorybalance['nominal'] = $row->price - round($row->fee);
                    } else {
                        $inserthistorybalance['nominal'] = $row->price;
                    }

                    $inserthistorybalance['currentbalance'] = $currentbalance;
                    $inserthistorybalance['orderid'] = $id;
                    $inserthistorybalance['createdby'] = $row->userid;
                    $inserthistorybalance['createddate'] = getCurrentDate();

                    $this->historybalance->insert($inserthistorybalance);

                    $updateUser = array();

                    if ($row->paymenttype == 'Otomatis' && $row->fee != null) {
                        $updateUser['balance'] = $currentbalance + ($row->price - round($row->fee));
                    } else {
                        $updateUser['balance'] = $currentbalance + $row->price;
                    }

                    $this->msusers->update(array(
                        'id' => $row->userid
                    ), $updateUser);
                }
            }

            $this->send_notification($id, getCurrentIdUser(), $row->phonenumber ?? $row->phonenumber_order);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal melakukan refund transaksi');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Transaksi berhasil direfund');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function reorder()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = stringEncryption('decrypt', getPost('id'));
            $type = getPost('type');
            $target = getPost('target');

            if ($id == null) {
                throw new Exception('ID Transaksi tidak ditemukan');
            } else if (!in_array($type, array('regular', 'wrong'))) {
                throw new Exception('Tipe order tidak ditemukan');
            } else {
                if ($type == 'wrong' && $target == null) {
                    throw new Exception('Silahkan masukkan target terlebih dahulu');
                }
            }

            $get = $this->trorder->select('a.*')
                ->join('msproduct b', 'b.id = a.serviceid')
                ->total(array(
                    'a.id' => $id,
                    'a.paymenttype !=' => null,
                    'a.status_payment' => 'sukses',
                    'a.userid' => null,
                    "(LOWER(a.status) = 'gagal' OR LOWER(a.status) = 'failed' OR LOWER(a.status) = 'error') =" => true,
                    'b.vendor !=' => null,
                ));

            if ($get == 0) {
                throw new Exception('Transaksi tidak ditemukan');
            }

            $update = array();
            $update['status'] = 'pending';
            $update['servercode'] = null;

            if ($type == 'wrong' && $target !=  null) {
                $update['target'] = $target;
            }

            $this->trorder->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal melakukan order ulang transaksi');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Transaksi berhasil diorder ulang');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function detail_prabayar($transactionid)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory == 'SMM') {
            return redirect(base_url('dashboard'));
        }

        $transactionid = stringEncryption('decrypt', $transactionid);

        $transaction = $this->trorder->select('a.*, c.vendor AS vendor_product, b.name AS buyer_name, c.code AS product_code, c.productname')
            ->join('msusers b', 'b.id = a.userid', 'LEFT')
            ->join('msproduct c', 'c.id = a.serviceid', 'LEFT')
            ->get(array(
                'a.id' => $transactionid,
                "(b.merchantid = '" . getCurrentIdUser() . "' OR a.merchantid_order = '" . getCurrentIdUser() . "') =" => true,
                'a.category_apikey' => 'PPOB',
                'a.subcategory_apikey' => 'PRABAYAR'
            ));

        if ($transaction->num_rows() == 0) {
            return redirect(base_url('transaction/prabayar'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Detail Transaksi Prabayar';
        $data['content'] = 'transaction/detail_prabayar';
        $data['transaction'] = $transaction->row();

        return $this->load->view('master', $data);
    }

    public function detail_pascabayar($transactionid)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory == 'SMM') {
            return redirect(base_url('dashboard'));
        }

        $transactionid = stringEncryption('decrypt', $transactionid);

        $transaction = $this->trorder->select('a.*, c.vendor AS vendor_product, b.name AS buyer_name, c.code AS product_code, c.productname')
            ->join('msusers b', 'b.id = a.userid', 'LEFT')
            ->join('msproduct c', 'c.id = a.serviceid', 'LEFT')
            ->get(array(
                'a.id' => $transactionid,
                "(b.merchantid = '" . getCurrentIdUser() . "' OR a.merchantid_order = '" . getCurrentIdUser() . "') =" => true,
                'a.category_apikey' => 'PPOB',
                'a.subcategory_apikey' => 'PASCABAYAR'
            ));

        if ($transaction->num_rows() == 0) {
            return redirect(base_url('transaction/pascabayar'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Detail Transaksi Pascabayar';
        $data['content'] = 'transaction/detail_pascabayar';
        $data['transaction'] = $transaction->row();

        return $this->load->view('master', $data);
    }

    public function detail_smm($transactionid)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        } else if ($this->user->companycategory == 'PPOB') {
            return redirect(base_url('dashboard'));
        }

        $transactionid = stringEncryption('decrypt', $transactionid);

        $transaction = $this->trorder->select('a.*, c.vendor AS vendor_product, b.name AS buyer_name, c.code AS product_code, c.productname')
            ->join('msusers b', 'b.id = a.userid', 'LEFT')
            ->join('msproduct c', 'c.id = a.serviceid', 'LEFT')
            ->get(array(
                'a.id' => $transactionid,
                "(b.merchantid = '" . getCurrentIdUser() . "' OR a.merchantid_order = '" . getCurrentIdUser() . "') =" => true,
                'a.category_apikey' => 'SMM',
                'a.subcategory_apikey' => 'SMM'
            ));

        if ($transaction->num_rows() == 0) {
            return redirect(base_url('transaction/smm'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Detail Transaksi SMM';
        $data['content'] = 'transaction/detail_smm';
        $data['transaction'] = $transaction->row();

        return $this->load->view('master', $data);
    }

    public function detail_user()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $userId = getPost('userId');

            if (!$userId) {
                throw new Exception('ID pengguna tidak valid');
            }

            $userData = $this->msusers->get(['id' => $userId, 'merchantid' => getCurrentIdUser()])->row();

            if (!$userData) {
                throw new Exception('Data pengguna tidak ditemukan');
            }

            $totalTransactions = $this->trorder->total(['userid' => $userId]);

            $lastTransaction = $this->trorder->select('createddate')
                ->order_by('createddate', 'DESC')
                ->limit(1)
                ->get(['userid' => $userId]);

            $lastTransactionDate = null;
            if ($lastTransaction->num_rows() > 0) {
                $lastTransactionDate = $lastTransaction->row()->createddate;
            }

            $content = $this->load->view('transaction/detail_user', [
                'user' => $userData,
                'totalTransactions' => $totalTransactions,
                'lastTransactionDate' => $lastTransactionDate
            ], true);

            return JSONResponse([
                'RESULT'  => 'OK',
                'CONTENT' => $content
            ]);
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
