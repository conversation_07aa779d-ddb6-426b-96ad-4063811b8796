<?php
defined('BASEPATH') or exit('No direct script access allowed');

class EmailHook extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('MsEmailMarketingClick', 'emailmarketingclick');
    }

    public function index($token = null)
    {
        try {
            if ($token == null) {
                show_404();
                return;
            }

            // Get userid from query parameter
            $encrypted_userid = $this->input->get('userid');
            if ($encrypted_userid == null) {
                show_404();
                return;
            }

            // Decrypt userid
            $userid = stringEncryption('decrypt', $encrypted_userid);
            if ($userid == null) {
                show_404();
                return;
            }

            // Verify merchant exists
            $merchant = $this->msusers->get(array('id' => $userid))->row();
            if (!$merchant) {
                show_404();
                return;
            }

            // Decrypt token (token sudah di-urldecode otomatis oleh CodeIgniter)
            $token_data = json_decode(stringEncryption('decrypt', $token), true);
            if (!$token_data || !isset($token_data['userid']) || !isset($token_data['merchantid'])) {
                show_404();
                return;
            }

            // Verify token data
            if ($token_data['merchantid'] != $userid) {
                show_404();
                return;
            }

            // Get recipient data
            $recipient = $this->msusers->get(array('id' => $token_data['userid']))->row();
            if (!$recipient) {
                show_404();
                return;
            }

            // Check if click already recorded (prevent duplicate clicks)
            $existing_click = $this->emailmarketingclick->get(array(
                'userid' => $token_data['userid'],
                'merchantid' => $token_data['merchantid'],
                'broadcast_id' => $token_data['broadcast_id'] ?? null,
                'DATE(createddate)' => date('Y-m-d')
            ))->row();

            if (!$existing_click) {
                // Record the click
                $insert = array(
                    'userid' => $token_data['userid'],
                    'merchantid' => $token_data['merchantid'],
                    'user_email' => $recipient->email,
                    'user_name' => $recipient->name,
                    'ip_address' => get_client_ip(),
                    'user_agent' => $this->input->user_agent(),
                    'createddate' => getCurrentDate(),
                    'createdby' => $token_data['userid']
                );

                // Add broadcast_id if available
                if (isset($token_data['broadcast_id'])) {
                    $insert['broadcast_id'] = $token_data['broadcast_id'];
                }

                $this->emailmarketingclick->insert($insert);
            }

            // Redirect to merchant's main page or a specific landing page
            $redirect_url = $merchant->domain ? 'https://' . $merchant->domain : base_url();

            // Add tracking parameter to URL
            $redirect_url .= (strpos($redirect_url, '?') !== false ? '&' : '?') . 'utm_source=email&utm_medium=email_marketing&utm_campaign=broadcast';

            redirect($redirect_url);
        } catch (Exception $e) {
            log_message('error', '[EMAIL HOOK] Error: ' . $e->getMessage());
            show_404();
        }
    }
}
