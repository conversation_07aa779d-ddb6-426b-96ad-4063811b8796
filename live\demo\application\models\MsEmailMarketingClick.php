<?php
defined('BASEPATH') or die('No direct script access allowed!');

class MsEmailMarketingClick extends MY_Model
{
    protected $table = 'msemailmarketingclick';
    public $SearchDatatables = array('user_email', 'user_name', 'ip_address');

    public function QueryDatatables()
    {
        $this->db->select('a.*, b.name as merchant_name')
            ->from($this->table . ' a')
            ->join('msusers b', 'a.merchantid = b.id', 'LEFT')
            ->order_by('a.createddate', 'DESC');

        return $this;
    }
}
