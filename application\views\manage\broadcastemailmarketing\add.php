<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">
            Buat Broadcast Email Marketing
        </h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('manage/broadcastemailmarketing') ?>" class="text-gray-600 text-hover-primary">Broadcast Email Marketing</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Buat Broadcast</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->

    <!--begin::Button-->
    <a href="<?= base_url('manage/broadcastemailmarketing') ?>" class="btn btn-danger fw-bold">Kembali</a>
    <!--end::Button-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <!--begin::Form Column-->
        <div class="col-md-8">
            <div class="card mb-5">
                <div class="card-header">
                    <h3 class="card-title">Form Buat Broadcast</h3>
                </div>

                <form id="form-add-broadcast" autocomplete="off">
                    <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>" />
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-10">
                                    <label for="title" class="required form-label">Judul Broadcast</label>
                                    <input type="text" class="form-control" id="title" name="title" placeholder="Masukkan judul broadcast">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-10">
                                    <label for="subject" class="required form-label">Subject Email</label>
                                    <input type="text" class="form-control" id="subject" name="subject" placeholder="Masukkan subject email">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-10">
                                    <label class="required form-label">Pilih Penerima</label>

                                    <!--begin::Recipient Options-->
                                    <div class="card border">
                                        <div class="card-body">
                                            <div class="form-check mb-4">
                                                <input class="form-check-input" type="radio" name="recipient_type" value="all" id="recipient_all" checked>
                                                <label class="form-check-label fw-bold" for="recipient_all">
                                                    Kirim ke Semua Pelanggan (<?= count($users) ?> orang)
                                                </label>
                                            </div>

                                            <div class="form-check">
                                                <input class="form-check-input" type="radio" name="recipient_type" value="specific" id="recipient_specific">
                                                <label class="form-check-label fw-bold" for="recipient_specific">
                                                    Pilih Penerima Tertentu
                                                </label>
                                            </div>

                                            <div id="specific-recipients-section" class="mt-4" style="display: none;">
                                                <button type="button" class="btn btn-light-primary" onclick="openRecipientModal()">
                                                    <i class="fa fa-search me-2"></i>Pilih Penerima
                                                </button>
                                                <div id="selected-recipients" class="mt-3"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <!--end::Recipient Options-->
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-10">
                                    <label for="content" class="required form-label">Konten Email</label>
                                    <textarea id="content" name="content" placeholder="Masukkan konten email..."></textarea>

                                    <!--begin::Parameter Table-->
                                    <div class="card border border-dashed border-gray-300 mt-3">
                                        <div class="card-header">
                                            <h3 class="card-title fs-6 fw-bold">Daftar Parameter Email Marketing</h3>
                                        </div>
                                        <div class="card-body p-4">
                                            <div class="table-responsive">
                                                <table class="table table-row-bordered table-row-gray-100 gy-4 gs-4">
                                                    <thead>
                                                        <tr class="fw-bold fs-7 text-gray-800 border-bottom-2 border-gray-200">
                                                            <th class="min-w-100px">Parameter</th>
                                                            <th>Deskripsi</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody class="fs-7">
                                                        <tr>
                                                            <td class="fw-bold">${NAME}</td>
                                                            <td>Menampilkan nama lengkap pelanggan</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-bold">${EMAIL}</td>
                                                            <td>Menampilkan alamat email pelanggan</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-bold">${BALANCE}</td>
                                                            <td>Menampilkan saldo saat ini pelanggan</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-bold">${USERNAME}</td>
                                                            <td>Menampilkan username pelanggan</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-bold">${PHONENUMBER}</td>
                                                            <td>Menampilkan nomor telepon pelanggan</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-bold">${COMPANYNAME}</td>
                                                            <td>Menampilkan nama perusahaan</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-bold">${COMPANYADDRESS}</td>
                                                            <td>Menampilkan alamat perusahaan</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-bold">${UNSUBSCRIBE_LINK}</td>
                                                            <td>Link untuk berhenti berlangganan email</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="fw-bold">${HOOK_URL}</td>
                                                            <td>Link tracking untuk menganalisa ketertarikan pelanggan (redirect ke domain merchant)</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <!--end::Parameter Table-->
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer">
                        <div class="d-flex justify-content-end">
                            <button type="submit" name="send_now" value="0" class="btn btn-warning me-3">
                                <span class="indicator-label">Simpan sebagai Draft</span>
                                <span class="indicator-progress">Please wait...
                                    <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                </span>
                            </button>
                            <button type="submit" name="send_now" value="1" class="btn btn-primary">
                                <span class="indicator-label">Kirim Sekarang</span>
                                <span class="indicator-progress">Please wait...
                                    <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                                </span>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        <!--end::Form Column-->

        <!--begin::Preview Column-->
        <div class="col-md-4">
            <div class="card mb-5">
                <div class="card-header">
                    <h3 class="card-title">Preview Email</h3>
                </div>
                <div class="card-body">
                    <div class="border rounded p-4" style="background-color: #f8f9fa; min-height: 400px;">
                        <div class="mb-4">
                            <div class="fw-bold text-muted mb-2">Subject:</div>
                            <div id="preview-subject" class="fw-bold">Subject akan muncul di sini...</div>
                        </div>

                        <div class="mb-4">
                            <div class="fw-bold text-muted mb-2">Kepada:</div>
                            <div id="preview-recipients" class="text-muted">Penerima akan muncul di sini...</div>
                        </div>

                        <hr>

                        <div>
                            <div class="fw-bold text-muted mb-2">Konten:</div>
                            <div id="preview-content" class="border rounded p-3" style="background-color: white; min-height: 300px;">
                                Konten email akan muncul di sini...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--end::Preview Column-->
    </div>
</div>

<!--begin::Recipient Modal-->
<div class="modal fade" id="recipientModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">Pilih Penerima Email</h3>
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                    <span class="svg-icon svg-icon-1"></span>
                </div>
            </div>
            <div class="modal-body">
                <div class="mb-5">
                    <input type="text" class="form-control" id="search-recipients" placeholder="Cari nama atau email...">
                </div>

                <div class="mb-4">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="modal-select-all">
                        <label class="form-check-label fw-bold" for="modal-select-all">
                            Pilih Semua
                        </label>
                    </div>
                </div>

                <div id="recipients-list" style="max-height: 400px; overflow-y: auto;">
                    <?php if (!empty($users)) : ?>
                        <?php foreach ($users as $user) : ?>
                            <div class="recipient-item form-check mb-3 d-flex align-items-center" data-name="<?= strtolower($user->name) ?>" data-email="<?= strtolower($user->email) ?>">
                                <input class="form-check-input modal-recipient-checkbox" type="checkbox" value="<?= $user->id ?>" id="modal_user_<?= $user->id ?>" data-name="<?= $user->name ?>" data-email="<?= $user->email ?>">
                                <label class="ms-3 form-check-label w-100" for="modal_user_<?= $user->id ?>">
                                    <div class="d-flex align-items-center">
                                        <div class="symbol symbol-40px me-3">
                                            <div class="symbol-label bg-light-primary text-primary fw-bold">
                                                <?= strtoupper(substr($user->name, 0, 1)) ?>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="fw-bold"><?= $user->name ?></div>
                                            <div class="text-muted fs-7"><?= $user->email ?></div>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        <?php endforeach; ?>
                    <?php else : ?>
                        <div class="text-center text-muted py-5">
                            <i class="fa fa-users fs-3x mb-3"></i>
                            <p>Belum ada pelanggan yang terdaftar</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Batal</button>
                <button type="button" class="btn btn-primary" onclick="selectRecipients()">Pilih Penerima</button>
            </div>
        </div>
    </div>
</div>
<!--end::Recipient Modal-->

<script>
    var selectedRecipients = [];
    var allUsers = <?= json_encode($users) ?>;
    var editor;

    $(document).ready(function() {
        // Initialize ClassicEditor
        ClassicEditor
            .create(document.querySelector('#content'))
            .then(response => {
                editor = response;

                // Listen for content changes
                editor.model.document.on('change:data', () => {
                    updatePreview();
                });

                // Initialize preview after editor is ready
                updatePreview();
            })
            .catch(responseError => {
                console.error('Error initializing editor:', responseError);
            });

        // Set default recipients to all users
        updateRecipientsPreview();

        // Bind events
        $('#title, #subject').on('input', updatePreview);
        $('input[name="recipient_type"]').change(handleRecipientTypeChange);
    });

    // Handle recipient type change
    function handleRecipientTypeChange() {
        var type = $('input[name="recipient_type"]:checked').val();

        if (type === 'specific') {
            $('#specific-recipients-section').show();
        } else {
            $('#specific-recipients-section').hide();
            selectedRecipients = [];
            updateSelectedRecipientsDisplay();
        }

        updateRecipientsPreview();
    }

    // Open recipient modal
    function openRecipientModal() {
        $('#recipientModal').modal('show');

        // Pre-select already selected recipients
        $('.modal-recipient-checkbox').prop('checked', false);
        selectedRecipients.forEach(function(id) {
            $('#modal_user_' + id).prop('checked', true);
        });

        updateModalSelectAll();
    }

    // Search functionality in modal
    $('#search-recipients').on('input', function() {
        var searchTerm = $(this).val().toLowerCase();

        $('.recipient-item').each(function() {
            var name = $(this).data('name');
            var email = $(this).data('email');

            if (name.includes(searchTerm) || email.includes(searchTerm)) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    });

    // Modal select all functionality
    $('#modal-select-all').change(function() {
        var isChecked = this.checked;
        $('.recipient-item:visible .modal-recipient-checkbox').prop('checked', isChecked);
    });

    $('.modal-recipient-checkbox').change(function() {
        updateModalSelectAll();
    });

    function updateModalSelectAll() {
        var visibleCheckboxes = $('.recipient-item:visible .modal-recipient-checkbox');
        var checkedCheckboxes = $('.recipient-item:visible .modal-recipient-checkbox:checked');

        $('#modal-select-all').prop('checked', visibleCheckboxes.length > 0 && visibleCheckboxes.length === checkedCheckboxes.length);
    }

    // Select recipients from modal
    function selectRecipients() {
        selectedRecipients = [];

        $('.modal-recipient-checkbox:checked').each(function() {
            selectedRecipients.push($(this).val());
        });

        updateSelectedRecipientsDisplay();
        updateRecipientsPreview();
        $('#recipientModal').modal('hide');
    }

    // Update selected recipients display
    function updateSelectedRecipientsDisplay() {
        var container = $('#selected-recipients');
        container.empty();

        if (selectedRecipients.length === 0) {
            container.html('<div class="text-muted">Belum ada penerima yang dipilih</div>');
            return;
        }

        var html = '<div class="fw-bold mb-2">Penerima yang dipilih (' + selectedRecipients.length + '):</div>';
        html += '<div class="d-flex flex-wrap gap-2">';

        selectedRecipients.forEach(function(id) {
            var user = allUsers.find(u => u.id == id);
            if (user) {
                html += '<span class="badge badge-light-primary">' + user.name + ' (' + user.email + ')</span>';
            }
        });

        html += '</div>';
        container.html(html);
    }

    // Update preview
    function updatePreview() {
        var title = $('#title').val() || 'Judul broadcast akan muncul di sini...';
        var subject = $('#subject').val() || 'Subject akan muncul di sini...';
        var content = '';

        // Get content from ClassicEditor if available
        if (editor) {
            content = editor.getData() || 'Konten email akan muncul di sini...';
        } else {
            content = 'Konten email akan muncul di sini...';
        }

        $('#preview-subject').text(subject);
        $('#preview-content').html(content);
    }

    // Update recipients preview
    function updateRecipientsPreview() {
        var type = $('input[name="recipient_type"]:checked').val();
        var recipientText = '';

        if (type === 'all') {
            recipientText = 'Semua pelanggan (' + allUsers.length + ' orang)';
        } else {
            if (selectedRecipients.length === 0) {
                recipientText = 'Belum ada penerima yang dipilih';
            } else {
                recipientText = selectedRecipients.length + ' penerima dipilih';
            }
        }

        $('#preview-recipients').text(recipientText);
    }

    // Form submission
    $('#form-add-broadcast').submit(function(e) {
        e.preventDefault();

        var btn = $(e.originalEvent.submitter);
        var sendNow = btn.val();

        btn.attr('data-kt-indicator', 'on');

        // Create FormData object
        var formData = new FormData(this);

        // Add content from ClassicEditor
        if (editor) {
            formData.append('content', editor.getData());
        }

        // Add send_now parameter
        formData.append('send_now', sendNow);

        // Add recipients based on type
        var recipientType = $('input[name="recipient_type"]:checked').val();
        if (recipientType === 'all') {
            // Add all user IDs
            allUsers.forEach(function(user) {
                formData.append('recipients[]', user.id);
            });
        } else {
            // Add selected recipients
            selectedRecipients.forEach(function(id) {
                formData.append('recipients[]', id);
            });
        }

        $.ajax({
            url: '<?= base_url(uri_string() . '/process') ?>',
            method: 'POST',
            dataType: 'json',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                btn.removeAttr('data-kt-indicator');

                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Berhasil',
                        text: response.MESSAGE,
                        icon: 'success'
                    }).then(function(result) {
                        return window.location.href = '<?= base_url('manage/broadcastemailmarketing') ?>';
                    });
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            }
        }).fail(function() {
            btn.removeAttr('data-kt-indicator');

            return Swal.fire({
                title: 'Gagal',
                text: 'Server sedang sibuk silahkan coba lagi nanti',
                icon: 'error'
            });
        });
    });
</script>