<?php

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Dompdf\Dompdf;

defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsProduct $msproduct
 * @property TrOrder $trorder
 * @property MsUsers $msusers
 * @property ApiKeys $apikeys
 * @property DisabledCategory $disabledcategory
 * @property DisabledBrand $disabledbrand
 * @property MsCategory $mscategory
 * @property ProductFavourite $productfavourite
 * @property Datatables $datatables
 * @property CI_DB_mysqli_driver $db
 * @property ProductPriceLog $productpricelog
 * @property DisabledBrand $disabledbrand
 * @property MsRole $msrole
 * @property MsRoleDiscount $msrolediscount
 * @property MsRoleDiscountAdv $msrolediscountadv
 * @property HistoryBalance $historybalance
 * @property MsContact $mscontact
 * @property ApiKeys_WhatsApp $apikeys_whatsapp
 * @property MsVendorDetail $msvendordetail
 * @property MsStockproduct $msstockproduct
 * @property MsStockproductDetail $msstockproductdetail
 */
class PPOB extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsProduct', 'msproduct');
        $this->load->model('MsRole', 'msrole');
        $this->load->model('MsRoleDiscount', 'msrolediscount');
        $this->load->model('MsRoleDiscountAdv', 'msrolediscountadv');
        $this->load->model('TrOrder', 'trorder');
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('ApiKeys', 'apikeys');
        $this->load->model('DisabledCategory', 'disabledcategory');
        $this->load->model('MsCategory', 'mscategory');
        $this->load->model('ProductFavourite', 'productfavourite');
        $this->load->model('ProductPriceLog', 'productpricelog');
        $this->load->model('DisabledBrand', 'disabledbrand');
        $this->load->model('MsContact', 'mscontact');
        $this->load->model('HistoryBalance', 'historybalance');
        $this->load->model('ApiKeys_WhatsApp', 'apikeys_whatsapp');
        $this->load->model('MsVendorDetail', 'msvendordetail');
        $this->load->model('MsStockproduct', 'msstockproduct');
        $this->load->model('MsStockproductDetail', 'msstockproductdetail');
    }

    public function services()
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $disabled = $this->disabledcategory->result(array(
            'userid' => $this->merchant->id,
            'category_apikey' => 'PPOB'
        ));

        $disabled_category = array();
        foreach ($disabled as $key => $value) {
            $disabled_category[] = $value->categoryname;
        }

        $where = array(
            'a.userid' => $this->merchant->id,
            'a.category_apikey' => 'PPOB',
        );

        if ($this->merchant->multivendor != 1) {
            $ppob = getCurrentVendor('PPOB', $this->merchant->id);
            $where["(a.vendor = '$ppob' OR a.vendor IS NULL) = "] = true;
            $where['a.vendorid'] = null;
        } else {
            $where["((a.vendorid IS NOT NULL AND a.vendorenabled = 1) OR a.vendor IS NULL) ="] = true;
        }

        $category = $this->msproduct->select('a.category')
            ->where_not_in('a.category', $disabled_category)
            ->group_by('a.category')
            ->result($where);

        $data = array();
        $data['title'] = $this->merchant->companyname . ' | Daftar Harga';
        $data['content'] = 'ppob/services';
        $data['category'] = $category;

        return viewTemplate($this->merchant->id, "master", $data);
    }

    public function datatables_services_ppob()
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            if (isLogin()) {
                $data = array();

                $datatable = $this->datatables->make('MsProduct', 'QueryDatatables', 'SearchDatatables');

                $where = array(
                    'a.userid' => $this->merchant->id,
                    'a.category_apikey' => 'PPOB',
                    'b.id' => null,
                    'a.status' => 1
                );

                if ($this->merchant->multivendor != 1) {
                    $vendor = getCurrentVendor('PPOB', $this->merchant->id);
                    $where["(a.vendor = '$vendor' OR a.vendor IS NULL) ="] = true;
                    $where['a.vendorid'] = null;
                } else {
                    $where["((a.vendorid IS NOT NULL AND a.vendorenabled = 1) OR a.vendor IS NULL) ="] = true;
                }

                $category = getPost('category');
                $brand = getPost('brand');

                if ($category != '') {
                    $where['a.category'] = $category;
                }

                if ($brand != '') {
                    $where['a.brand'] = $brand;
                }

                $currenttheme = getCurrentThemeConfiguration($this->merchant->id);

                if ($currenttheme != 'HighAdmin-TopNav' && $currenttheme != 'Dason-TopNav' && $currenttheme != 'Able') {
                    $label = "label label";
                } else if ($currenttheme == 'Dason-TopNav' || $currenttheme == 'Able') {
                    $label = "badge bg";
                } else {
                    $label = "badge badge";
                }

                $currentuser = getCurrentUser();

                if ($currentuser->roleid == null) {
                    $getrole = $this->msrole->get(array(
                        'createdby' => $this->merchant->id,
                        'isdefault' => '1'
                    ))->row();
                } else {
                    $getrole = $this->msrole->get(array(
                        'createdby' => $this->merchant->id,
                        'id' => $currentuser->roleid
                    ))->row();
                }

                if ($getrole != null) {
                    if ($getrole->discounttype == 'Simple') {
                        $getrolediscount = $this->msrolediscount->get(array(
                            'userid' => $this->merchant->id,
                            'roleid' => $getrole->id
                        ))->row();

                        $discount = $getrolediscount->trxdiscount ?? 0;
                    } else {
                        $discount = $this->msrolediscountadv->get(array(
                            'createdby' => $this->merchant->id,
                            'roleid' => $getrole->id,
                            '(servicetype = "Prabayar" OR servicetype = "Pascabayar") =' => true
                        ))->result();
                    }
                } else {
                    $getrole = new stdClass();
                    $getrole->discounttype = 'Simple';
                    $getrole->id = 0;
                }

                foreach ($datatable->getData($where) as $key => $value) {
                    if ($value->status == 1) {
                        $status = "<span class=\"$label-success\">Normal</span>";
                    } else {
                        $status = "<span class=\"$label-danger\">Gangguan</span>";
                    }

                    $favourite = "";
                    if ($value->favouriteid == null) {
                        if ($currenttheme != 'HighAdmin-TopNav' && $currenttheme != 'Dason-TopNav' && $currenttheme != 'Able') {
                            $favourite = "<a href=\"javascript:;\" onclick=\"addFavourite('" . stringEncryption('encrypt', $value->id) . "')\"><i class=\"fa fa-star-o\"></i></a>";
                        } else {
                            $favourite = "<a href=\"javascript:;\" onclick=\"addFavourite('" . stringEncryption('encrypt', $value->id) . "')\"><i class=\"far fa-star\"></i></a>";
                        }
                    } else {
                        $favourite = "<a href=\"javascript:;\" onclick=\"removeFavourite('" . stringEncryption('encrypt', $value->id) . "')\"><i class=\"fa fa-star\"></i></a>";
                    }

                    $detail = array();
                    $detail[] = $favourite;
                    $detail[] = $value->code;
                    $detail[] = $value->productname;
                    $detail[] = $value->category;
                    $detail[] = $value->brand;
                    $detail[] = $value->description ?? '-';

                    if ($value->subcategory_apikey == 'PRABAYAR') {
                        if ($getrole->discounttype == 'Simple') {
                            $detail[] = IDR($value->price - $discount);
                        } else {
                            $found = false;

                            foreach ($discount as $val) {
                                if ($found) continue;

                                if ($val->startrange <= $value->price && $val->endrange >= $value->price && strtoupper($val->servicetype) == 'PRABAYAR') {
                                    if ($val->discounttype == 'Persentase') {
                                        $detail[] = IDR($value->price - ($value->price * $val->nominal / 100));

                                        $found = true;
                                    } else {
                                        $detail[] = IDR($value->price - $val->nominal);

                                        $found = true;
                                    }
                                }
                            }

                            if ($found == false) {
                                $detail[] = IDR($value->price);
                            }
                        }
                    } else {
                        if ($getrole->discounttype == 'Simple') {
                            $detail[] = IDR($value->admin + $value->profit - $discount);
                        } else {
                            $found = false;

                            foreach ($discount as $val) {
                                if ($found) continue;

                                if ($val->startrange <= $value->admin + $value->profit && $val->endrange >= $value->admin + $value->profit && strtoupper($val->servicetype) == 'PASCABAYAR') {
                                    if ($val->discounttype == 'Persentase') {
                                        $detail[] = IDR($value->admin + $value->profit - ($value->admin + $value->profit * $val->nominal / 100));

                                        $found = true;
                                    } else {
                                        $detail[] = IDR($value->admin + $value->profit - $val->nominal);

                                        $found = true;
                                    }
                                }
                            }

                            if ($found == false) {
                                $detail[] = IDR($value->admin + $value->profit);
                            }
                        }
                    }

                    $detail[] = $status;

                    $data[] = $detail;
                }

                return $datatable->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function prabayar()
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $disabled = $this->disabledcategory->result(array(
            'userid' => $this->merchant->id,
            'category_apikey' => 'PPOB',
        ));

        $disabled_category = array();
        foreach ($disabled as $key => $value) {
            $disabled_category[] = $value->categoryname;
        }

        $where = array(
            'userid' => $this->merchant->id,
            'subcategory_apikey' => 'PRABAYAR',
            'status' => 1
        );

        if ($this->merchant->multivendor != 1) {
            $vendor = getCurrentVendor('PPOB', $this->merchant->id);
            $where['vendor'] = $vendor;
            $where['vendorid'] = null;
        } else {
            $where['vendorid !='] = null;
            $where['vendorenabled'] = 1;
        }

        $categoryproduct = $this->db->select('category')
            ->from('msproduct')
            ->where($where)
            ->group_by('category')
            ->order_by('category', 'ASC');

        if (count($disabled_category) > 0) {
            $this->db->where_not_in('category', $disabled_category);
        }

        $categoryproduct = $this->db->get_compiled_select();

        $mscategory = $this->db->select('name AS category')
            ->from('mscategory')
            ->where(array(
                'userid' => $this->merchant->id
            ));

        $mscategory = $this->db->get_compiled_select();

        $category = $this->db->query("SELECT a.* FROM (($categoryproduct) UNION ($mscategory)) a ORDER BY a.category ASC")->result();

        $data = array();
        $data['title'] = $this->merchant->companyname . ' | Pesanan Baru';
        $data['content'] = 'ppob/prabayar';
        $data['category'] = $category;

        return viewTemplate($this->merchant->id, "master", $data);
    }

    public function fields_prabayar()
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            }

            $product = getPost('product');

            $disabled = $this->disabledcategory->result(array(
                'userid' => $this->merchant->id,
                'category_apikey' => 'PPOB'
            ));

            $disabled_category = array();
            foreach ($disabled as $key => $value) {
                $disabled_category[] = $value->categoryname;
            }

            $disabledbrand = $this->disabledbrand->result(array(
                'userid' => $this->merchant->id,
            ));

            $disabled_brand = array();
            foreach ($disabledbrand as $key => $value) {
                $disabled_brand[] = $value->brandname;
            }

            $where = array(
                'id' => $product,
                'userid' => $this->merchant->id,
                'subcategory_apikey' => 'PRABAYAR',
                'status' => 1
            );

            if ($this->merchant->multivendor != 1) {
                $vendor = getCurrentVendor('PPOB', $this->merchant->id);
                $where["(vendor = '$vendor' OR vendor IS NULL) ="] = true;
                $where['vendorid'] = null;
            } else {
                $where["((vendorid IS NOT NULL AND vendorenabled = 1) OR vendor IS NULL) ="] = true;
            }

            $get = $this->msproduct->where_not_in('category', $disabled_category)
                ->where_not_in('brand', $disabled_brand)
                ->get($where);

            if ($get->num_rows() == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            $row = $get->row();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => viewTemplate($this->merchant->id, "ppob/prabayar_fields", array(
                    'product' => $row
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_prabayar()
    {
        try {
            $this->db->trans_begin();

            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                $this->db->trans_rollback();

                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            }

            $product = getPost('product');
            $target = getPost('target');
            $zoneid = getPost('zoneid');
            $pin = getPost('pin');

            if ($product == null) {
                throw new Exception('Produk wajib diisi');
            } else if (!is_numeric($product)) {
                throw new Exception('Produk tidak ditemukan');
            } else if ($target == null) {
                throw new Exception('Target wajib diisi');
            } else if ($pin == null) {
                throw new Exception('PIN Transaksi wajib diisi');
            }

            $currentuser = getCurrentUser(null, true);

            if (stringEncryption('encrypt', $pin) != $currentuser->pin) {
                throw new Exception('PIN Transaksi yang anda masukkan salah');
            }

            $disabled = $this->disabledcategory->result(array(
                'userid' => $this->merchant->id,
                'category_apikey' => 'PPOB'
            ));

            $disabled_category = array();
            foreach ($disabled as $key => $value) {
                $disabled_category[] = $value->categoryname;
            }

            $disabledbrand = $this->disabledbrand->result(array(
                'userid' => $this->merchant->id,
            ));

            $disabled_brand = array();
            foreach ($disabledbrand as $key => $value) {
                $disabled_brand[] = $value->brandname;
            }

            $where = array(
                'id' => $product,
                'userid' => $this->merchant->id,
                'subcategory_apikey' => 'PRABAYAR'
            );

            if ($this->merchant->multivendor != 1) {
                $vendor = getCurrentVendor('PPOB', $this->merchant->id);
                $where["(vendor = '$vendor' OR vendor IS NULL) ="] = true;
                $where['vendorid'] = null;
            } else {
                $where["((vendorid IS NOT NULL AND vendorenabled = 1) OR vendor IS NULL) ="] = true;
            }

            $getproduct = $this->msproduct->where_not_in('category', $disabled_category)
                ->where_not_in('brand', $disabled_brand)
                ->get($where);

            $productRow = $getproduct->row();

            if ($getproduct->num_rows() == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            if ($productRow->status != 1) {
                throw new Exception('Produk sedang gangguan');
            }

            if ($productRow->isstock == 1) {
                $stock = $productRow->stock;

                if ($stock == 0) {
                    throw new Exception('Produk sedang kosong');
                }
            }

            if ($productRow->isdatabase == 1 && $productRow->databasecategory == 'Stock Product') {
                $databaseid = $productRow->databaseid;

                $stockproduct = $this->msstockproduct->get(array(
                    'id' => $databaseid
                ))->row();

                if ($stockproduct == null) {
                    throw new Exception('Produk tidak tersedia');
                } else {
                    $available_stock = $this->msstockproductdetail->total(array(
                        'stockproductid' => $databaseid,
                        'status' => 'Tersedia'
                    ));

                    if ($available_stock == 0) {
                        throw new Exception('Produk sedang kosong');
                    }
                }
            }

            if ($currentuser->roleid == null) {
                $getrole = $this->msrole->get(array(
                    'createdby' => $this->merchant->id,
                    'isdefault' => '1'
                ))->row();
            } else {
                $getrole = $this->msrole->get(array(
                    'createdby' => $this->merchant->id,
                    'id' => $currentuser->roleid
                ))->row();
            }

            if ($getrole != null) {
                if ($getrole->discounttype == 'Simple') {
                    $getrolediscount = $this->msrolediscount->get(array(
                        'userid' => $this->merchant->id,
                        'roleid' => $getrole->id
                    ))->row();

                    $discount = $getrolediscount->trxdiscount ?? 0;
                } else {
                    $discount = $this->msrolediscountadv->get(array(
                        'createdby' => $this->merchant->id,
                        'roleid' => $getrole->id,
                        'servicetype' => 'Prabayar'
                    ))->result();
                }
            } else {
                $getrole = new stdClass();
                $getrole->discounttype = 'Simple';
                $discount = 0;
            }

            $fixproductprice = $productRow->price;

            if ($getrole->discounttype == 'Simple') {
                $fixproductprice = $productRow->price - $discount;
            } else {
                $found = false;

                foreach ($discount as $val) {
                    if ($found) continue;

                    if ($val->startrange <= $productRow->price && $val->endrange >= $productRow->price && strtoupper($val->servicetype) == 'PRABAYAR') {
                        if ($val->discounttype == 'Persentase') {
                            $fixproductprice = $productRow->price - ($productRow->price * $val->nominal / 100);

                            $found = true;
                        } else {
                            $fixproductprice = $productRow->price - $val->nominal;

                            $found = true;
                        }
                    }
                }
            }

            if ($currentuser->balance < $fixproductprice) {
                throw new Exception('Saldo anda tidak mencukupi');
            }

            $getpending = $this->trorder->total(array(
                'userid' => getCurrentIdUser(),
                'serviceid' => $productRow->id,
                'target' => $target,
                'status' => 'pending'
            ));

            if ($getpending > 0) {
                throw new Exception('Transaksi sedang diproses');
            }

            $queuetransaction = false;
            if ($productRow->vendor != null) {
                if (preg_match('/(mobile legends)|(mobilelegends)|(mobile legend)|(mobilelegend)/', strtolower($productRow->brand)) && $productRow->vendor == 'VIPayment') {
                    if ($zoneid == null) {
                        throw new Exception('Zone ID wajib diisi');
                    }
                }

                if ($this->merchant->multivendor != 1) {
                    $apikeys = getCurrentAPIKeys('PPOB', $this->merchant->id);
                    if (ENVIRONMENT == 'production' && $apikeys->balance < $productRow->vendorprice) {
                        $queuetransaction = true;
                    }
                } else {
                    if (getCurrentBalanceVendor($this->merchant->id, $productRow->vendorid) < $productRow->vendorprice) {
                        $queuetransaction = true;
                    }
                }
            }

            $originprice = $productRow->price;

            $potonganprofit = $originprice - $fixproductprice;

            if ($productRow->isstock == 1) {
                $this->msproduct->update(array(
                    'id' => $productRow->id
                ), array(
                    'stock' => $stock - 1
                ));
            }

            $insert = array();
            $insert['userid'] = getCurrentIdUser();
            $insert['clientcode'] = generateTransactionNumber('PRABAYAR');
            $insert['serviceid'] = $productRow->id;
            $insert['target'] = $target;
            $insert['price'] = $fixproductprice;
            $insert['profit'] = ($productRow->profit - $potonganprofit);
            $insert['currentsaldo'] = $currentuser->balance;
            $insert['status'] = 'pending';
            $insert['type'] = 'PPOB';
            $insert['queuetransaction'] = $queuetransaction ? 1 : 0;
            $insert['category_apikey'] = $productRow->category_apikey;
            $insert['subcategory_apikey'] = $productRow->subcategory_apikey;
            $insert['orderplatform'] = 'web';
            $insert['productcode'] = $productRow->code;
            $insert['productname_order'] = $productRow->productname;
            $insert['vendor'] = $productRow->vendor;
            $insert['merchantid_order'] = $this->merchant->id;
            $insert['status_payment'] = 'sukses';
            $insert['vendorid'] = $productRow->vendorid;

            if ($productRow->vendor != null) {
                if (preg_match('/(mobile legends)|(mobilelegends)|(mobile legend)|(mobilelegend)/', strtolower($productRow->brand)) && $productRow->vendor == 'VIPayment') {
                    $insert['zoneid'] = $zoneid;
                }
            }

            $this->trorder->insert($insert);
            $orderid = $this->db->insert_id();

            $contact = getPost('contact');

            if ($contact != null) {
                $insertcontact = array();
                $insertcontact['contactname'] = $contact;
                $insertcontact['content'] = $target;
                $insertcontact['userid'] = getCurrentIdUser();
                $insertcontact['contacttype'] = 'PPOB';

                $this->mscontact->insert($insertcontact);
            }

            $inserthistorybalance = array();
            $inserthistorybalance['userid'] = getCurrentIdUser();
            $inserthistorybalance['type'] = 'OUT';
            $inserthistorybalance['nominal'] = $fixproductprice;
            $inserthistorybalance['currentbalance'] = $currentuser->balance;
            $inserthistorybalance['orderid'] = $orderid;
            $inserthistorybalance['createdby'] = getCurrentIdUser();
            $inserthistorybalance['createddate'] = getCurrentDate();

            $this->historybalance->insert($inserthistorybalance);

            $update = array();
            $update['balance'] = $currentuser->balance - $fixproductprice;

            $this->msusers->update(array(
                'id' => getCurrentIdUser()
            ), $update);

            if ($this->db->trans_status() == FALSE) {
                throw new Exception('Transaksi gagal');
            }

            pullTransaction($this->merchant->id);

            if ($queuetransaction == false && $productRow->vendor != null && ENVIRONMENT == 'production') {
                if ($this->merchant->multivendor != 1) {
                    if ($vendor == 'Digiflazz') {
                        $digiflazz = new Digiflazz(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));

                        $servercode = generateTransactionNumber('ORDER');
                        $topup = $digiflazz->topup($productRow->code, $target, $servercode);

                        $result = json_decode($topup);

                        if (isset($result->data->status)) {
                            if ($result->data->status != 'Gagal') {
                                $update = array();
                                $update['servercode'] = $servercode;
                                $update['note'] = $result->data->message;
                                $update['sn'] = $result->data->sn;
                                $update['status'] = strtolower($result->data->status);
                                $update['jsonresponse'] = json_encode($result);

                                $this->trorder->update(array(
                                    'id' => $orderid
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($orderid, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Transaksi berhasil');
                            } else {
                                if ($result != null) {
                                    log_message_user('error', '[DIGIFLAZZ PRABAYAR] Response: ' . json_encode($result), $this->merchant->id);
                                }

                                $responsecode = getResponseCodeDigiflazz($result->data->rc);
                                throw new Exception($responsecode);
                            }
                        } else {
                            throw new Exception('Transaksi gagal');
                        }
                    } else if ($vendor == 'VIPayment') {
                        $vipayment = new VIPayment(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));

                        if ($productRow->vendor == 'VIPayment' && $productRow->type == 'game') {
                            if (preg_match('/(mobile legends)|(mobilelegends)|(mobile legend)|(mobilelegend)/', strtolower($productRow->brand))) {
                                $order = $vipayment->order_game($productRow->code, $target, $zoneid);
                            } else {
                                $order = $vipayment->order_game($productRow->code, $target, null);
                            }
                        } else {
                            $order = $vipayment->order_prepaid($productRow->code, $target);
                        }

                        if ($order->result) {
                            $update = array();
                            $update['servercode'] = $order->data->trxid;
                            $update['jsonresponse'] = json_encode($order);

                            $this->trorder->update(array(
                                'id' => $orderid
                            ), $update);

                            $this->db->trans_commit();

                            $this->send_notification($orderid, $this->merchant->id, $currentuser->phonenumber);

                            return JSONResponseDefault('OK', $order->message);
                        } else {
                            if ($order != null) {
                                log_message_user('error', '[VIPAYMENT PRABAYAR] Response: ' . json_encode($order), $this->merchant->id);
                            }

                            throw new Exception($order->message);
                        }
                    }
                } else {
                    $order = $this->msvendordetail->select('a.*, b.default_config')
                        ->join('msvendor b', 'b.id = a.vendorid')
                        ->get(array(
                            'a.vendorid' => $productRow->vendorid,
                            'a.apitype' => 'Order'
                        ));

                    if ($order->num_rows() == 0) {
                        throw new Exception('Konfigurasi vendor tidak ditemukan');
                    }

                    $vendor_order = $order->row();

                    if ($vendor_order->default_config == null) {
                        throw new Exception('Konfigurasi vendor tidak ditemukan');
                    }

                    $response_indicator = json_decode($vendor_order->response_indicator);
                    $response_setting = json_decode($vendor_order->response_setting);

                    $dynamicvendor = new DynamicVendor($productRow->vendorid, json_decode($vendor_order->default_config, true));
                    $order = $dynamicvendor->order($orderid);

                    if (
                        ($response_indicator->index == null && isset($order[$response_indicator->key]) && (
                            $response_indicator->datatype == 'string' && $order[$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'number' && $order[$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'boolean' && $order[$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'exists' && isset($order[$response_indicator->key])
                        ))
                        || ($response_indicator->index != null && isset($order[$response_indicator->index][$response_indicator->key]) && (
                            $response_indicator->datatype == 'string' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'number' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'boolean' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'exists' && isset($order[$response_indicator->index][$response_indicator->key])
                        ))
                    ) {
                        $var_referenceid = $response_setting->referenceid ?? null;
                        $var_price = $response_setting->price ?? null;
                        $var_status = $response_setting->status ?? null;
                        $var_note = $response_setting->note ?? null;
                        $var_sn = $response_setting->sn ?? null;
                        $var_errorrefund = $response_setting->errorrefund;

                        $exploding_errorefund = explode('[#]', strtolower($var_errorrefund ?? ''));

                        if ($response_setting->index != null) {
                            $order = $order[$response_setting->index] ?? null;
                        }

                        $referenceid = $var_referenceid != null ? ($order[$var_referenceid] ?? null) : null;
                        $price = $var_price != null ? ($order[$var_price] ?? null) : null;
                        $status = $var_status != null ? ($order[$var_status] ?? null) : null;
                        $note = $var_note != null ? ($order[$var_note] ?? null) : null;
                        $sn = $var_sn != null ? ($order[$var_sn] ?? null) : null;

                        if ($status != null) {
                            if (in_array($status, $exploding_errorefund)) {
                                log_message_user('error', '[MULTIVENDOR ORDER] Failed to parsing error refund, response: ' . json_encode($order), $this->merchant->id);

                                throw new Exception('Gagal melakukan transaksi');
                            }
                        } else {
                            if ($var_status == null) {
                                $status = 'pending';
                            } else if ($var_status != null && $status == null) {
                                log_message_user('error', '[MULTIVENDOR ORDER] Failed to parsing status, response: ' . json_encode($order), $this->merchant->id);

                                throw new Exception('Gagal melakukan transaksi');
                            }
                        }

                        $update = array();
                        $update['jsonresponse'] = json_encode($order);

                        if ($referenceid != null) {
                            $update['servercode'] = $referenceid;
                        }

                        if ($price != null) {
                            $update['price'] = $price;
                        }

                        if ($status != null) {
                            $update['status'] = $status;
                        }

                        if ($note != null) {
                            $update['note'] = $note;
                        }

                        if ($sn != null) {
                            $update['sn'] = $sn;
                        }

                        $this->trorder->update(array(
                            'id' => $orderid
                        ), $update);

                        $this->db->trans_commit();

                        $this->send_notification($orderid, $this->merchant->id, $currentuser->phonenumber);

                        return JSONResponseDefault('OK', 'Transaksi berhasil');
                    } else {
                        log_message_user('error', '[MULTIVENDOR ORDER] Failed to parse response, response: ' . json_encode($order), $this->merchant->id);

                        throw new Exception('Gagal melakukan transaksi');
                    }
                }
            }

            if ($productRow->isdatabase == 1 && $productRow->databasecategory == 'Stock Product') {
                $stockproductdetail = $this->msstockproductdetail->get(array(
                    'stockproductid' => $databaseid,
                    'status' => 'Tersedia'
                ))->row();

                $updatestockproductdetail = array();
                $updatestockproductdetail['status'] = 'Tidak Tersedia';
                $updatestockproductdetail['orderid'] = $orderid;

                $this->msstockproductdetail->update(array(
                    'id' => $stockproductdetail->id
                ), $updatestockproductdetail);

                $update = array();
                $update['status'] = 'success';
                $update['sn'] = stringEncryption('decrypt', $stockproductdetail->data);

                $this->trorder->update(array(
                    'id' => $orderid
                ), $update);
            }

            $this->db->trans_commit();

            $this->send_notification($orderid, $this->merchant->id, $currentuser->phonenumber);

            return JSONResponseDefault('OK', 'Transaksi berhasil');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function prabayar_massal()
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $disabled = $this->disabledcategory->result(array(
            'userid' => $this->merchant->id,
            'category_apikey' => 'PPOB',
        ));

        $disabled_category = array();
        foreach ($disabled as $key => $value) {
            $disabled_category[] = $value->categoryname;
        }

        $where = array(
            'userid' => $this->merchant->id,
            'subcategory_apikey' => 'PRABAYAR',
            'status' => 1
        );

        if ($this->merchant->multivendor != 1) {
            $vendor = getCurrentVendor('PPOB', $this->merchant->id);
            $where['vendor'] = $vendor;
            $where['vendorid'] = null;
        } else {
            $where['vendorid !='] = null;
            $where['vendorenabled'] = 1;
        }

        $categoryproduct = $this->db->select('category')
            ->from('msproduct')
            ->where($where)
            ->group_by('category')
            ->order_by('category', 'ASC');

        if (count($disabled_category) > 0) {
            $this->db->where_not_in('category', $disabled_category);
        }

        $categoryproduct = $this->db->get_compiled_select();

        $mscategory = $this->db->select('name AS category')
            ->from('mscategory')
            ->where(array(
                'userid' => $this->merchant->id
            ));

        $mscategory = $this->db->get_compiled_select();

        $category = $this->db->query("SELECT a.* FROM (($categoryproduct) UNION ($mscategory)) a ORDER BY a.category ASC")->result();

        $data = array();
        $data['title'] = $this->merchant->companyname . ' | Pesanan Baru';
        $data['content'] = 'ppob/prabayar_massal';
        $data['category'] = $category;

        return viewTemplate($this->merchant->id, "master", $data);
    }

    public function fields_prabayar_massal()
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            }

            $product = getPost('product');

            $disabled = $this->disabledcategory->result(array(
                'userid' => $this->merchant->id,
                'category_apikey' => 'PPOB'
            ));

            $disabled_category = array();
            foreach ($disabled as $key => $value) {
                $disabled_category[] = $value->categoryname;
            }

            $disabledbrand = $this->disabledbrand->result(array(
                'userid' => $this->merchant->id,
            ));

            $disabled_brand = array();
            foreach ($disabledbrand as $key => $value) {
                $disabled_brand[] = $value->brandname;
            }

            $where = array(
                'id' => $product,
                'userid' => $this->merchant->id,
                'subcategory_apikey' => 'PRABAYAR',
                'status' => 1
            );

            if ($this->merchant->multivendor != 1) {
                $vendor = getCurrentVendor('PPOB', $this->merchant->id);
                $where["(vendor = '$vendor' OR vendor IS NULL) ="] = true;
                $where['vendorid'] = null;
            } else {
                $where["((vendorid IS NOT NULL AND vendorenabled = 1) OR vendor IS NULL) ="] = true;
            }

            $get = $this->msproduct->where_not_in('category', $disabled_category)
                ->where_not_in('brand', $disabled_brand)
                ->get($where);

            if ($get->num_rows() == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            $row = $get->row();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => viewTemplate($this->merchant->id, "ppob/prabayar_massal_fields", array(
                    'product' => $row
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function form_prabayar()
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            }

            $disabled = $this->disabledcategory->result(array(
                'userid' => $this->merchant->id,
                'category_apikey' => 'PPOB',
            ));

            $disabled_category = array();
            foreach ($disabled as $key => $value) {
                $disabled_category[] = $value->categoryname;
            }

            $where = array(
                'userid' => $this->merchant->id,
                'subcategory_apikey' => 'PRABAYAR',
                'status' => 1
            );

            if ($this->merchant->multivendor != 1) {
                $vendor = getCurrentVendor('PPOB', $this->merchant->id);
                $where['vendor'] = $vendor;
                $where['vendorid'] = null;
            } else {
                $where['vendorid !='] = null;
                $where['vendorenabled'] = 1;
            }

            $categoryproduct = $this->db->select('category')
                ->from('msproduct')
                ->where($where)
                ->group_by('category')
                ->order_by('category', 'ASC');

            if (count($disabled_category) > 0) {
                $this->db->where_not_in('category', $disabled_category);
            }

            $categoryproduct = $this->db->get_compiled_select();

            $mscategory = $this->db->select('name AS category')
                ->from('mscategory')
                ->where(array(
                    'userid' => $this->merchant->id
                ));

            $mscategory = $this->db->get_compiled_select();

            $category = $this->db->query("SELECT a.* FROM (($categoryproduct) UNION ($mscategory)) a ORDER BY a.category ASC")->result();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => viewTemplate($this->merchant->id, "ppob/prabayar_form", array(
                    'category' => $category,
                    'index' => getPost('index', null)
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_prabayar_massal()
    {
        try {
            $this->db->trans_begin();

            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                $this->db->trans_rollback();
                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            }

            $product = getPost('product', array());
            $target = getPost('target', array());
            $zoneid = getPost('zoneid', array());
            $pin = getPost('pin');
            $contact = getPost('contact', array());

            if (count($product) == 0) {
                throw new Exception('Produk wajib diisi');
            } else {
                foreach ($product as $key => $value) {
                    if ($value == null) {
                        throw new Exception('Produk ke ' . ($key + 1) . ' wajib diisi');
                    } else if ($target[$key] == null) {
                        throw new Exception('Target ke ' . ($key + 1) . ' wajib diisi');
                    }
                }
            }

            if ($pin == null) {
                throw new Exception('PIN Transaksi wajib diisi');
            }

            $currentuser = getCurrentUser();

            if (stringEncryption('encrypt', $pin) != $currentuser->pin) {
                throw new Exception('PIN Transaksi yang anda masukkan salah');
            }

            if ($currentuser->roleid == null) {
                $getrole = $this->msrole->get(array(
                    'createdby' => $this->merchant->id,
                    'isdefault' => '1'
                ))->row();
            } else {
                $getrole = $this->msrole->get(array(
                    'createdby' => $this->merchant->id,
                    'id' => $currentuser->roleid
                ))->row();
            }

            if ($getrole != null) {
                if ($getrole->discounttype == 'Simple') {
                    $getrolediscount = $this->msrolediscount->get(array(
                        'userid' => $this->merchant->id,
                        'roleid' => $getrole->id
                    ))->row();

                    $discount = $getrolediscount->trxdiscount ?? 0;
                } else {
                    $discount = $this->msrolediscountadv->get(array(
                        'createdby' => $this->merchant->id,
                        'roleid' => $getrole->id,
                        'servicetype' => 'Prabayar'
                    ))->result();
                }
            } else {
                $getrole = new stdClass();
                $getrole->discounttype = 'Simple';
                $discount = 0;
            }

            $disabled = $this->disabledcategory->result(array(
                'userid' => $this->merchant->id,
                'category_apikey' => 'PPOB'
            ));

            $disabled_category = array();
            foreach ($disabled as $key => $value) {
                $disabled_category[] = $value->categoryname;
            }

            $disabledbrand = $this->disabledbrand->result(array(
                'userid' => $this->merchant->id,
            ));

            $disabled_brand = array();
            foreach ($disabledbrand as $key => $value) {
                $disabled_brand[] = $value->brandname;
            }

            if ($this->merchant->multivendor != 1) {
                $vendor = getCurrentVendor('PPOB', $this->merchant->id);
            }

            $grandtotal = 0;

            $arrayproduct = array();
            foreach ($product as $key => $value) {
                $where = array(
                    'id' => $value,
                    'userid' => $this->merchant->id,
                    'subcategory_apikey' => 'PRABAYAR'
                );

                if ($this->merchant->multivendor != 1) {
                    $where["(vendor = '$vendor' OR vendor IS NULL) ="] = true;
                    $where['vendorid'] = null;
                } else {
                    $where["((vendorid IS NOT NULL AND vendorenabled = 1) OR vendor IS NULL) ="] = true;
                }

                $getproduct = $this->msproduct->where_not_in('category', $disabled_category)
                    ->where_not_in('brand', $disabled_brand)
                    ->get($where);

                if ($getproduct->num_rows() == 0) {
                    throw new Exception('Produk tidak ditemukan');
                }

                $productRow = $getproduct->row();

                if ($productRow->status != 1) {
                    throw new Exception('Produk sedang gangguan');
                }

                if ($productRow->isstock == 1) {
                    $stock = $productRow->stock;

                    if ($stock == 0) {
                        throw new Exception('Produk sedang kosong');
                    }
                }

                if ($productRow->isdatabase == 1 && $productRow->databasecategory == 'Stock Product') {
                    $databaseid = $productRow->databaseid;

                    $stockproduct = $this->msstockproduct->get(array(
                        'id' => $databaseid
                    ))->row();

                    if ($stockproduct == null) {
                        throw new Exception('Produk tidak tersedia');
                    } else {
                        $available_stock = $this->msstockproductdetail->total(array(
                            'stockproductid' => $databaseid,
                            'status' => 'Tersedia'
                        ));

                        if ($available_stock == 0) {
                            throw new Exception('Produk sedang kosong');
                        }
                    }
                }

                $fixproductprice = $productRow->price;

                if ($getrole->discounttype == 'Simple') {
                    $fixproductprice = $productRow->price - $discount;
                } else {
                    $found = false;

                    foreach ($discount as $val) {
                        if ($found) continue;

                        if ($val->startrange <= $productRow->price && $val->endrange >= $productRow->price && strtoupper($val->servicetype) == 'PRABAYAR') {
                            if ($val->discounttype == 'Persentase') {
                                $fixproductprice = $productRow->price - ($productRow->price * $val->nominal / 100);

                                $found = true;
                            } else {
                                $fixproductprice = $productRow->price - $val->nominal;

                                $found = true;
                            }
                        }
                    }
                }

                $grandtotal += $fixproductprice;

                $arrayproduct[] = array(
                    'product' => $productRow,
                    'fixproductprice' => $fixproductprice
                );
            }

            $currentbalance = getCurrentBalance(getCurrentIdUser(), true);
            $simulatedBalance = $currentbalance;
            if ($currentbalance < $grandtotal) {
                throw new Exception('Saldo anda tidak mencukupi');
            }

            $keyzone = 0;
            $realGrandTotal = 0;
            $transactionFailed = array();
            $transactionSuccess = array();
            foreach ($arrayproduct as $key => $value) {
                $productRow = $value['product'];
                $fixproductprice = $value['fixproductprice'];

                $getpending = $this->trorder->total(array(
                    'userid' => getCurrentIdUser(),
                    'serviceid' => $productRow->id,
                    'target' => $target[$key],
                    'status' => 'pending'
                ));

                if ($getpending > 0) {
                    $transactionFailed[] = array(
                        'message' => 'Transaksi ' . $productRow->productname . ' ke ' . $target[$key] . ' sedang dalam proses transaksi',
                    );

                    continue;
                }

                if ($productRow->vendor != null && preg_match('/(mobile legends)|(mobilelegends)|(mobile legend)|(mobilelegend)/', strtolower($productRow->brand)) && $productRow->vendor == 'VIPayment') {
                    if (isset($zoneid[$keyzone]) && $zoneid[$keyzone] == null) {
                        $keyzone++;

                        $transactionFailed[] = array(
                            'message' => 'Zone ID ' . $productRow->productname . ' ke ' . $target[$key] . ' wajib diisi',
                        );

                        continue;
                    }
                }

                $originprice = $productRow->price;
                $potonganprofit = $originprice - $fixproductprice;

                if ($productRow->isstock == 1) {
                    $this->msproduct->update(array(
                        'id' => $productRow->id
                    ), array(
                        'stock' => $stock - 1
                    ));
                }

                $insert = array();
                $insert['userid'] = getCurrentIdUser();
                $insert['clientcode'] = generateTransactionNumber('PRABAYAR');
                $insert['serviceid'] = $productRow->id;
                $insert['target'] = $target[$key];
                $insert['price'] = $fixproductprice;
                $insert['profit'] = ($productRow->profit - $potonganprofit);
                $insert['currentsaldo'] = $simulatedBalance;
                $insert['status'] = 'pending';
                $insert['type'] = 'PPOB';
                $insert['queuetransaction'] = 1;
                $insert['category_apikey'] = $productRow->category_apikey;
                $insert['subcategory_apikey'] = $productRow->subcategory_apikey;
                $insert['orderplatform'] = 'web';
                $insert['productcode'] = $productRow->code;
                $insert['productname_order'] = $productRow->productname;
                $insert['vendor'] = $productRow->vendor;
                $insert['merchantid_order'] = $this->merchant->id;
                $insert['status_payment'] = 'sukses';
                $insert['vendorid'] = $productRow->vendorid;

                if ($productRow->vendor != null) {
                    if (preg_match('/(mobile legends)|(mobilelegends)|(mobile legend)|(mobilelegend)/', strtolower($productRow->brand)) && $productRow->vendor == 'VIPayment') {
                        $insert['zoneid'] = $zoneid[$keyzone];
                    }
                }

                $this->trorder->insert($insert);
                $orderid = $this->db->insert_id();

                if (isset($contact[$key]) && $contact[$key] != null) {
                    $insertcontact = array();
                    $insertcontact['contactname'] = $contact[$key];
                    $insertcontact['content'] = $target[$key];
                    $insertcontact['userid'] = getCurrentIdUser();
                    $insertcontact['contacttype'] = 'PPOB';

                    $this->mscontact->insert($insertcontact);
                }

                $inserthistorybalance = array();
                $inserthistorybalance['userid'] = getCurrentIdUser();
                $inserthistorybalance['type'] = 'OUT';
                $inserthistorybalance['nominal'] = $fixproductprice;
                $inserthistorybalance['currentbalance'] = $simulatedBalance;
                $inserthistorybalance['orderid'] = $orderid;
                $inserthistorybalance['createdby'] = getCurrentIdUser();
                $inserthistorybalance['createddate'] = getCurrentDate();

                $this->historybalance->insert($inserthistorybalance);

                $keyzone++;

                $transactionSuccess[] = array(
                    'message' => 'Transaksi ' . $productRow->productname . ' ke ' . $target[$key] . ' berhasil',
                );
                $realGrandTotal += $fixproductprice;
                $simulatedBalance -= $fixproductprice;
            }

            $update = array();
            $update['balance'] = $currentbalance - $realGrandTotal;

            $this->msusers->update(array(
                'id' => getCurrentIdUser()
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Transaksi gagal');
            }

            $this->db->trans_commit();

            // generate message from transactionSuccess and transactionFailed
            $message = '';
            foreach ($transactionSuccess as $key => $value) {
                if (count($transactionFailed) > 0) {
                    $message .= $value['message'] . ', ';
                } else {
                    $message .= $value['message'];
                }
            }

            foreach ($transactionFailed as $key => $value) {
                if ($key == count($transactionFailed) - 1) {
                    $message .= $value['message'];
                } else {
                    $message .= $value['message'] . ', ';
                }
            }

            return JSONResponseDefault('OK', $message);
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function history()
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $data = array();
        $data['title'] = $this->merchant->companyname . ' | Riwayat';
        $data['content'] = 'ppob/history';

        return viewTemplate($this->merchant->id, "master", $data);
    }

    public function history_datatables_ppob()
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            if (isLogin()) {
                $data = array();

                $datatables = $this->datatables->make('TrOrder', 'QueryDatatables', 'SearchDatatables');

                $where = array(
                    'a.userid' => getCurrentIdUser(),
                    'a.type' => 'PPOB'
                );

                $currenttheme = getCurrentThemeConfiguration($this->merchant->id);

                if ($currenttheme != 'HighAdmin-TopNav' && $currenttheme != 'Dason-TopNav' && $currenttheme != 'Able') {
                    $label = "label label";
                } else if ($currenttheme == 'Dason-TopNav' || $currenttheme == 'Able') {
                    $label = "badge bg";
                } else {
                    $label = "badge badge";
                }

                foreach ($datatables->getData($where) as $key => $value) {
                    $createddate = new DateTime($value->createddate);
                    $updateddate = new DateTime($value->updateddate);

                    $waktu_proses = $createddate->diff($updateddate);

                    if (strtolower($value->status) == 'pending') {
                        $status = "<span class=\"$label-warning\">Pending</span>";
                    } else if (strtolower($value->status) == 'success' || strtolower($value->status) == 'sukses') {
                        $status = "<span class=\"$label-success\">Success</span>";
                    } else {
                        $status = "<span class=\"$label-danger\">" . ucfirst(strtolower($value->status)) . "</span>";
                    }

                    $timeprocess = "-";
                    if (strtolower($value->status) == 'success' || strtolower($value->status) == 'sukses') {
                        $timeprocess = $waktu_proses->format("%a Hari %h Jam %i Menit %s Detik");
                    }

                    $orderplatform = "-";
                    if ($value->orderplatform == 'web' || $value->orderplatform == null) {
                        $orderplatform = "Website";
                    } else if ($value->orderplatform == 'apps') {
                        $orderplatform = "Aplikasi";
                    } else if ($value->orderplatform == 'api') {
                        $orderplatform = "API";
                    }

                    $detail = array();
                    $detail[] = "<a href=\"javascript:;\" onclick=\"modalStruk('" . stringEncryption('encrypt', $value->id) . "')\">$value->clientcode</a>";
                    $detail[] = DateFormat($value->createddate, 'd F Y H:i:s');
                    $detail[] = ($value->productname ?? $value->productname_order) ?? '- Layanan telah dihapus -';
                    $detail[] = $value->target;
                    $detail[] = IDR($value->price);
                    $detail[] = $value->sn ?? '-';
                    $detail[] = $orderplatform;
                    $detail[] = $value->paymenttype == null || $value->paymenttype == 'Saldo' ? IDR($value->currentsaldo) : '-';
                    $detail[] = $value->paymenttype == null || $value->paymenttype == 'Saldo' ? IDR($value->currentsaldo - $value->price) : '-';
                    $detail[] = $status;
                    $detail[] = $timeprocess;

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function pascabayar()
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $disabled = $this->disabledcategory->result(array(
            'userid' => $this->merchant->id,
            'category_apikey' => 'PPOB'
        ));

        $disabled_category = array();
        foreach ($disabled as $key => $value) {
            $disabled_category[] = $value->categoryname;
        }

        $where = array(
            'userid' => $this->merchant->id,
            'subcategory_apikey' => 'PASCABAYAR',
            'status' => 1
        );

        if ($this->merchant->multivendor != 1) {
            $vendor = getCurrentVendor('PPOB', $this->merchant->id);
            $where["(vendor = '$vendor' OR vendor IS NULL) ="] = true;
            $where['vendorid'] = null;
        } else {
            $where["((vendorid IS NOT NULL AND vendorenabled = 1) OR vendor IS NULL) ="] = true;
        }

        $data = array();
        $data['title'] = $this->merchant->companyname . ' | Bayar Tagihan';
        $data['content'] = 'ppob/pascabayar';
        $data['category'] = $this->msproduct->select('brand')
            ->where_not_in('category', $disabled_category)
            ->group_by('brand')
            ->order_by('brand', 'ASC')
            ->result($where);

        return viewTemplate($this->merchant->id, "master", $data);
    }

    public function cek_tagihan()
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            }

            $producttype = getPost('producttype');
            $customernumber = getPost('customernumber');

            if ($producttype == null) {
                throw new Exception('Jenis Produk wajib diisi');
            } else if ($customernumber == null) {
                throw new Exception('ID Pelanggan wajib diisi');
            }

            $apikeys = getCurrentAPIKeys('PPOB', $this->merchant->id);
            $vendor = getCurrentVendor('PPOB', $this->merchant->id);

            $disabled = $this->disabledcategory->result(array(
                'userid' => $this->merchant->id,
                'category_apikey' => 'PPOB'
            ));

            $disabled_category = array();
            foreach ($disabled as $key => $value) {
                $disabled_category[] = $value->categoryname;
            }

            $disabledbrand = $this->disabledbrand->result(array(
                'userid' => $this->merchant->id,
            ));

            $disabled_brand = array();
            foreach ($disabledbrand as $key => $value) {
                $disabled_brand[] = $value->brandname;
            }

            $get = $this->msproduct->where_not_in('category', $disabled_category)
                ->where_not_in('brand', $disabled_brand)
                ->get(array(
                    'id' => $producttype,
                    'userid' => $this->merchant->id,
                    "(vendor = '$vendor' OR vendor IS NULL) =" => true,
                    'subcategory_apikey' => 'PASCABAYAR'
                ));

            if ($get->num_rows() == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            $productRow = $get->row();

            if ($productRow->status != 1) {
                throw new Exception('Produk sedang gangguan');
            }

            if ($vendor == 'Digiflazz') {
                $refid = generateTransactionNumber('PASCABAYAR');

                $digiflazz = new Digiflazz(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));
                $cek = $digiflazz->cek_tagihan($productRow->code, $customernumber, $refid, ENVIRONMENT == 'development');

                $result = json_decode($cek);

                if (isset($result->data->status)) {
                    if ($result->data->status != 'Gagal') {
                        $data = array();
                        $data['result'] = $result;
                        $data['productname'] = strtoupper($productRow->productname);
                        $data['profit'] = $productRow->profit;
                        $data['refid'] = $refid;

                        $getroleid = getCurrentUser();

                        if ($getroleid->roleid == null) {
                            $getrole = $this->msrole->get(array(
                                'createdby' => $this->merchant->id,
                                'isdefault' => '1'
                            ))->row();
                        } else {
                            $getrole = $this->msrole->get(array(
                                'createdby' => $this->merchant->id,
                                'id' => $getroleid->roleid
                            ))->row();
                        }

                        if ($getrole != null) {
                            if ($getrole->discounttype == 'Simple') {
                                $getrolediscount = $this->msrolediscount->get(array(
                                    'userid' => $this->merchant->id,
                                    'roleid' => $getroleid->roleid
                                ))->row();

                                $discount = $getrolediscount->trxdiscount ?? 0;
                            } else {
                                $discount = $this->msrolediscountadv->get(array(
                                    'createdby' => $this->merchant->id,
                                    'roleid' => $getrole->id,
                                    'servicetype' => 'Pascabayar'
                                ))->result();
                            }
                        } else {
                            $getrole = new stdClass();
                            $getrole->discounttype = 'Simple';
                            $discount = 0;
                        }

                        $data['discounttype'] = $getrole->discounttype;
                        $data['discount'] = $discount;

                        return JSONResponse(array(
                            'RESULT' => 'OK',
                            'CONTENT' => viewTemplate($this->merchant->id, 'ppob/tagihan/digiflazz', $data, true)
                        ));
                    } else {
                        if ($result != null) {
                            log_message_user('error', '[DIGIFLAZZ TAGIHAN CHECK] Response: ' . json_encode($result), $this->merchant->id);
                        }

                        $responsecode = getResponseCodeDigiflazz($result->data->rc);
                        throw new Exception($responsecode);
                    }
                } else {
                    throw new Exception('Gagal melakukan pengecekan tagihan');
                }
            } else {
                throw new Exception('Vendor tidak menyediakan layanan cek tagihan');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function bayar_tagihan()
    {
        try {
            $this->db->trans_begin();

            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                $this->db->trans_rollback();

                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            }

            $producttype = getPost('producttype');
            $customernumber = getPost('customernumber');
            $pin = getPost('pin');

            if ($producttype == null) {
                throw new Exception('Jenis Produk wajib diisi');
            } else if ($customernumber == null) {
                throw new Exception('ID Pelanggan wajib diisi');
            } else if ($pin == null) {
                throw new Exception('PIN Transaksi wajib diisi');
            }

            $apikeys = getCurrentAPIKeys('PPOB', $this->merchant->id);
            $vendor = getCurrentVendor('PPOB', $this->merchant->id);
            $currentuser = getCurrentUser(null, true);

            if (stringEncryption('decrypt', $currentuser->pin) != $pin) {
                throw new Exception('PIN Transaksi yang anda masukkan salah');
            }

            $disabled = $this->disabledcategory->result(array(
                'userid' => $this->merchant->id,
                'category_apikey' => 'PPOB'
            ));

            $disabled_category = array();
            foreach ($disabled as $key => $value) {
                $disabled_category[] = $value->categoryname;
            }

            $disabledbrand = $this->disabledbrand->result(array(
                'userid' => $this->merchant->id,
            ));

            $disabled_brand = array();
            foreach ($disabledbrand as $key => $value) {
                $disabled_brand[] = $value->brandname;
            }

            $get = $this->msproduct->where_not_in('category', $disabled_category)
                ->where_not_in('brand', $disabled_brand)
                ->get(array(
                    'id' => $producttype,
                    'userid' => $this->merchant->id,
                    'vendor' => $vendor,
                    'subcategory_apikey' => 'PASCABAYAR'
                ));

            if ($get->num_rows() == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            $productRow = $get->row();

            if ($productRow->status != 1) {
                throw new Exception('Produk sedang gangguan');
            }

            if ($vendor == 'Digiflazz') {
                $refid_inquiry = generateTransactionNumber('PASCABAYAR');

                $digiflazz = new Digiflazz(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));
                $cek = $digiflazz->cek_tagihan($productRow->code, $customernumber, $refid_inquiry, ENVIRONMENT == 'development');

                $result = json_decode($cek);

                if (isset($result->data->status)) {
                    if ($result->data->status != 'Gagal') {
                        $customer_name = null;

                        if (isset($result->data->customer_name)) {
                            $customer_name = $result->data->customer_name;
                        }

                        $getroleid = getCurrentUser();

                        if ($getroleid->roleid == null) {
                            $getrole = $this->msrole->get(array(
                                'createdby' => $this->merchant->id,
                                'isdefault' => '1'
                            ))->row();
                        } else {
                            $getrole = $this->msrole->get(array(
                                'createdby' => $this->merchant->id,
                                'id' => $getroleid->roleid
                            ))->row();
                        }

                        if ($getrole != null) {
                            if ($getrole->discounttype == 'Simple') {
                                $getrolediscount = $this->msrolediscount->get(array(
                                    'userid' => $this->merchant->id,
                                    'roleid' => $getroleid->roleid
                                ))->row();

                                $discount = $getrolediscount->trxdiscount ?? 0;
                            } else {
                                $discount = $this->msrolediscountadv->get(array(
                                    'createdby' => $this->merchant->id,
                                    'roleid' => $getrole->id,
                                    'servicetype' => 'Pascabayar'
                                ))->result();
                            }
                        } else {
                            $getrole = new stdClass();
                            $getrole->discounttype = 'Simple';
                            $discount = 0;
                        }

                        if ($getrole->discounttype == 'Simple') {
                            $fixprice = $result->data->selling_price - $discount;
                        } else {
                            $found = false;

                            foreach ($discount as $val) {
                                if ($found) continue;

                                if ($val->startrange <= $result->data->selling_price && $val->endrange >= $result->data->selling_price) {
                                    if ($val->discounttype == 'Persentase') {
                                        $fixprice = $result->data->selling_price - ($result->data->selling_price * $val->nominal / 100);

                                        $found = true;
                                    } else {
                                        $fixprice = $result->data->selling_price - $val->nominal;

                                        $found = true;
                                    }
                                }
                            }

                            if ($found == false) {
                                $fixprice = $result->data->selling_price;
                            }
                        }

                        if ($currentuser->balance < ($fixprice + $productRow->profit)) {
                            throw new Exception('Saldo anda tidak mencukupi');
                        } else {
                            $update = array();
                            $update['balance'] = $currentuser->balance - ($fixprice + $productRow->profit);

                            $this->msusers->update(array(
                                'id' => getCurrentIdUser()
                            ), $update);
                        }

                        $potonganprofit = $result->data->selling_price - $fixprice;
                        $refid = $refid_inquiry;

                        $insert = array();
                        $insert['userid'] = getCurrentIdUser();
                        $insert['clientcode'] = $refid;
                        $insert['serviceid'] = $productRow->id;
                        $insert['target'] = $customernumber;
                        $insert['price'] = $fixprice + $productRow->profit;
                        $insert['profit'] = ($productRow->profit - $potonganprofit);
                        $insert['currentsaldo'] = $currentuser->balance;
                        $insert['status'] = 'pending';
                        $insert['note'] = 'Transaksi Sukses';
                        $insert['type'] = 'PPOB';
                        $insert['category_apikey'] = $productRow->category_apikey;
                        $insert['subcategory_apikey'] = $productRow->subcategory_apikey;
                        $insert['orderplatform'] = 'web';
                        $insert['productcode'] = $productRow->code;
                        $insert['productname_order'] = $productRow->productname;
                        $insert['vendor'] = $productRow->vendor;
                        $insert['customer_name'] = $customer_name;
                        $insert['merchantid_order'] = $this->merchant->id;
                        $insert['status_payment'] = 'sukses';
                        $insert['vendorid'] = $productRow->vendorid;

                        $this->trorder->insert($insert);
                        $orderid = $this->db->insert_id();

                        $contact = getPost('contact');

                        if ($contact != null) {
                            $insertcontact = array();
                            $insertcontact['contactname'] = $contact;
                            $insertcontact['content'] = $customernumber;
                            $insertcontact['userid'] = getCurrentIdUser();
                            $insertcontact['contacttype'] = 'PPOB';

                            $this->mscontact->insert($insertcontact);
                        }

                        $insertlog = array();
                        $insertlog['userid'] = getCurrentIdUser();
                        $insertlog['type'] = 'OUT';
                        $insertlog['nominal'] = $fixprice + $productRow->profit;
                        $insertlog['currentbalance'] = $currentuser->balance;
                        $insertlog['orderid'] = $orderid;
                        $insertlog['createdby'] = getCurrentIdUser();
                        $insertlog['createddate'] = getCurrentDate();

                        $this->historybalance->insert($insertlog);

                        if ($this->db->trans_status() === FALSE) {
                            throw new Exception('Transaksi gagal');
                        }

                        pullTransaction($this->merchant->id);

                        $bayar = $digiflazz->bayar_tagihan($productRow->code, $customernumber, $refid, ENVIRONMENT == 'development');
                        $result = json_decode($bayar);

                        if (isset($result->data->status)) {
                            if ($result->data->status != 'Gagal') {
                                $update = array();
                                $update['servercode'] = $result->data->ref_id;
                                $update['note'] = $result->data->message;
                                $update['sn'] = $result->data->sn;
                                $update['status'] = strtolower($result->data->status);
                                $update['jsonresponse'] = json_encode($result);

                                $this->trorder->update(array(
                                    'id' => $orderid
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($orderid, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Transaksi berhasil');
                            } else {
                                $responsecode = getResponseCodeDigiflazz($result->data->rc);

                                throw new Exception($responsecode);
                            }
                        } else {
                            throw new Exception('Transaksi gagal');
                        }
                    } else {
                        $responsecode = getResponseCodeDigiflazz($result->data->rc);
                        throw new Exception($responsecode);
                    }
                } else {
                    if ($result != null) {
                        log_message_user('error', '[DIGIFLAZZ TAGIHAN PAY] Response: ' . json_encode($result), $this->merchant->id);
                    }

                    throw new Exception('Gagal melakukan transaksi');
                }
            } else {
                throw new Exception('Vendor tidak menyediakan layanan bayar tagihan');
            }
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function report()
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $data = array();
        $data['title'] = $this->merchant->companyname . ' | Laporan PPOB';
        $data['content'] = 'ppob/report/index';
        $data['pending'] = $this->trorder->select('status, count(*) as total, sum(price) as nominal')
            ->get(array(
                'userid' => getCurrentIdUser(),
                'status' => 'pending',
                'type' => 'PPOB',
                'MONTH(createddate) =' => getCurrentDate('m'),
                'YEAR(createddate) =' => getCurrentDate('Y')
            ))->row();
        $data['success'] = $this->trorder->select('status, count(*) as total, sum(price) as nominal')
            ->get(array(
                'userid' => getCurrentIdUser(),
                "(status = 'success' or status = 'sukses' or status = 'completed') =" => true,
                'type' => 'PPOB',
                'MONTH(createddate) =' => getCurrentDate('m'),
                'YEAR(createddate) =' => getCurrentDate('Y')
            ))->row();
        $data['failed'] = $this->trorder->select('status, count(*) as total, sum(price) as nominal')
            ->get(array(
                'userid' => getCurrentIdUser(),
                "(status = 'gagal' or status = 'failed' or status = 'error') =" => true,
                'type' => 'PPOB',
                'MONTH(createddate) =' => getCurrentDate('m'),
                'YEAR(createddate) =' => getCurrentDate('Y')
            ))->row();
        $data['transaction'] = $this->trorder->select('date(createddate) as date, count(*) as total, sum(price) as nominal')
            ->group_by('date(createddate)')
            ->order_by('date(createddate)', 'asc')
            ->result(array(
                'userid' => getCurrentIdUser(),
                'type' => 'PPOB',
                'MONTH(createddate) =' => getCurrentDate('m'),
                'YEAR(createddate) =' => getCurrentDate('Y')
            ));
        $data['startperiode'] = getCurrentDate('1 F Y');
        $data['endperiode'] = getCurrentDate('t F Y');

        return viewTemplate($this->merchant->id, "master", $data);
    }

    public function filter_report()
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Anda belum login');
            }

            $startdate = getPost('startdate');
            $enddate = getPost('enddate');

            if (empty($startdate) || empty($enddate)) {
                throw new Exception('Tanggal tidak boleh kosong');
            } else if ($enddate < $startdate) {
                throw new Exception('Tanggal akhir tidak boleh kurang dari tanggal awal');
            }

            $data = array();
            $data['pending'] = $this->trorder->select('status, count(*) as total, sum(price) as nominal')
                ->get(array(
                    'userid' => getCurrentIdUser(),
                    'status' => 'pending',
                    'type' => 'PPOB',
                    'DATE(createddate) >=' => $startdate,
                    'DATE(createddate) <=' => $enddate
                ))->row();
            $data['success'] = $this->trorder->select('status, count(*) as total, sum(price) as nominal')
                ->get(array(
                    'userid' => getCurrentIdUser(),
                    "(status = 'success' or status = 'sukses' or status = 'completed') =" => true,
                    'type' => 'PPOB',
                    'DATE(createddate) >=' => $startdate,
                    'DATE(createddate) <=' => $enddate
                ))->row();
            $data['failed'] = $this->trorder->select('status, count(*) as total, sum(price) as nominal')
                ->get(array(
                    'userid' => getCurrentIdUser(),
                    "(status = 'gagal' or status = 'failed' or status = 'error') =" => true,
                    'type' => 'PPOB',
                    'DATE(createddate) >=' => $startdate,
                    'DATE(createddate) <=' => $enddate
                ))->row();
            $data['transaction'] = $this->trorder->select('date(createddate) as date, count(*) as total, sum(price) as nominal')
                ->group_by('date(createddate)')
                ->order_by('date(createddate)', 'asc')
                ->result(array(
                    'userid' => getCurrentIdUser(),
                    'type' => 'PPOB',
                    'DATE(createddate) >=' => $startdate,
                    'DATE(createddate) <=' => $enddate
                ));
            $data['startperiode'] = DateFormat($startdate, 'd F Y');
            $data['endperiode'] = DateFormat($enddate, 'd F Y');

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => viewTemplate($this->merchant->id, 'ppob/report/content', $data, true),
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function print($id)
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if ($this->merchant->licenseid == null) {
                throw new Exception('Anda belum memiliki lisensi');
            }

            $id = stringEncryption('decrypt', $id);
            $merchantid = $this->merchant->id;

            $get = $this->trorder->select('a.clientcode, c.productname, a.price, a.updateddate, a.target, c.category_apikey, a.sn, d.domain, d.companyaddress, d.companyicon, d.companyname, d.strukcontent')
                ->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->join('msproduct c', 'c.id = a.serviceid', 'LEFT')
                ->join('msusers d', 'd.id = b.merchantid')
                ->get(array(
                    'a.id' => $id,
                    "(b.merchantid = '$merchantid' OR a.merchantid_order = '$merchantid') =" => true,
                    'a.userid' => getCurrentIdUser(),
                    'c.category_apikey' => 'PPOB'
                ));

            if ($get->num_rows() == 0) {
                return redirect(base_url('dashboard?userid=' . $this->userid));
            }

            $content = viewTemplate($this->merchant->id, "ppob/modalprint", array('transaction' => $get->row()), true);

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $content,
            ), JSON_INVALID_UTF8_IGNORE);
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_print($id)
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        ini_set('max_execution_time', 0);
        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $id = stringEncryption('decrypt', $id);
        $price = getGet('price');

        $merchantid = $this->merchant->id;

        $get = $this->trorder->select('a.clientcode, c.productname, a.price, a.updateddate, a.target, c.category_apikey, a.sn, d.domain, d.companyaddress, d.companyicon, d.companyname, d.strukcontent')
            ->join('msusers b', 'b.id = a.userid', 'LEFT')
            ->join('msproduct c', 'c.id = a.serviceid', 'LEFT')
            ->join('msusers d', 'd.id = b.merchantid')
            ->get(array(
                'a.id' => $id,
                "(b.merchantid = '$merchantid' OR a.merchantid_order = '$merchantid') =" => true,
                'a.userid' => getCurrentIdUser(),
                'c.category_apikey' => 'PPOB'
            ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('dashboard?userid=' . $this->userid));
        }

        $row = $get->row();

        $data = array();
        $data['title'] = 'Cetak Struk - ' . $row->clientcode;
        $data['transaction'] = $row;
        $data['price'] = $price;

        $dompdf = new Dompdf();
        $dompdf->loadHtml($this->load->view('transaction/print', $data, true));

        $dompdf->setPaper('A5', 'P');

        $dompdf->render();

        $dompdf->stream("Struk Transaksi", array("Attachment" => false));

        exit(0);
    }

    public function add_favourite_services()
    {
        try {
            $this->db->trans_begin();

            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                $this->db->trans_rollback();

                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Anda belum login');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);

            $get = $this->msproduct->get(array(
                'id' => $id,
                'userid' => $this->merchant->id,
                'category_apikey' => 'PPOB',
                "(subcategory_apikey = 'PRABAYAR' OR subcategory_apikey = 'PASCABAYAR')=" => true
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            $row = $get->row();

            $favourite = $this->productfavourite->total(array(
                'userid' => getCurrentIdUser(),
                'productid' => $row->id
            ));

            if ($favourite > 0) {
                throw new Exception('Produk sudah ada di daftar favorit');
            }

            $insert = array();
            $insert['userid'] = getCurrentIdUser();
            $insert['productid'] = $row->id;
            $insert['createddate'] = getCurrentDate('Y-m-d H:i:s');
            $insert['createdby'] = getCurrentIdUser();
            $insert['updateddate'] = getCurrentDate('Y-m-d H:i:s');
            $insert['updatedby'] = getCurrentIdUser();

            $this->productfavourite->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menambahkan produk ke daftar favorit');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Produk berhasil ditambahkan ke daftar favorit');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function remove_favourite_services()
    {
        try {
            $this->db->trans_begin();

            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                $this->db->trans_rollback();

                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Anda belum login');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);

            $get = $this->msproduct->get(array(
                'id' => $id,
                'userid' => $this->merchant->id,
                'category_apikey' => 'PPOB',
                'subcategory_apikey' => 'PRABAYAR'
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            $row = $get->row();

            $favourite = $this->productfavourite->total(array(
                'userid' => getCurrentIdUser(),
                'productid' => $row->id
            ));

            if ($favourite == 0) {
                throw new Exception('Produk tidak ada di daftar favorit');
            }

            $this->productfavourite->delete(array(
                'userid' => getCurrentIdUser(),
                'productid' => $row->id
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menghapus produk dari daftar favorit');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Produk berhasil dihapus dari daftar favorit');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function favourite()
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $data = array();
        $data['title'] = 'Favorit';
        $data['content'] = 'ppob/favourite';

        return viewTemplate($this->merchant->id, 'master', $data);
    }

    public function datatables_favourite_ppob()
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            if (isLogin()) {
                $data = array();

                $datatables = $this->datatables->make('ProductFavourite', 'QueryDatatables', 'SearchDatatables');

                $currenttheme = getCurrentThemeConfiguration($this->merchant->id);

                if ($currenttheme != 'HighAdmin-TopNav' && $currenttheme != 'Dason-TopNav' && $currenttheme != 'Able') {
                    $label = "label label";
                } else if ($currenttheme == 'Dason-TopNav' || $currenttheme == 'Able') {
                    $label = "badge bg";
                } else {
                    $label = "badge badge";
                }

                $where = array(
                    'a.userid' => getCurrentIdUser(),
                    'b.category_apikey' => 'PPOB',
                );

                if ($this->merchant->multivendor != 1) {
                    $vendor = getCurrentVendor('PPOB', $this->merchant->id);
                    $where["(b.vendor = '$vendor' OR b.vendor IS NULL) ="] = true;
                    $where['b.vendorid'] = null;
                } else {
                    $where["((b.vendorid IS NOT NULL AND b.vendorenabled = 1) OR b.vendor IS NULL) ="] = true;
                }

                foreach ($datatables->getData($where) as $key => $value) {
                    if ($value->status == 1) {
                        $status = "<span class=\"$label-success\">Normal</span>";
                    } else {
                        $status = "<span class=\"$label-danger\">Gangguan</span>";
                    }

                    $detail = array();
                    $detail[] = $value->code;
                    $detail[] = $value->productname;
                    $detail[] = $value->category;
                    $detail[] = $value->brand;
                    $detail[] = $value->description;
                    $detail[] = IDR($value->price);
                    $detail[] = $status;
                    $detail[] = "<button type=\"button\" class=\"btn btn-icon btn-danger btn-sm mb-1\" onclick=\"deleteFavourite('" . stringEncryption('encrypt', $value->id) . "')\">
                    <i class=\"fa fa-trash\"></i>
                </button>";

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Anda belum login');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function delete_favourite_ppob()
    {
        try {
            $this->db->trans_begin();

            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                $this->db->trans_rollback();

                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Anda belum login');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);

            $get = $this->productfavourite->total(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ));

            if ($get == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            $this->productfavourite->delete(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menghapus produk dari daftar favorit');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Produk berhasil dihapus dari daftar favorit');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function information()
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $currentuserid = $this->merchant->id;

        $where_vendor = "";
        if ($this->merchant->multivendor != 1) {
            $vendorppob = getCurrentVendor('PPOB', $currentuserid);
            $where_vendor = "vendor = '$vendorppob' AND vendorid IS NULL";
        } else {
            $where_vendor = "vendorid IS NOT NULL AND vendorenabled = 1";
        }

        $data = array();
        $data['title'] = 'Informasi Layanan';
        $data['content'] = 'ppob/information';
        $data['category'] = $this->db->query("SELECT * FROM ( SELECT category FROM msproduct WHERE userid = $currentuserid AND $where_vendor GROUP BY category UNION ALL SELECT NAME AS category FROM mscategory WHERE userid = $currentuserid GROUP BY NAME ) a WHERE a.category IS NOT NULL ORDER BY a.category ASC")->result();

        return viewTemplate($this->merchant->id, 'master', $data);
    }

    public function datatables_information_ppob()
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            if (isLogin()) {
                $data = array();

                $datatables = $this->datatables->make('ProductPriceLog', 'QueryDatatables', 'SearchDatatables');

                $category = getPost('category');

                $where = array(
                    'a.userid' => $this->merchant->id,
                    'b.category_apikey' => 'PPOB',
                    'b.subcategory_apikey' => 'PRABAYAR',
                );

                if ($category != null) {
                    $where['b.category'] = $category;
                }

                $currentuser = getCurrentUser();

                if ($currentuser->roleid == null) {
                    $getrole = $this->msrole->get(array(
                        'createdby' => $this->merchant->id,
                        'isdefault' => '1'
                    ))->row();
                } else {
                    $getrole = $this->msrole->get(array(
                        'createdby' => $this->merchant->id,
                        'id' => $currentuser->roleid
                    ))->row();
                }

                if ($getrole != null) {
                    if ($getrole->discounttype == 'Simple') {
                        $getrolediscount = $this->msrolediscount->get(array(
                            'userid' => $this->merchant->id,
                            'roleid' => $getrole->id
                        ))->row();

                        $discount = $getrolediscount->trxdiscount ?? 0;
                    } else {
                        $discount = $this->msrolediscountadv->get(array(
                            'createdby' => $this->merchant->id,
                            'roleid' => $getrole->id,
                            '(servicetype = "Prabayar" OR servicetype = "Pascabayar") =' => true
                        ))->result();
                    }
                } else {
                    $getrole = new stdClass();
                    $getrole->discounttype = 'Simple';
                    $getrole->id = 0;
                }

                foreach ($datatables->getData($where) as $key => $value) {
                    if ($value->type == 'UP') {
                        $status = "<span class=\"badge badge-success label label-success bg-success\">Harga Naik</span>";
                    } else {
                        $status = "<span class=\"badge badge-danger label label-danger bg-danger\">Harga Turun</span>";
                    }

                    $detail = array();
                    $detail[] = $value->productname;
                    $detail[] = $status;

                    if ($value->subcategory_apikey == 'PRABAYAR') {
                        if ($getrole->discounttype == 'Simple') {
                            $detail[] = "Rp " . IDR($value->oldprice - $discount);
                        } else {
                            $found = false;

                            foreach ($discount as $val) {
                                if ($found) continue;

                                if ($val->startrange <= $value->oldprice && $val->endrange >= $value->oldprice && strtoupper($val->servicetype) == 'PRABAYAR') {
                                    if ($val->discounttype == 'Persentase') {
                                        $detail[] = "Rp " . IDR($value->oldprice - ($value->oldprice * $val->nominal / 100));

                                        $found = true;
                                    } else {
                                        $detail[] = "Rp " . IDR($value->oldprice - $val->nominal);

                                        $found = true;
                                    }
                                }
                            }

                            if ($found == false) {
                                $detail[] = "Rp " . IDR($value->oldprice);
                            }
                        }
                    } else {
                        $detail[] = '-';
                    }

                    if ($value->subcategory_apikey == 'PRABAYAR') {
                        if ($getrole->discounttype == 'Simple') {
                            $detail[] = "Rp " . IDR($value->newprice - $discount);
                        } else {
                            $found = false;

                            foreach ($discount as $val) {
                                if ($found) continue;

                                if ($val->startrange <= $value->newprice && $val->endrange >= $value->newprice && strtoupper($val->servicetype) == 'PRABAYAR') {
                                    if ($val->discounttype == 'Persentase') {
                                        $detail[] = "Rp " . IDR($value->newprice - ($value->newprice * $val->nominal / 100));

                                        $found = true;
                                    } else {
                                        $detail[] = "Rp " . IDR($value->newprice - $val->nominal);

                                        $found = true;
                                    }
                                }
                            }

                            if ($found == false) {
                                $detail[] = "Rp " . IDR($value->newprice);
                            }
                        }
                    } else {
                        $detail[] = '-';
                    }
                    $detail[] = DateFormat($value->createddate, 'd F Y H:i:s');

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Anda belum login');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    private function send_notification($orderid, $userid, $phonenumber)
    {
        // Kirim Firebase notification untuk order
        sendFirebaseNotificationOrder($orderid, $userid);

        $apikeys_whatsapp = $this->apikeys_whatsapp->get(array(
            'userid' => $userid,
        ));

        if ($apikeys_whatsapp->num_rows() == 0) {
            return false;
        }

        $row = $apikeys_whatsapp->row();

        $wablasingateway = new WABlasinGateway(stringEncryption('decrypt', $row->apikey), stringEncryption('decrypt', $row->devicecode));

        $messagenotification = replaceParameterNotification($orderid, $userid);
        if ($messagenotification != null && $phonenumber != null) {
            $phonenumber = changePrefixPhone($phonenumber);

            $send = $wablasingateway->sendMessage($phonenumber, $messagenotification);

            if (!isset($send->RESULT) || (isset($send->RESULT) && $send->RESULT != 'OK')) {
                log_message_user('error', '[WHATSAPP SEND MESSAGE] Send notification failed: ' . json_encode($send), $userid);
            }

            return true;
        }

        return false;
    }

    public function modalContact()
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $getContact = $this->mscontact->get(array(
            'userid' => getCurrentIdUser(),
            'contacttype' => 'PPOB'
        ))->result();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => viewTemplate($this->merchant->id, "ppob/modalcontact", array(
                'data' => $getContact
            ), true)
        ));
    }

    public function modalContactPascabayar()
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $getContact = $this->mscontact->get(array(
            'userid' => getCurrentIdUser(),
            'contacttype' => 'PPOB'
        ))->result();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => viewTemplate($this->merchant->id, "ppob/modalcontact", array(
                'data' => $getContact
            ), true)
        ));
    }

    public function export_services_ppob()
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            if (isLogin()) {
                $start = 'A1';

                $letter = (string) preg_replace('/[0-9]+/', '', $start);
                $rowstart = (int) preg_replace('/[A-z]+/', '', $start);

                $spreadsheet = new Spreadsheet();
                $sheet = $spreadsheet->getActiveSheet();

                $letters = $letter;
                $sheet->setCellValue($letters++ . $rowstart, "Kode Produk")
                    ->setCellValue($letters++ . $rowstart, "Nama Produk")
                    ->setCellValue($letters++ . $rowstart, "Kategori")
                    ->setCellValue($letters++ . $rowstart, "Brand")
                    ->setCellValue($letters++ . $rowstart, "Deskripsi")
                    ->setCellValue($letters++ . $rowstart, "Harga")
                    ->setCellValue($letters . $rowstart++, "Status");

                $where = array(
                    'a.userid' => $this->merchant->id,
                    'a.category_apikey' => 'PPOB',
                    'b.id' => null,
                    'a.status' => 1
                );

                if ($this->merchant->multivendor != 1) {
                    $vendor = getCurrentVendor('PPOB', $this->merchant->id);
                    $where["(a.vendor = '$vendor' OR a.vendor IS NULL) ="] = true;
                    $where['a.vendorid'] = null;
                } else {
                    $where["((a.vendorid IS NOT NULL AND a.vendorenabled = 1) OR a.vendor IS NULL) ="] = true;
                }

                $category = getGet('category');
                $brand = getGet('brand');

                if ($category != '') {
                    $where['a.category'] = $category;
                }

                if ($brand != '') {
                    $where['a.brand'] = $brand;
                }

                $currentuser = getCurrentUser();

                if ($currentuser->roleid == null) {
                    $getrole = $this->msrole->get(array(
                        'createdby' => $this->merchant->id,
                        'isdefault' => '1'
                    ))->row();
                } else {
                    $getrole = $this->msrole->get(array(
                        'createdby' => $this->merchant->id,
                        'id' => $currentuser->roleid
                    ))->row();
                }

                if ($getrole != null) {
                    if ($getrole->discounttype == 'Simple') {
                        $getrolediscount = $this->msrolediscount->get(array(
                            'userid' => $this->merchant->id,
                            'roleid' => $getrole->id
                        ))->row();

                        $discount = $getrolediscount->trxdiscount ?? 0;
                    } else {
                        $discount = $this->msrolediscountadv->get(array(
                            'createdby' => $this->merchant->id,
                            'roleid' => $getrole->id,
                            '(servicetype = "Prabayar" OR servicetype = "Pascabayar") =' => true
                        ))->result();
                    }
                } else {
                    $getrole = new stdClass();
                    $getrole->discounttype = 'Simple';
                    $discount = 0;
                }

                $dataprduct = $this->msproduct->select('a.*, b.id AS disabledid, c.id AS favouriteid')
                    ->join('disabledcategory b', 'b.categoryname = a.category AND b.category_apikey = a.category_apikey AND b.userid = a.userid', 'LEFT')
                    ->join('productfavourite c', 'c.productid = a.id AND c.userid = ' . (getCurrentIdUser() ?? '0'), 'LEFT')
                    ->order_by('a.price, a.category, a.brand')->result($where);

                foreach ($dataprduct as $key => $value) {
                    if ($value->status == 1) {
                        $status = "Normal";
                    } else {
                        $status = "Gangguan";
                    }

                    if ($value->subcategory_apikey == 'PRABAYAR') {
                        if ($getrole->discounttype == 'Simple') {
                            $harga = IDR($value->price - $discount);
                        } else {
                            $found = false;

                            foreach ($discount as $val) {
                                if ($found) continue;

                                if ($val->startrange <= $value->price && $val->endrange >= $value->price && strtoupper($val->servicetype) == 'PRABAYAR') {
                                    if ($val->discounttype == 'Persentase') {
                                        $harga = IDR($value->price - ($value->price * $val->nominal / 100));

                                        $found = true;
                                    } else {
                                        $harga = IDR($value->price - $val->nominal);

                                        $found = true;
                                    }
                                }
                            }

                            if ($found == false) {
                                $harga =  IDR($value->price);
                            }
                        }
                    } else {
                        if ($getrole->discounttype == 'Simple') {
                            $harga = IDR($value->admin + $value->profit - $discount);
                        } else {
                            $found = false;

                            foreach ($discount as $val) {
                                if ($found) continue;

                                if ($val->startrange <= $value->admin + $value->profit && $val->endrange >= $value->admin + $value->profit && strtoupper($val->servicetype) == 'PASCABAYAR') {
                                    if ($val->discounttype == 'Persentase') {
                                        $harga = IDR($value->admin + $value->profit - ($value->admin + $value->profit * $val->nominal / 100));

                                        $found = true;
                                    } else {
                                        $harga = IDR($value->admin + $value->profit - $val->nominal);

                                        $found = true;
                                    }
                                }
                            }

                            if ($found == false) {
                                $harga = IDR($value->admin + $value->profit);
                            }
                        }
                    }

                    $letters = $letter;

                    $sheet->setCellValue($letters++ . $rowstart, $value->code)
                        ->setCellValue($letters++ . $rowstart, $value->productname)
                        ->setCellValue($letters++ . $rowstart, $value->category)
                        ->setCellValue($letters++ . $rowstart, $value->brand)
                        ->setCellValue($letters++ . $rowstart, $value->description ?? '-')
                        ->setCellValue($letters++ . $rowstart, $harga)
                        ->setCellValue($letters . $rowstart, $status);

                    $rowstart++;
                }


                foreach (range('A', $sheet->getHighestDataColumn()) as $col) {
                    $sheet->getColumnDimension($col)
                        ->setAutoSize(true);
                }

                $sheet->getStyle("A1:$letters" . --$rowstart)->applyFromArray(array(
                    'borders' => array(
                        'allBorders' => array(
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN
                        )
                    )
                ));

                $writer = new Xlsx($spreadsheet);
                $filename = "Daftar Harga PPOB";

                header('Content-Type: application/vnd.ms-excel');
                header('Content-Disposition: attachment;filename="' . $filename . '.xlsx"');
                header('Cache-Control: max-age=0');

                $writer->save('php://output');
            } else {
                throw new Exception('Anda belum login');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
