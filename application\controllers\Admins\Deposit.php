<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property Datatables $datatables
 * @property Deposits $deposits
 * @property MsUsers $msusers
 * @property MsPaymentGateway $paymentgateway
 * @property MsPaymentMethod $paymentmethod
 * @property FeePaymentGateway $feepaymentgateway
 * @property HistoryWrongNominal $historywrongnominal
 * @property HistoryBalance $historybalance
 * @property CI_DB_mysqli_driver $db
 * @property ApiKeys_WhatsApp $apikeys_whatsapp
 */
class Deposit extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Deposits', 'deposits');
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('MsPaymentGateway', 'paymentgateway');
        $this->load->model('MsPaymentMethod', 'paymentmethod');
        $this->load->model('FeePaymentGateway', 'feepaymentgateway');
        $this->load->model('HistoryWrongNominal', 'historywrongnominal');
        $this->load->model('HistoryBalance', 'historybalance');
        $this->load->model('ApiKeys_WhatsApp', 'apikeys_whatsapp');
    }

    public function history()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isAdmin()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Riwayat Topup';
        $data['content'] = 'admin/deposit/history';
        $data['users'] = $this->deposits->select('a.userid, b.name')
            ->join('msusers b', 'b.id = a.userid')
            ->where('b.isdeleted', null)
            ->where('b.merchantid', null)
            ->group_by('a.userid')
            ->order_by('b.name', 'ASC')
            ->get()
            ->result();

        $this->load->view('master', $data);
    }

    public function datatables_history()
    {
        try {
            if (isLogin() && isAdmin()) {
                $data = array();
                $date = getPost('date');
                $code = getPost('code');
                $users = getPost('users');
                $payment = getPost('payment');
                $status = getPost('status');

                $datatable = $this->datatables->make('Deposits', 'QueryDatatables', 'SearchDatatables');

                $where = array();
                $where['b.merchantid'] = null;

                if ($date != null && isset(explode(' - ', $date)[0]) && isset(explode(' - ', $date)[1])) {
                    $startdate = explode(' - ', $date)[0];
                    $startdate = date('Y-m-d', strtotime($startdate));

                    $enddate = explode(' - ', $date)[1];
                    $enddate = date('Y-m-d', strtotime($enddate));

                    $where['DATE(a.createddate) >='] = $startdate;
                    $where['DATE(a.createddate) <='] = $enddate;
                } else {
                    $where['DATE(a.createddate) >='] = date('Y-m-01');
                    $where['DATE(a.createddate) <='] = date('Y-m-t');
                }

                if ($code != null) {
                    $where['a.code'] = $code;
                }

                if ($users != null) {
                    $where['a.userid'] = $users;
                }

                if ($payment != null) {
                    $where['a.paymenttype'] = $payment;
                }

                if ($status != null) {
                    if ($status == 'Success') {
                        $where["(a.status = 'settlement' OR a.status = 'Success')="] = TRUE;
                    } else {
                        $where['a.status'] = $status;
                    }
                }

                foreach ($datatable->getData($where) as $key => $value) {
                    $detail = array();

                    if ($value->status == 'Pending') {
                        $status = "<span class=\"badge badge-warning\">Menunggu</span>";
                    } else if ($value->status == 'Success' || $value->status == 'settlement') {
                        $status = "<span class=\"badge badge-success\">Berhasil</span>";
                    } else {
                        $status = "<span class=\"badge badge-danger\">Gagal</span>";
                    }

                    $actions = "";

                    if ($value->status == 'Pending') {
                        $actions .= "<a href=\"javascript:;\" class=\"btn btn-success btn-sm mb-1\" onclick=\"confirmPayment('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-check\"></i>
                            <span>Konfirmasi</span>
                        </a>

                        <button type=\"button\" class=\"btn btn-warning btn-sm mb-1\" onclick=\"wrongNominal('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-credit-card\"></i>
                            <span>Salah Nominal Transfer</span>
                        </button>

                        <button type=\"button\" class=\"btn btn-danger btn-sm mb-1\" onclick=\"cancelPayment('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-times\"></i>
                            <span>Cancel</span>
                        </button>";
                    }

                    if ($actions == "") {
                        $actions = "N/A";
                    }

                    $detail[] = DateFormat($value->createddate, 'd F Y H:i:s');
                    $detail[] = $value->code;
                    $detail[] = $value->buyer_name;
                    $detail[] = $value->paymenttype;
                    $detail[] = $value->payment;
                    $detail[] = IDR($value->nominal);
                    $detail[] = IDR($value->nominal + $value->nominalbonus - $value->uniqueadmin - $value->fee);
                    $detail[] = $status;
                    $detail[] = $actions;

                    $data[] = $detail;
                }

                return $datatable->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    private function send_notification($orderid, $userid, $phonenumber)
    {
        try {
            // Kirim Firebase notification untuk deposit
            sendFirebaseNotificationDeposit($orderid, $userid);

            $apikeys_whatsapp = $this->apikeys_whatsapp->get(array(
                'userid' => $userid,
            ));

            if ($apikeys_whatsapp->num_rows() == 0) {
                return false;
            }

            $row = $apikeys_whatsapp->row();

            $wablasingateway = new WABlasinGateway(stringEncryption('decrypt', $row->apikey), stringEncryption('decrypt', $row->devicecode));

            $messagenotification = replaceParameterNotificationDeposit($orderid, $userid);

            if ($messagenotification != null && $phonenumber != null) {
                $phonenumber = changePrefixPhone($phonenumber);

                $send = $wablasingateway->sendMessage($phonenumber, $messagenotification);

                if (!isset($send->RESULT) || (isset($send->RESULT) && $send->RESULT != 'OK')) {
                    log_message_user('error', '[WHATSAPP SEND MESSAGE] Send notification failed: ' . json_encode($send), $userid);
                }

                return true;
            }

            return false;
        } catch (Exception $ex) {
            return false;
        }
    }

    public function process_confirm_topup()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda harus login terlebih dahulu');
            } else if (!isAdmin()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->deposits->select('a.*')
                ->join('msusers b', 'b.id = a.userid')
                ->get(array(
                    'a.id' => $id,
                    'a.status' => 'Pending',
                    'a.merchantid' => null,
                ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            $check_history_balance = $this->historybalance->total(array(
                'depositid' => $id,
                'type' => 'IN',
                'userid' => $row->userid,
            ));

            if ($check_history_balance == 0) {
                $currentbalance = getCurrentBalance($row->userid, true);

                $balance = $row->nominal;

                $inserthistorybalance = array();
                $inserthistorybalance['userid'] = $row->userid;
                $inserthistorybalance['type'] = 'IN';
                $inserthistorybalance['nominal'] = $balance;
                $inserthistorybalance['currentbalance'] = $currentbalance;
                $inserthistorybalance['depositid'] = $id;
                $inserthistorybalance['createdby'] = $row->userid;
                $inserthistorybalance['createddate'] = getCurrentDate();

                $this->historybalance->insert($inserthistorybalance);

                $update = array();
                $update['balance'] = $currentbalance + $balance;

                $this->msusers->update(array(
                    'id' => $row->userid
                ), $update);
            }

            $update = array();
            $update['status'] = 'Success';

            $this->deposits->update(array(
                'id' => $id
            ), $update);

            if ($row->merchantid != null) {
                $this->send_notification($id, $row->merchantid, $row->phonenumber);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal melakukan konfirmasi');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil dikonfirmasi');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_cancel_topup()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda harus login terlebih dahulu');
            } else if (!isAdmin()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->deposits->select('a.*')
                ->join('msusers b', 'b.id = a.userid')
                ->get(array(
                    'a.id' => $id,
                    'a.status' => 'Pending',
                    'a.merchantid' => null,
                ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            $update = array();
            $update['status'] = 'Cancel';

            $this->deposits->update(array(
                'id' => $id
            ), $update);

            $this->send_notification($id, $row->merchantid, $row->phonenumber);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal membatalkan permintaan deposit');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Permintaan deposit berhasil dibatalkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function wrong_nominal()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isAdmin()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->deposits->get(array(
                'id' => $id,
                'status' => 'Pending',
                'merchantid' => null,
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('admins/deposit/wrong_nominal', array(
                    'deposit' => $row
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_wrong_nominal()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda harus login terlebih dahulu');
            } else if (!isAdmin()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');
            $nominal = getPost('nominal');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            } else if ($nominal == null) {
                throw new Exception('Nominal wajib diisi');
            } else if (!is_numeric($nominal)) {
                throw new Exception('Nominal harus berupa angka');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->deposits->select('a.*')
                ->join('msusers b', 'b.id = a.userid')
                ->get(array(
                    'a.id' => $id,
                    'a.status' => 'Pending',
                    'a.merchantid' => null,
                ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            $update = array();
            $update['nominal'] = $nominal;
            $update['nominalbonus'] = 0;
            $update['fee'] = 0;
            $update['uniqueadmin'] = 0;
            $update['status'] = 'Success';

            $this->deposits->update(array(
                'id' => $id
            ), $update);

            $this->send_notification($id, $row->merchantid, $row->phonenumber);

            $check_history_balance = $this->historybalance->total(array(
                'depositid' => $id,
                'type' => 'IN',
                'userid' => $row->userid,
            ));

            if ($check_history_balance == 0) {
                $currentbalance = getCurrentBalance($row->userid, true);

                $inserthistorybalance = array();
                $inserthistorybalance['userid'] = $row->userid;
                $inserthistorybalance['type'] = 'IN';
                $inserthistorybalance['nominal'] = $nominal;
                $inserthistorybalance['currentbalance'] = $currentbalance;
                $inserthistorybalance['depositid'] = $id;
                $inserthistorybalance['createdby'] = $row->userid;
                $inserthistorybalance['createddate'] = getCurrentDate();

                $this->historybalance->insert($inserthistorybalance);

                $update = array();
                $update['balance'] = $currentbalance + $nominal;

                $this->msusers->update(array(
                    'id' => $row->userid
                ), $update);
            }

            $insert = array();
            $insert['depositid'] = $id;
            $insert['correctnominal'] = $nominal;
            $insert['recalculatebonus'] = 0;
            $insert['recalculatefee'] = 0;
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = getCurrentIdUser();

            $this->historywrongnominal->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengkonfirmasi data');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil dikonfirmasi');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
