<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property MsProduct $msproduct
 * @property CI_Output $output
 * @property MsUsers $msusers
 * @property MobileSession $mobilesession
 * @property DisabledCategory $disabledcategory
 * @property DisabledBrand $disabledbrand
 * @property MsProduct $msproduct
 * @property TrOrder $trorder
 * @property CI_DB_query_builder|CI_DB_mysqli_driver $db
 * @property ApiKeys $apikeys
 * @property MsRole $msrole
 * @property MsRoleDiscount $msrolediscount
 * @property MsRoleDiscountAdv $msrolediscountadv
 * @property HistoryBalance $historybalance
 * @property ApiKeys_WhatsApp $apikeys_whatsapp
 * @property MsBrand $msbrand
 * @property MsVendorDetail $msvendordetail
 * @property MsStockproduct $msstockproduct
 * @property MsStockproductDetail $msstockproductdetail
 */
class Order extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsProduct', 'msproduct');
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('MobileSession', 'mobilesession');
        $this->load->model('DisabledCategory', 'disabledcategory');
        $this->load->model('DisabledBrand', 'disabledbrand');
        $this->load->model('MsProduct', 'msproduct');
        $this->load->model('TrOrder', 'trorder');
        $this->load->model('ApiKeys', 'apikeys');
        $this->load->model('MsRole', 'msrole');
        $this->load->model('MsRoleDiscount', 'msrolediscount');
        $this->load->model('MsRoleDiscountAdv', 'msrolediscountadv');
        $this->load->model('HistoryBalance', 'historybalance');
        $this->load->model('ApiKeys_WhatsApp', 'apikeys_whatsapp');
        $this->load->model('MsBrand', 'msbrand');
        $this->load->model('MsVendorDetail', 'msvendordetail');
        $this->load->model('MsStockproduct', 'msstockproduct');
        $this->load->model('MsStockproductDetail', 'msstockproductdetail');
    }

    private function send_notification($orderid, $userid, $phonenumber)
    {
        // Kirim Firebase notification untuk order
        sendFirebaseNotificationOrder($orderid, $userid);

        $apikeys_whatsapp = $this->apikeys_whatsapp->get(array(
            'userid' => $userid,
        ));

        if ($apikeys_whatsapp->num_rows() == 0) {
            return false;
        }

        $row = $apikeys_whatsapp->row();

        $wablasingateway = new WABlasinGateway(stringEncryption('decrypt', $row->apikey), stringEncryption('decrypt', $row->devicecode));

        $messagenotification = replaceParameterNotification($orderid, $userid);
        if ($messagenotification != null && $phonenumber != null) {
            $phonenumber = changePrefixPhone($phonenumber);

            $send = $wablasingateway->sendMessage($phonenumber, $messagenotification);

            if (!isset($send->RESULT) || (isset($send->RESULT) && $send->RESULT != 'OK')) {
                log_message_user('error', '[WHATSAPP SEND MESSAGE] Send notification failed: ' . json_encode($send), $userid);
            }

            return true;
        }

        return false;
    }

    public function provider()
    {
        $category = getPost('category');

        if ($category == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Kategori tidak boleh kosong',
            ));
        }

        $where = array(
            'a.userid' => $this->merchant->id,
            'a.category' => $category,
        );

        if ($this->merchant->multivendor != 1) {
            $vendor = getCurrentVendor('PPOB', $this->merchant->id);
            $where['a.vendor'] = $vendor;
            $where['a.vendorid'] = null;
        } else {
            $where['a.vendorid !='] = null;
            $where['a.endorenabled'] = 1;
        }

        $provider = $this->msproduct->select('a.brand, b.prefix')
            ->join('msprefixoperator b', 'b.operatorname = a.brand AND b.userid = a.userid', 'LEFT')
            ->group_by('a.brand')
            ->order_by('a.brand')
            ->result($where);

        $brand = $this->msbrand->select('a.name AS brand, b.prefix')
            ->join('msprefixoperator b', 'b.operatorname = a.name AND b.userid = a.userid', 'LEFT')
            ->group_by('a.name')
            ->order_by('a.name')
            ->result(array(
                'a.userid' => $this->merchant->id,
                'a.category' => $category
            ));

        $brand = array_merge($provider, $brand);

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => true,
            'message' => 'Data berhasil diambil',
            'data' => $brand,
        ));
    }

    public function subprovider()
    {
        $category = getPost('category');
        $brand = getPost('brand');

        if ($category == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Kategori tidak boleh kosong',
            ));
        } else if ($brand == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Provider tidak boleh kosong',
            ));
        }

        $where = array(
            'userid' => $this->merchant->id,
            'category' => $category,
            'brand' => $brand,
            'type !=' => null
        );

        if ($this->merchant->multivendor != 1) {
            $vendor = getCurrentVendor('PPOB', $this->merchant->id);
            $where["(vendor = '$vendor' OR vendor IS NULL) ="] = true;
            $where['vendorid'] = null;
        } else {
            $where["((vendorid IS NOT NULL AND vendorenabled = 1) OR vendor IS NULL) ="] = true;
        }

        $subprovider = $this->msproduct->select('type')
            ->group_by('type')
            ->order_by('type')
            ->result($where);

        if (count($subprovider) == 0) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Data tidak ditemukan',
            ));
        }

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => true,
            'message' => 'Data berhasil diambil',
            'data' => $subprovider,
        ));
    }

    public function product()
    {
        $token = getPost('token');
        $category = getPost('category');
        $brand = getPost('brand');
        $subprovider = getPost('type');

        if ($category == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Kategori tidak boleh kosong',
            ));
        }

        if ($brand == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Provider tidak boleh kosong',
            ));
        }

        $validate = $this->validateToken($token);

        if ($validate['status'] == false) {
            $this->output->set_status_header(401);
            $this->output->set_content_type('application/json');

            return JSONResponse($validate);
        }

        $currentuser = $validate['data'];

        if ($currentuser->roleid == null) {
            $getrole = $this->msrole->get(array(
                'createdby' => $this->merchant->id,
                'isdefault' => '1'
            ))->row();
        } else {
            $getrole = $this->msrole->get(array(
                'createdby' => $this->merchant->id,
                'id' => $currentuser->roleid
            ))->row();
        }

        if ($getrole != null) {
            if ($getrole->discounttype == 'Simple') {
                $getrolediscount = $this->msrolediscount->get(array(
                    'userid' => $this->merchant->id,
                    'roleid' => $getrole->id,
                ))->row();

                $discount = $getrolediscount->trxdiscount ?? 0;
            } else {
                $discount = $this->msrolediscountadv->result(array(
                    'createdby' => $this->merchant->id,
                    'roleid' => $getrole->id,
                    'servicetype' => 'Prabayar'
                ));
            }
        } else {
            $getrole = new stdClass();
            $getrole->discounttype = "Simple";
            $discount = 0;
        }

        $where = array(
            'a.userid' => $this->merchant->id,
            'a.category' => $category,
            'a.brand' => $brand,
            'a.status' => 1,
        );

        if ($this->merchant->multivendor != 1) {
            $vendor = getCurrentVendor('PPOB', $this->merchant->id);
            $where["(a.vendor = '$vendor' OR a.vendor IS NULL) ="] = true;
            $where['a.vendorid'] = null;
        } else {
            $where["((a.vendorid IS NOT NULL AND a.vendorenabled = 1) OR a.vendor IS NULL) ="] = true;
        }

        if ($subprovider != null) {
            $where["((a.type IS NOT NULL AND a.type = '$subprovider') OR (a.type IS NULL)) ="] = true;
        }

        $product = $this->msproduct->select('a.id, a.productname, a.price, a.description, c.asseturl')
            ->join('brandimage b', 'b.brandname = a.brand AND b.categoryname = a.category AND b.userid = a.userid', 'LEFT')
            ->join('msicons c', 'c.id = b.assetid', 'LEFT')
            ->order_by('a.price')
            ->result($where);

        foreach ($product as $key => $value) {
            if ($getrole->discounttype == 'Simple') {
                $product[$key]->price = $value->price - $discount;
            } else {
                $found = false;
                foreach ($discount as $k => $v) {
                    if ($found) continue;

                    if ($v->startrange <= $value->price && $v->endrange >= $value->price) {
                        if ($v->discounttype == 'Persentase') {
                            $product[$key]->price = round($value->price - ($value->price * ($v->nominal / 100)));
                            $found = true;
                        } else {
                            $product[$key]->price = round($value->price - $v->nominal);
                            $found = true;
                        }
                    }
                }

                if ($found == false) {
                    $product[$key]->price = round($value->price);
                }
            }
        }

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => true,
            'message' => 'Data berhasil diambil',
            'data' => $product,
        ));
    }

    private function validateToken($token)
    {
        if ($token == null) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $token = stringEncryption('decrypt', $token);

        if ($token == null) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $token = json_decode($token);

        if (json_last_error() != JSON_ERROR_NONE) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $validate = $this->mobilesession->total(array(
            'token' => getPost('token')
        ));

        if ($validate == 0) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $user = $this->msusers->get(array(
            'id' => $token->id,
            'merchantid' => $this->merchant->id,
        ));

        if ($user->num_rows() == 0) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $row = $user->row();

        return array(
            'status' => true,
            'message' => 'Data berhasil diambil',
            'data' => $row
        );
    }

    public function process()
    {
        try {
            $this->db->trans_begin();

            $token = getPost('token');
            $product = getPost('product');
            $target = getPost('target');
            $pin = getPost('pin');
            $zoneid = null;

            if ($product == null) {
                throw new Exception('Produk tidak boleh kosong');
            }

            if ($target == null) {
                throw new Exception('Tujuan tidak boleh kosong');
            }

            if ($pin == null) {
                throw new Exception('PIN tidak boleh kosong');
            }

            $validate = $this->validateToken($token);

            if ($validate['status'] == false) {
                throw new Exception('Autorisasi ditolak');
            }

            $user = $validate['data'];

            if (stringEncryption('encrypt', $pin) != $user->pin) {
                throw new Exception('PIN Transaksi yang anda masukkan salah');
            }

            $disabled = $this->disabledcategory->result(array(
                'userid' => $this->merchant->id,
                'category_apikey' => 'PPOB'
            ));

            $disabled_category = array();
            foreach ($disabled as $key => $value) {
                $disabled_category[] = $value->categoryname;
            }

            $disabledbrand = $this->disabledbrand->result(array(
                'userid' => $this->merchant->id,
            ));

            $disabled_brand = array();
            foreach ($disabledbrand as $key => $value) {
                $disabled_brand[] = $value->brandname;
            }

            $where = array(
                'id' => $product,
                'userid' => $this->merchant->id,
                'subcategory_apikey' => 'PRABAYAR'
            );

            if ($this->merchant->multivendor != 1) {
                $vendor = getCurrentVendor('PPOB', $this->merchant->id);
                $where["(vendor = '$vendor' OR vendor IS NULL) ="] = true;
                $where['vendorid'] = null;
            } else {
                $where["((vendorid IS NOT NULL AND vendorenabled = 1) OR vendor IS NULL) ="] = true;
            }

            $getproduct = $this->msproduct->where_not_in('category', $disabled_category)
                ->where_not_in('brand', $disabled_brand)
                ->get($where);

            if ($getproduct->num_rows() == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            $productRow = $getproduct->row();

            if ($productRow->status != 1) {
                throw new Exception('Produk tidak tersedia');
            }

            if ($productRow->isstock == 1) {
                $stock = $productRow->stock;

                if ($stock == 0) {
                    throw new Exception('Produk tidak tersedia');
                }
            }

            if ($productRow->isdatabase == 1 && $productRow->databasecategory == 'Stock Product') {
                $databaseid = $productRow->databaseid;

                $stockproduct = $this->msstockproduct->get(array(
                    'id' => $databaseid
                ))->row();

                if ($stockproduct == null) {
                    throw new Exception('Produk tidak tersedia');
                } else {
                    $available_stock = $this->msstockproductdetail->total(array(
                        'stockproductid' => $databaseid,
                        'status' => 'Tersedia'
                    ));

                    if ($available_stock == 0) {
                        throw new Exception('Produk sedang kosong');
                    }
                }
            }

            $currentuser = $user;

            if ($currentuser->roleid == null) {
                $getrole = $this->msrole->get(array(
                    'createdby' => $this->merchant->id,
                    'isdefault' => '1'
                ))->row();
            } else {
                $getrole = $this->msrole->get(array(
                    'createdby' => $this->merchant->id,
                    'id' => $currentuser->roleid
                ))->row();
            }

            if ($getrole != null) {
                if ($getrole->discounttype == 'Simple') {
                    $getrolediscount = $this->msrolediscount->get(array(
                        'userid' => $this->merchant->id,
                        'roleid' => $getrole->id,
                    ))->row();

                    $discount = $getrolediscount->trxdiscount ?? 0;
                } else {
                    $discount = $this->msrolediscountadv->result(array(
                        'createdby' => $this->merchant->id,
                        'roleid' => $getrole->id,
                        'servicetype' => 'Prabayar'
                    ));
                }
            } else {
                $getrole = new stdClass();
                $getrole->discounttype = "Simple";
                $discount = 0;
            }

            $price = $productRow->price;

            if ($getrole->discounttype == "Simple") {
                $price = $price - $discount;
            } else {
                $found = false;
                foreach ($discount as $key => $value) {
                    if ($found) continue;

                    if ($value->startrange <= $price && $value->endrange >= $price) {
                        if ($value->discounttype == "Persentase") {
                            $price = $price - ($price * $value->nominal / 100);
                            $found = true;
                        } else {
                            $price = $price - $value->nominal;
                            $found = true;
                        }
                    }
                }

                if ($found == false) {
                    $price = $price;
                }
            }

            if ($user->balance < $price) {
                throw new Exception('Saldo anda tidak mencukupi');
            }

            $getpending = $this->trorder->total(array(
                'userid' => $user->id,
                'serviceid' => $productRow->id,
                'target' => $target,
                'status' => 'pending'
            ));

            if ($getpending > 0) {
                throw new Exception('Anda memiliki transaksi yang masih dalam proses');
            }

            $queuetransaction = false;
            if ($productRow->vendor != null) {
                if (preg_match('/(mobile legends)|(mobilelegends)|(mobile legend)|(mobilelegend)/', strtolower($productRow->brand)) && $productRow->vendor == 'VIPayment') {
                    if ($zoneid == null) {
                        throw new Exception('Zone ID wajib diisi');
                    }
                }

                if ($this->merchant->multivendor != 1) {
                    $apikeys = getCurrentAPIKeys('PPOB', $this->merchant->id);
                    if ($apikeys->balance < $productRow->vendorprice) {
                        $queuetransaction = true;
                    }
                } else {
                    if (getCurrentBalanceVendor($this->merchant->id, $productRow->vendorid) < $productRow->vendorprice) {
                        $queuetransaction = true;
                    }
                }
            }

            $potonganprofit = $productRow->price - $price;

            if ($productRow->isstock == 1) {
                $this->msproduct->update(array(
                    'id' => $productRow->id
                ), array(
                    'stock' => $productRow->stock - 1
                ));
            }

            $insert = array();
            $insert['userid'] = $user->id;
            $insert['clientcode'] = generateTransactionNumber('PRABAYAR');
            $insert['serviceid'] = $productRow->id;
            $insert['target'] = $target;
            $insert['price'] = $price;
            $insert['profit'] = ($productRow->profit - $potonganprofit);
            $insert['currentsaldo'] = $user->balance;
            $insert['status'] = 'pending';
            $insert['type'] = 'PPOB';
            $insert['queuetransaction'] = $queuetransaction ? 1 : 0;
            $insert['category_apikey'] = $productRow->category_apikey;
            $insert['subcategory_apikey'] = $productRow->subcategory_apikey;
            $insert['orderplatform'] = 'apps';
            $insert['productcode'] = $productRow->code;
            $insert['vendor'] = $productRow->vendor;
            $insert['productname_order'] = $productRow->productname;
            $insert['merchantid_order'] = $this->merchant->id;
            $insert['status_payment'] = 'sukses';
            $insert['vendorid'] = $productRow->vendorid;

            if ($productRow->vendor != null) {
                if (preg_match('/(mobile legends)|(mobilelegends)|(mobile legend)|(mobilelegend)/', strtolower($productRow->brand)) && $productRow->vendor == 'VIPayment') {
                    $insert['zoneid'] = $zoneid;
                }
            }

            $this->trorder->insert($insert);
            $orderid = $this->db->insert_id();

            $inserthistorybalance = array();
            $inserthistorybalance['userid'] = $user->id;
            $inserthistorybalance['type'] = 'OUT';
            $inserthistorybalance['nominal'] = $price;
            $inserthistorybalance['currentbalance'] = $user->balance;
            $inserthistorybalance['orderid'] = $orderid;
            $inserthistorybalance['createdby'] = $user->id;
            $inserthistorybalance['createddate'] = getCurrentDate();

            $this->historybalance->insert($inserthistorybalance);

            $update = array();
            $update['balance'] = $user->balance - $price;

            $this->msusers->update(array(
                'id' => $user->id,
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal melakukan transaksi');
            }

            pullTransaction($this->merchant->id);

            if ($queuetransaction == false && $productRow->vendor != null && ENVIRONMENT == 'production') {
                if ($this->merchant->multivendor != 1) {
                    if ($vendor == 'Digiflazz') {
                        $digiflazz = new Digiflazz(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));

                        $servercode = generateTransactionNumber('ORDER');
                        $topup = $digiflazz->topup($productRow->code, $target, $servercode);

                        $result = json_decode($topup);

                        if (isset($result->data->status)) {
                            if ($result->data->status != 'Gagal') {
                                $update = array();
                                $update['servercode'] = $servercode;
                                $update['note'] = $result->data->message;
                                $update['sn'] = $result->data->sn ?? '';
                                $update['status'] = strtolower($result->data->status);
                                $update['jsonresponse'] = json_encode($result);

                                $this->trorder->update(array(
                                    'id' => $orderid
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($orderid, $this->merchant->id, $user->phonenumber);

                                $this->output->set_status_header(200);
                                $this->output->set_content_type('application/json');

                                return JSONResponse(array(
                                    'status' => true,
                                    'message' => 'Transaksi berhasil',
                                    'transactionid' => $orderid,
                                ));
                            } else {
                                if ($result != null) {
                                    log_message_user('error', '[DIGIFLAZZ PRABAYAR] Response: ' . json_encode($result), $this->merchant->id);
                                }

                                $responsecode = getResponseCodeDigiflazz($result->data->rc);
                                throw new Exception($responsecode);
                            }
                        } else {
                            throw new Exception('Gagal melakukan transaksi');
                        }
                    } else if ($vendor == 'VIPayment') {
                        $vipayment = new VIPayment(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));

                        if ($productRow->vendor == 'VIPayment' && $productRow->type == 'game') {
                            if (preg_match('/(mobile legends)|(mobilelegends)|(mobile legend)|(mobilelegend)/', strtolower($productRow->brand))) {
                                $order = $vipayment->order_game($productRow->code, $target, $zoneid);
                            } else {
                                $order = $vipayment->order_game($productRow->code, $target, null);
                            }
                        } else {
                            $order = $vipayment->order_prepaid($productRow->code, $target);
                        }

                        if ($order->result) {
                            $update = array();
                            $update['servercode'] = $order->data->trxid;
                            $update['jsonresponse'] = json_encode($order);

                            $this->trorder->update(array(
                                'id' => $orderid
                            ), $update);

                            $this->db->trans_commit();

                            $this->send_notification($orderid, $this->merchant->id, $user->phonenumber);

                            $this->output->set_status_header(200);
                            $this->output->set_content_type('application/json');

                            return JSONResponse(array(
                                'status' => true,
                                'message' => 'Transaksi berhasil',
                                'transactionid' => $orderid,
                            ));
                        } else {
                            if ($order != null) {
                                log_message_user('error', '[VIPAYMENT PRABAYAR] Response: ' . json_encode($order), $this->merchant->id);
                            }

                            throw new Exception('Gagal melakukan transaksi');
                        }
                    }
                } else {
                    $order = $this->msvendordetail->select('a.*, b.default_config')
                        ->join('msvendor b', 'b.id = a.vendorid')
                        ->get(array(
                            'a.vendorid' => $productRow->vendorid,
                            'a.apitype' => 'Order'
                        ));

                    if ($order->num_rows() == 0) {
                        throw new Exception('Vendor tidak ditemukan');
                    }

                    $vendor_order = $order->row();

                    if ($vendor_order->default_config == null) {
                        throw new Exception('Konfigurasi vendor tidak ditemukan');
                    }

                    $response_indicator = json_decode($vendor_order->response_indicator);
                    $response_setting = json_decode($vendor_order->response_setting);

                    $dynamicvendor = new DynamicVendor($productRow->vendorid, json_decode($vendor_order->default_config, true));
                    $order = $dynamicvendor->order($orderid);

                    if (
                        ($response_indicator->index == null && isset($order[$response_indicator->key]) && (
                            $response_indicator->datatype == 'string' && $order[$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'number' && $order[$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'boolean' && $order[$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'exists' && isset($order[$response_indicator->key])
                        ))
                        || ($response_indicator->index != null && isset($order[$response_indicator->index][$response_indicator->key]) && (
                            $response_indicator->datatype == 'string' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'number' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'boolean' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'exists' && isset($order[$response_indicator->index][$response_indicator->key])
                        ))
                    ) {
                        $var_referenceid = $response_setting->referenceid ?? null;
                        $var_price = $response_setting->price ?? null;
                        $var_status = $response_setting->status ?? null;
                        $var_note = $response_setting->note ?? null;
                        $var_sn = $response_setting->sn ?? null;
                        $var_errorrefund = $response_setting->errorrefund;

                        $exploding_errorefund = explode('[#]', strtolower($var_errorrefund ?? ''));

                        if ($response_setting->index != null) {
                            $order = $order[$response_setting->index] ?? null;
                        }

                        $referenceid = $var_referenceid != null ? ($order[$var_referenceid] ?? null) : null;
                        $price = $var_price != null ? ($order[$var_price] ?? null) : null;
                        $status = $var_status != null ? ($order[$var_status] ?? null) : null;
                        $note = $var_note != null ? ($order[$var_note] ?? null) : null;
                        $sn = $var_sn != null ? ($order[$var_sn] ?? null) : null;

                        if ($status != null) {
                            if (in_array($status, $exploding_errorefund)) {
                                log_message_user('error', '[MULTIVENDOR ORDER] Failed to parsing error refund, response: ' . json_encode($order), $this->merchant->id);

                                throw new Exception('Transaksi gagal');
                            }
                        } else {
                            if ($var_status == null) {
                                $status = 'pending';
                            } else if ($var_status != null && $status == null) {
                                log_message_user('error', '[MULTIVENDOR ORDER] Failed to parsing status, response: ' . json_encode($order), $this->merchant->id);

                                throw new Exception('Status transaksi tidak ditemukan');
                            }
                        }

                        $update = array();
                        $update['jsonresponse'] = json_encode($order);

                        if ($referenceid != null) {
                            $update['servercode'] = $referenceid;
                        }

                        if ($price != null) {
                            $update['price'] = $price;
                        }

                        if ($status != null) {
                            $update['status'] = $status;
                        }

                        if ($note != null) {
                            $update['note'] = $note;
                        }

                        if ($sn != null) {
                            $update['sn'] = $sn;
                        }

                        $this->trorder->update(array(
                            'id' => $orderid
                        ), $update);

                        $this->db->trans_commit();

                        $this->send_notification($orderid, $this->merchant->id, $currentuser->phonenumber);

                        $this->output->set_status_header(200);
                        $this->output->set_content_type('application/json');

                        return JSONResponse(array(
                            'status' => true,
                            'message' => 'Transaksi berhasil',
                            'transactionid' => $orderid,
                        ));
                    } else {
                        log_message_user('error', '[MULTIVENDOR ORDER] Failed to parse response, response: ' . json_encode($order), $this->merchant->id);

                        throw new Exception('Vendor tidak ditemukan');
                    }
                }
            }

            if ($productRow->isdatabase == 1 && $productRow->databasecategory == 'Stock Product') {
                $stockproductdetail = $this->msstockproductdetail->get(array(
                    'stockproductid' => $databaseid,
                    'status' => 'Tersedia'
                ))->row();

                $updatestockproductdetail = array();
                $updatestockproductdetail['status'] = 'Tidak Tersedia';
                $updatestockproductdetail['orderid'] = $orderid;

                $this->msstockproductdetail->update(array(
                    'id' => $stockproductdetail->id
                ), $updatestockproductdetail);

                $update = array();
                $update['status'] = 'success';
                $update['sn'] = stringEncryption('decrypt', $stockproductdetail->data);

                $this->trorder->update(array(
                    'id' => $orderid
                ), $update);
            }

            $this->db->trans_commit();

            $this->send_notification($orderid, $this->merchant->id, $user->phonenumber);

            $this->output->set_status_header(200);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => true,
                'message' => 'Transaksi berhasil',
                'transactionid' => $orderid,
            ));
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => $ex->getMessage(),
            ));
        }
    }
}
