<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Firebase</h1>
        <!--end::Title-->
        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Notifikasi</li>
            <!--end::Item-->
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Firebase</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->
</div>
<!--end::Toolbar-->

<!--begin::Content-->
<div class="content flex-column-fluid" id="kt_content">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">Format Notifikasi Transaksi</h3>
                    </div>
                </div>

                <div class="card-body">
                    <form id="frmTransactionNotification" action="<?= base_url(uri_string() . '/process/transaction') ?>" method="POST" autocomplete="off">
                        <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Notifikasi Transaksi Berhasil</label>
                                    <input type="text" name="transaction_success" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Transaksi ${PRODUCT} berhasil. Ref: ${REFERENCE}" value="<?= isset($firebase_notification->transaction_success) ? $firebase_notification->transaction_success : '' ?>">
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Notifikasi Transaksi Pending</label>
                                    <input type="text" name="transaction_pending" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Transaksi ${PRODUCT} sedang diproses. Ref: ${REFERENCE}" value="<?= isset($firebase_notification->transaction_pending) ? $firebase_notification->transaction_pending : '' ?>">
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Notifikasi Transaksi Gagal</label>
                                    <input type="text" name="transaction_failed" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Transaksi ${PRODUCT} gagal. Ref: ${REFERENCE}" value="<?= isset($firebase_notification->transaction_failed) ? $firebase_notification->transaction_failed : '' ?>">
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">Simpan Format Transaksi</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">Format Notifikasi Deposit</h3>
                    </div>
                </div>

                <div class="card-body">
                    <form id="frmDepositNotification" action="<?= base_url(uri_string() . '/process/deposit') ?>" method="POST" autocomplete="off">
                        <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Notifikasi Deposit Berhasil</label>
                                    <input type="text" name="deposit_success" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Deposit ${AMOUNT} berhasil. Saldo: ${BALANCE}" value="<?= isset($firebase_notification->deposit_success) ? $firebase_notification->deposit_success : '' ?>">
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Notifikasi Deposit Pending</label>
                                    <input type="text" name="deposit_pending" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Deposit ${AMOUNT} sedang diproses. Ref: ${REFERENCE}" value="<?= isset($firebase_notification->deposit_pending) ? $firebase_notification->deposit_pending : '' ?>">
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Notifikasi Deposit Kadaluarsa/Gagal</label>
                                    <input type="text" name="deposit_failed" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Deposit ${AMOUNT} gagal/kadaluarsa. Ref: ${REFERENCE}" value="<?= isset($firebase_notification->deposit_failed) ? $firebase_notification->deposit_failed : '' ?>">
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">Simpan Format Deposit</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">Parameter Pesan Transaksi</h3>
                    </div>
                </div>

                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-rounded table-striped border gy-7 gs-7">
                            <thead>
                                <tr class="fw-semibold fs-6 text-gray-800 border-bottom border-gray-200">
                                    <th>Parameter</th>
                                    <th>Penjelasan</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-bold">${CODE}</td>
                                    <td>Menampilkan kode transaksi</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${TARGET}</td>
                                    <td>Menampilkan target/tujuan layanan dikirim</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${PRICE}</td>
                                    <td>Menampilkan harga produk yang dipesan</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${BALANCE_BEFORE}</td>
                                    <td>Menampilkan jumlah saldo sebelum dipotong transaksi</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${BALANCE_AFTER}</td>
                                    <td>Menampilkan jumlah saldo setelah dipotong transaksi</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${STATUS}</td>
                                    <td>Menampilkan status transaksi</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${SN}</td>
                                    <td>Menampilkan SN (Khusus untuk pembelian kategori PPOB)</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${PRODUCT}</td>
                                    <td>Menampilkan nama produk yang dipesan</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${QTY}</td>
                                    <td>Menampilkan jumlah pengisian (Khusus untuk pembelian kategori SMM)</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">Parameter Pesan Deposit</h3>
                    </div>
                </div>

                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-rounded table-striped border gy-7 gs-7">
                            <thead>
                                <tr class="fw-semibold fs-6 text-gray-800 border-bottom border-gray-200">
                                    <th>Parameter</th>
                                    <th>Penjelasan</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-bold">${PAYMENT}</td>
                                    <td>Menampilkan metode pembayaran yang dipilih saat mengajukan deposit</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${AMOUNT}</td>
                                    <td>Menampilkan jumlah deposit yang diajukan</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${NOTE}</td>
                                    <td>Menampilkan catatan pembayaran</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${CODE}</td>
                                    <td>Menampilkan kode transaksi deposit</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${PAYMENTTYPE}</td>
                                    <td>Menampilkan tipe pembayaran yang dipilih saat mengajukan deposit</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${BONUS}</td>
                                    <td>Menampilkan nominal bonus yang didapatkan</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${FEE}</td>
                                    <td>Menampilkan biaya admin yang dikenakan</td>
                                </tr>

                                <tr>
                                    <td class="fw-bold">${STATUS}</td>
                                    <td>Menampilkan status deposit</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    window.addEventListener('load', function() {
        // Form Transaction Notification
        $.AjaxRequest('#frmTransactionNotification', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Berhasil',
                        text: response.MESSAGE,
                        icon: 'success'
                    }).then(function() {
                        return window.location.reload();
                    });
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            },
            error: function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error'
                });
            }
        });

        // Form Deposit Notification
        $.AjaxRequest('#frmDepositNotification', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Berhasil',
                        text: response.MESSAGE,
                        icon: 'success'
                    }).then(function() {
                        return window.location.reload();
                    });
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            },
            error: function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error'
                });
            }
        });
    });
</script>