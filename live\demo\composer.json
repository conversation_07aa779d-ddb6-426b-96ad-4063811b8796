{"description": "The CodeIgniter framework", "name": "codeigniter/framework", "type": "project", "homepage": "https://codeigniter.com", "license": "MIT", "support": {"forum": "http://forum.codeigniter.com/", "wiki": "https://github.com/bcit-ci/CodeIgniter/wiki", "slack": "https://codeigniterchat.slack.com", "source": "https://github.com/bcit-ci/CodeIgniter"}, "require": {"php": ">=5.3.7", "midtrans/midtrans-php": "^2.5", "phpoffice/phpspreadsheet": "^1.29", "ipaymu/ipaymu-php-api": "^3.0", "chillerlan/php-qrcode": "^4.3", "duitkupg/duitku-php": "dev-master", "dompdf/dompdf": "^2.0", "giggsey/libphonenumber-for-php": "^8.13", "kreait/firebase-php": "^6.9"}, "suggest": {"paragonie/random_compat": "Provides better randomness in PHP 5.x"}, "scripts": {"test:coverage": ["@putenv XDEBUG_MODE=coverage", "phpunit --color=always --coverage-text --configuration tests/travis/sqlite.phpunit.xml"]}, "require-dev": {"mikey179/vfsstream": "1.6.*", "phpunit/phpunit": "4.* || 5.* || 9.*"}, "config": {"platform": {"php": "7.4"}}}