<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property MsCustomerSupport $mscustomersupport
 * @property CI_Output $output
 */
class CustomerSupport extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsCustomerSupport', 'mscustomersupport');
    }

    public function index()
    {
        $customersupport = $this->mscustomersupport->select('id, name, link, icon')
            ->result(array(
                'createdby' => $this->merchant->id
            ));

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => true,
            'message' => 'Data berhasil diambil',
            'data' => $customersupport
        ));
    }
}
