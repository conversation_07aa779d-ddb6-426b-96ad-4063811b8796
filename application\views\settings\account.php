<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">Pengaturan Akun</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Akun</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">Pengaturan</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <?php if (isUser()) : ?>
            <div class="col-md-6">
                <div class="card mb-5">
                    <div class="card-header">
                        <div class="card-title m-0">
                            <h3 class="m-0 fw-bold">Profil Usaha</h3>
                        </div>
                    </div>

                    <form id="frmChangeCompany" action="<?= base_url(uri_string() . '/process/company') ?>" method="POST" autocomplete="off">
                        <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                        <div class="card-body">
                            <div class="mb-7">
                                <label class="col-form-label fw-semibold fs-6 pt-0">Nama Usaha</label>
                                <input type="text" name="name" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nama Usaha" value="<?= $user->companyname ?>" required>
                                <div class="form-text">Nama usaha akan ditampilkan pada halaman website anda.</div>
                            </div>

                            <div class="mb-7">
                                <label class="col-form-label fw-semibold fs-6 pt-0">Alamat Usaha</label>
                                <textarea name="address" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Alamat Usaha" required><?= $user->companyaddress ?></textarea>
                            </div>

                            <div class="mb-7">
                                <div>
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Logo Usaha</label>
                                </div>

                                <!--begin::Image input-->
                                <div class="image-input image-input-outline" data-kt-image-input="true" style="background-image: url('<?= base_url() ?>assets/media/svg/avatars/blank.svg')">
                                    <!--begin::Preview existing avatar-->
                                    <?php if ($user->companyicon == null) : ?>
                                        <div class="image-input-wrapper w-125px h-125px" style="background-image: url(<?= base_url() ?>assets/media/svg/avatars/blank.svg)"></div>
                                    <?php else : ?>
                                        <div class="image-input-wrapper w-125px h-125px" style="background-image: url(<?= $user->companyicon ?>)"></div>
                                    <?php endif; ?>
                                    <!--end::Preview existing avatar-->

                                    <!--begin::Label-->
                                    <label class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="change" data-bs-toggle="tooltip" title="Ubah Logo">
                                        <i class="bi bi-pencil-fill fs-7"></i>

                                        <!--begin::Inputs-->
                                        <input type="file" name="logo" accept=".ico, .png" />
                                        <input type="hidden" name="avatar_remove" />
                                        <!--end::Inputs-->
                                    </label>
                                    <!--end::Label-->

                                    <!--begin::Cancel-->
                                    <span class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow" data-kt-image-input-action="cancel" data-bs-toggle="tooltip" title="Cancel avatar">
                                        <i class="bi bi-x fs-2"></i>
                                    </span>
                                    <!--end::Cancel-->

                                    <!--begin::Remove-->
                                    <span class="btn btn-icon btn-circle btn-active-color-primary w-25px h-25px bg-body shadow <?= $user->companyicon == null ? 'd-none' : null ?>" data-kt-image-input-action="remove" data-bs-toggle="tooltip" title="Remove avatar">
                                        <i class="bi bi-x fs-2"></i>
                                    </span>
                                    <!--end::Remove-->
                                </div>
                                <!--end::Image input-->
                            </div>

                            <?php if (getCurrentUser()->companycategory == 'PPOB & SMM' || getCurrentUser()->licenseid == 1) : ?>
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Kategori Usaha</label>
                                    <select name="category" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" required>
                                        <option value="PPOB & SMM" <?= getCurrentUser()->licenseid == 1 && $user->companycategory == 'PPOB & SMM' ? 'selected' : null ?> <?= getCurrentUser()->licenseid != 1 ? 'disabled' : null ?>>PPOB & SMM</option>
                                        <option value="PPOB" <?= $user->companycategory == 'PPOB' ? 'selected' : null ?> <?= getCurrentUser()->licenseid == 3 ? 'disabled' : null ?>>PPOB</option>
                                        <option value="SMM" <?= $user->companycategory == 'SMM' ? 'selected' : null ?> <?= getCurrentUser()->licenseid == 2 ? 'disabled' : null ?>>SMM</option>
                                    </select>
                                </div>
                            <?php endif; ?>

                            <?php if (getCurrentUser()->licenseid != null) : ?>
                                <div class="mb-7">
                                    <label class="col-form-label fw-semibold fs-6 pt-0">Limit Saldo</label>
                                    <input type="number" name="balancelimit" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Limit Saldo" required value="<?= $user->balancelimit ?? 0 ?>">
                                    <div class="form-text">*Isi '0' jika anda tidak membatasi jumlah saldo pengguna website anda</div>
                                </div>

                                <div class="form-check form-check-custom form-check-solid mb-7">
                                    <input class="form-check-input" type="checkbox" value="1" id="maintenance" name="maintenance" <?= $user->ismaintenance ? 'checked' : null ?> />
                                    <label class="form-check-label" for="maintenance">
                                        Maintenance
                                    </label>
                                </div>

                                <div class="form-check form-check-custom form-check-solid">
                                    <input class="form-check-input" type="checkbox" value="1" id="pendaftarantanpaverifikasi" name="pendaftarantanpaverifikasi" <?= $user->isregisterwithoutverification ? 'checked' : null ?> />
                                    <label class="form-check-label" for="pendaftarantanpaverifikasi">
                                        Pendaftaran Tanpa Verifikasi
                                    </label>
                                </div>
                            <?php endif; ?>
                        </div>

                        <div class="card-footer d-flex justify-content-end py-6 px-9">
                            <button type="submit" class="btn btn-primary">Simpan</button>
                        </div>
                    </form>
                </div>
            </div>
        <?php endif; ?>

        <div class="col-md-6">
            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">Profil Personal</h3>
                    </div>
                </div>

                <form id="frmProfilePersonal" action="<?= base_url(uri_string() . '/process/personal') ?>" method="POST" autocomplete="off">
                    <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                    <div class="card-body">
                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Nama</label>
                            <input type="text" name="name" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nama" value="<?= $user->name ?>" required>
                        </div>

                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Alamat Email</label>
                            <input type="text" name="email" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Alamat Email" value="<?= getSessionValue('EMAIL') ?>" disabled>
                        </div>

                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Nomor Handphone</label>
                            <input type="number" name="phonenumber" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nomor Handphone" value="<?= $user->phonenumber ?>" required>
                        </div>
                    </div>

                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>

            <?php if (!isAdmin()) : ?>
                <div class="card mb-5">
                    <div class="card-header">
                        <div class="card-title m-0">
                            <h3 class="m-0 fw-bold">Pengaturan Nominal Unik</h3>
                        </div>
                    </div>

                    <form id="frmUniqueNominal" action="<?= base_url(uri_string() . '/process/uniquenominal') ?>" method="POST" autocomplete="off">
                        <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                        <div class="card-body">
                            <!--begin::Alert-->
                            <div class="alert alert-info d-flex align-items-center p-5 mb-10">
                                <!--begin::Svg Icon | path: icons/duotune/general/gen048.svg-->
                                <span class="svg-icon svg-icon-info svg-icon-2hx me-4"><svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path opacity="0.3" d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z" fill="currentColor" />
                                        <path d="M10.5606 11.3042L9.57283 10.3018C9.28174 10.0065 8.80522 10.0065 8.51412 10.3018C8.22897 10.5912 8.22897 11.0559 8.51412 11.3452L10.4182 13.2773C10.8099 13.6747 11.451 13.6747 11.8427 13.2773L15.4859 9.58051C15.771 9.29117 15.771 8.82648 15.4859 8.53714C15.1948 8.24176 14.7183 8.24176 14.4272 8.53714L11.7002 11.3042C11.3869 11.6221 10.874 11.6221 10.5606 11.3042Z" fill="currentColor" />
                                    </svg>
                                </span>
                                <!--end::Svg Icon-->

                                <div class="d-flex flex-column">
                                    <h4 class="mb-1 text-info">Pemberitahuan</h4>
                                    <span>Selalu gunakan rentang nilai yang jauh agar pengguna kamu tidak mengalami konflik nominal yang menyebabkan kebinggungan untuk melakukan konfirmasi deposit maupun transaksi.</span>
                                </div>
                            </div>
                            <!--end::Alert-->

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-7">
                                        <label class="col-form-label fw-semibold fs-6 pt-0">Nilai (Mulai)</label>
                                        <input type="number" name="startvalue" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nilai (Mulai)" value="<?= $currentuser->uniquenominal_start ?? 100 ?>" required>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-7">
                                        <label class="col-form-label fw-semibold fs-6 pt-0">Nilai (Sampai)</label>
                                        <input type="number" name="endvalue" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Nilai (Sampai)" value="<?= $currentuser->uniquenominal_end ?? 999 ?>" required>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card-footer d-flex justify-content-end py-6 px-9">
                            <button type="submit" class="btn btn-primary">Simpan</button>
                        </div>
                    </form>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
    window.onload = function() {
        $.AjaxRequest('#frmProfilePersonal', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Sukses',
                        text: response.MESSAGE,
                        icon: 'success'
                    }).then(function(result) {
                        return window.location.reload();

                    });
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            },
            error: function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error'
                });
            }
        });

        $.AjaxRequest('#frmChangeCompany', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Sukses',
                        text: response.MESSAGE,
                        icon: 'success'
                    }).then(function(result) {
                        return window.location.reload();

                    });
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            },
            error: function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error'
                });
            }
        });

        $.AjaxRequest('#frmUniqueNominal', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Berhasil',
                        text: response.MESSAGE,
                        icon: 'success'
                    }).then(function(result2) {
                        return window.location.reload();

                    });
                } else {
                    return Swal.fire({
                        icon: 'error',
                        title: 'Gagal',
                        text: response.MESSAGE
                    });
                }
            },
            error: function() {
                return Swal.fire({
                    icon: 'error',
                    title: 'Gagal',
                    text: 'Server sedang sibuk, silahkan coba beberapa saat lagi.'
                });
            }
        });
    };
</script>