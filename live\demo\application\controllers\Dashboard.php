<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property TrOrder $trorder
 * @property Deposits $deposits
 * @property MsNews $msnews
 * @property MsSlider $msslider
 * @property MsProduct $msproduct
 * @property DisabledCategory $disabledcategory
 * @property DisabledBrand $disabledbrand
 * @property MsRole $msrole
 * @property MsRoleDiscount $msrolediscount
 * @property MsRoleDiscountAdv $msrolediscountadv
 * @property MsUsers $msusers
 * @property MsPlatformSosmed $msplatformsosmed
 * @property MsDetailPlatform $msdetailplatform
 * @property HistoryBalance $historybalance
 * @property ApiKeys $apikeys
 * @property ApiKeys_WhatsApp $apikeys_whatsapp
 * @property BrandImage $brandimage
 * @property MsIcons $msicons
 * @property MsVendorDetail $msvendordetail
 * @property MsStockproduct $msstockproduct
 * @property MsStockproductDetail $msstockproductdetail
 */
class Dashboard extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('TrOrder', 'trorder');
        $this->load->model('Deposits', 'deposits');
        $this->load->model('MsNews', 'msnews');
        $this->load->model('MsSlider', 'msslider');
        $this->load->model('MsProduct', 'msproduct');
        $this->load->model('DisabledCategory', 'disabledcategory');
        $this->load->model('DisabledBrand', 'disabledbrand');
        $this->load->model('MsRole', 'msrole');
        $this->load->model('MsRoleDiscount', 'msrolediscount');
        $this->load->model('MsRoleDiscountAdv', 'msrolediscountadv');
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('MsPlatformSosmed', 'msplatformsosmed');
        $this->load->model('MsDetailPlatform', 'msdetailplatform');
        $this->load->model('ApiKeys', 'apikeys');
        $this->load->model('ApiKeys_WhatsApp', 'apikeys_whatsapp');
        $this->load->model('HistoryBalance', 'historybalance');
        $this->load->model('BrandImage', 'brandimage');
        $this->load->model('MsIcons', 'msicons');
        $this->load->model('MsVendor', 'msvendor');
        $this->load->model('MsVendorDetail', 'msvendordetail');
        $this->load->model('MsStockproduct', 'msstockproduct');
        $this->load->model('MsStockproductDetail', 'msstockproductdetail');
    }

    public function index()
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        $mark = getGet('mark');

        if ($mark != null) {
            setSessionValue(array(
                'POPUPREAD' => true
            ));
        }

        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $data = array();
        $data['title'] = $this->merchant->companyname . ' | Dashboard';
        $data['content'] = 'dashboard';
        $data['orderppob'] = $this->trorder->select('SUM(a.price) AS total')
            ->join('msusers c', 'c.id = a.userid')
            ->join('msproduct b', 'b.id = a.serviceid')
            ->get(array(
                'a.userid' => getCurrentIdUser(),
                "(LOWER(a.status) = 'success' OR LOWER(a.status) = 'sukses' OR LOWER(a.status) = 'completed') =" => true,
                'b.category_apikey' => 'PPOB'
            ))->row()->total;
        $data['ordersmm'] = $this->trorder->select('SUM(a.price) AS total')
            ->join('msusers c', 'c.id = a.userid')
            ->join('msproduct b', 'b.id = a.serviceid')
            ->get(array(
                'a.userid' => getCurrentIdUser(),
                "(LOWER(a.status) = 'success' OR LOWER(a.status) = 'sukses' OR LOWER(a.status) = 'completed') =" => true,
                'b.category_apikey' => 'SMM'
            ))->row()->total;
        $data['deposits'] = $this->deposits->select('(SUM(nominal) + COALESCE(SUM(nominalbonus), 0)) AS total')
            ->get(array(
                'userid' => getCurrentIdUser(),
                'status' => 'Success'
            ))->row()->total;
        $data['news'] = $this->msnews->select('a.*, b.name AS typename, b.badge')
            ->join('msnewstype b', 'b.id = a.typeid')
            ->order_by('a.createddate', 'DESC')
            ->limit(10)
            ->result(array(
                'a.userid' => $this->merchant->id
            ));
        $data['slider'] = $this->msslider->result(array(
            'a.userid' => $this->merchant->id
        ));

        $merchantid = $this->merchant->id;

        $last_transaction = $this->trorder->select('b.name, c.productname, a.price')
            ->join('msusers b', 'b.id = a.userid')
            ->join('msproduct c', 'c.id = a.serviceid')
            ->order_by('a.createddate', 'DESC')
            ->limit(10)
            ->result(array(
                "(b.merchantid = '$merchantid' OR a.merchantid_order = '$merchantid') =" => true,
            ));

        $marquee = "";
        foreach ($last_transaction as $key => $value) {
            $name = $value->name;
            $name = sensorKarakter($name, 5);

            $marquee .=  $name . " Telah melakukan transaksi " . $value->productname . " senilai Rp " . number_format($value->price, 0, ',', '.') . " | ";
        }

        $data['marquee'] = $marquee;

        $date = array();
        $graph = array();
        $graph_pasca = array();
        $graph_smm = array();

        // Optimized: Get all data in single queries instead of looping
        $current_year_month = getCurrentDate("Y-m");
        $days_in_month = date('t');

        // Initialize arrays with zeros for all days
        for ($i = 1; $i <= $days_in_month; $i++) {
            $date[] = $i;
            $graph[$i] = 0;
            $graph_pasca[$i] = 0;
            $graph_smm[$i] = 0;
        }

        // Get PRABAYAR data for the month
        $prabayar_data = $this->db->select('DAY(a.createddate) as day, COUNT(*) as total')
            ->from('trorder a')
            ->join('msusers c', 'c.id = a.userid')
            ->join('msproduct b', 'b.id = a.serviceid')
            ->where('a.userid', getCurrentIdUser())
            ->where('b.category_apikey', 'PPOB')
            ->where('b.subcategory_apikey', 'PRABAYAR')
            ->where_in('a.status', array('success', 'sukses', 'completed'))
            ->where("DATE_FORMAT(a.createddate, '%Y-%m') =", $current_year_month)
            ->group_by('DAY(a.createddate)')
            ->get()->result();

        // Fill PRABAYAR data
        foreach ($prabayar_data as $row) {
            $graph[$row->day] = (int)$row->total;
        }

        // Get PASCABAYAR data for the month
        $pascabayar_data = $this->db->select('DAY(a.createddate) as day, COUNT(*) as total')
            ->from('trorder a')
            ->join('msusers c', 'c.id = a.userid')
            ->join('msproduct b', 'b.id = a.serviceid')
            ->where('a.userid', getCurrentIdUser())
            ->where('b.category_apikey', 'PPOB')
            ->where('b.subcategory_apikey', 'PASCABAYAR')
            ->where_in('a.status', array('success', 'sukses', 'completed'))
            ->where("DATE_FORMAT(a.createddate, '%Y-%m') =", $current_year_month)
            ->group_by('DAY(a.createddate)')
            ->get()->result();

        // Fill PASCABAYAR data
        foreach ($pascabayar_data as $row) {
            $graph_pasca[$row->day] = (int)$row->total;
        }

        // Get SMM data for the month
        $smm_data = $this->db->select('DAY(a.createddate) as day, COUNT(*) as total')
            ->from('trorder a')
            ->join('msusers c', 'c.id = a.userid')
            ->join('msproduct b', 'b.id = a.serviceid')
            ->where('a.userid', getCurrentIdUser())
            ->where('b.category_apikey', 'SMM')
            ->where_in('a.status', array('success', 'sukses', 'completed'))
            ->where("DATE_FORMAT(a.createddate, '%Y-%m') =", $current_year_month)
            ->group_by('DAY(a.createddate)')
            ->get()->result();

        // Fill SMM data
        foreach ($smm_data as $row) {
            $graph_smm[$row->day] = (int)$row->total;
        }

        // Convert associative arrays back to indexed arrays for JSON encoding
        $graph = array_values($graph);
        $graph_pasca = array_values($graph_pasca);
        $graph_smm = array_values($graph_smm);

        $data['date'] = json_encode($date);
        $data['graph'] = json_encode($graph);
        $data['graph_pasca'] = json_encode($graph_pasca);
        $data['graph_smm'] = json_encode($graph_smm);

        $productcategory = array();
        if ($this->merchant->multivendor != 1) {
            $vendor = getCurrentVendor('SMM', $this->merchant->id);

            $product = $this->msproduct->select('category')
                ->group_by('category')
                ->result(array(
                    'userid' => $this->merchant->id,
                    'category_apikey' => 'SMM',
                    'subcategory_apikey' => 'SMM',
                    "(vendor = '$vendor' OR vendorid IS NULL) =" => true
                ));

            foreach ($product as $key => $value) {
                if (!in_array($value->category, $productcategory)) {
                    $productcategory[] = $value->category;
                }
            }
        } else {
            $vendor = $this->msvendor->result(array(
                'userid' => $this->merchant->id,
                'category' => 'SMM',
                'isactive' => 1
            ));

            foreach ($vendor as $key => $value) {
                $product = $this->msproduct->select('category')
                    ->group_by('category')
                    ->result(array(
                        'userid' => $this->merchant->id,
                        'category_apikey' => 'SMM',
                        'subcategory_apikey' => 'SMM',
                        'vendorid' => $value->id
                    ));

                foreach ($product as $key => $value) {
                    if (!in_array($value->category, $productcategory)) {
                        $productcategory[] = $value->category;
                    }
                }
            }
        }

        $disabled = $this->disabledcategory->result(array(
            'userid' => $this->merchant->id,
            'category_apikey' => 'PPOB',
        ));

        $disabled_category = array();
        foreach ($disabled as $key => $value) {
            $disabled_category[] = $value->categoryname;
        }

        $this->db->select('a.categoryname as category, b.asseturl, "PPOB" as vendor');
        $this->db->from('categoryimage a');
        $this->db->join('msicons b', 'b.id = a.assetid');

        if (count($disabled_category) > 0) {
            $this->db->where_not_in('a.categoryname', $disabled_category);
        }

        if (count($productcategory) > 0) {
            $this->db->where_in('a.categoryname', $productcategory);
        }

        $this->db->where(array(
            'a.userid' => $this->merchant->id
        ));

        $query1 = $this->db->get_compiled_select();

        $this->db->select('a.name as category, b.asseturl, "SMM" as vendor');
        $this->db->from('msplatformsosmed a');
        $this->db->join('msicons b', 'b.id = a.assetid');

        $this->db->where(array(
            'a.userid' => $this->merchant->id,
        ));

        $query2 = $this->db->get_compiled_select();

        if ($this->merchant->companycategory == 'PPOB & SMM') {
            $data['category'] = $this->db->query("($query1) UNION ($query2)")->result();
        } else if ($this->merchant->companycategory == 'PPOB') {
            $data['category'] = $this->db->query($query1)->result();
        } else if ($this->merchant->companycategory == 'SMM') {
            $data['category'] = $this->db->query($query2)->result();
        } else {
            $data['category'] = array();
        }

        return viewTemplate($this->merchant->id, "master", $data);
    }

    public function news()
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if ($this->merchant->licenseid == null) {
                throw new Exception('Anda belum memiliki lisensi');
            }

            if (getSessionValue('POPUPREAD')) {
                throw new Exception('Anda sudah membaca berita terbaru');
            }

            $content = viewTemplate($this->merchant->id, "dashboard/news", array(), true);

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $content
            ), JSON_INVALID_UTF8_IGNORE);
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    private function send_notification($orderid, $userid, $phonenumber)
    {
        // Kirim Firebase notification untuk order
        sendFirebaseNotificationOrder($orderid, $userid);

        $apikeys_whatsapp = $this->apikeys_whatsapp->get(array(
            'userid' => $userid,
        ));

        if ($apikeys_whatsapp->num_rows() == 0) {
            return false;
        }

        $row = $apikeys_whatsapp->row();

        $wablasingateway = new WABlasinGateway(stringEncryption('decrypt', $row->apikey), stringEncryption('decrypt', $row->devicecode));

        $messagenotification = replaceParameterNotification($orderid, $userid);
        if ($messagenotification != null && $phonenumber != null) {
            $phonenumber = changePrefixPhone($phonenumber);

            $send = $wablasingateway->sendMessage($phonenumber, $messagenotification);

            if (!isset($send->RESULT) || (isset($send->RESULT) && $send->RESULT != 'OK')) {
                log_message_user('error', '[WHATSAPP SEND MESSAGE] Send notification failed: ' . json_encode($send), $userid);
            }

            return true;
        }

        return false;
    }

    public function order_ppob()
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if ($this->merchant->licenseid == null) {
                throw new Exception('Anda belum memiliki lisensi');
            }

            $category = getPost('category');

            $cekcategory = $this->msproduct->select('a.category')
                ->where(array(
                    'a.userid' => $this->merchant->id,
                    'a.category' => $category
                ))
                ->total();

            if ($cekcategory == 0) {
                throw new Exception('Kategori tidak ditemukan');
            }

            $content = viewTemplate($this->merchant->id, "dashboard/ppob_order", array('category' => $category), true);

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $content,
            ), JSON_INVALID_UTF8_IGNORE);
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function fields_ppob()
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            $product = getPost('product');

            $disabled = $this->disabledcategory->result(array(
                'userid' => $this->merchant->id,
                'category_apikey' => 'PPOB'
            ));

            $disabled_category = array();
            foreach ($disabled as $key => $value) {
                $disabled_category[] = $value->categoryname;
            }

            $disabledbrand = $this->disabledbrand->result(array(
                'userid' => $this->merchant->id,
            ));

            $disabled_brand = array();
            foreach ($disabledbrand as $key => $value) {
                $disabled_brand[] = $value->brandname;
            }

            $where = array(
                'id' => $product,
                'userid' => $this->merchant->id,
                'subcategory_apikey' => 'PRABAYAR',
                'status' => 1
            );

            if ($this->merchant->multivendor != 1) {
                $vendor = getCurrentVendor('PPOB', $this->merchant->id);
                $where["(vendor = '$vendor' OR vendor IS NULL) ="] = true;
                $where['vendorid'] = null;
            } else {
                $where["((vendorid IS NOT NULL AND vendorenabled = 1) OR vendor IS NULL) ="] = true;
            }

            $get = $this->msproduct->where_not_in('category', $disabled_category)
                ->where_not_in('brand', $disabled_brand)
                ->get($where);

            if ($get->num_rows() == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            $row = $get->row();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => viewTemplate($this->merchant->id, "dashboard/ppob_fields", array(
                    'product' => $row
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_order_ppob()
    {
        try {
            $this->db->trans_begin();

            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                $this->db->trans_rollback();

                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            }

            $product = getPost('product');
            $target = getPost('target');
            $zoneid = getPost('zoneid');
            $pin = getPost('pin');

            if ($product == null) {
                throw new Exception('Produk wajib diisi');
            } else if (!is_numeric($product)) {
                throw new Exception('Produk tidak valid');
            } else if ($target == null) {
                throw new Exception('Target wajib diisi');
            } else if ($pin == null) {
                throw new Exception('PIN Transaksi wajib diisi');
            }

            $currentuser = getCurrentUser(null, true);

            if (stringEncryption('encrypt', $pin) != $currentuser->pin) {
                throw new Exception('PIN Transaksi yang anda masukkan salah');
            }

            $disabled = $this->disabledcategory->result(array(
                'userid' => $this->merchant->id,
                'category_apikey' => 'PPOB'
            ));

            $disabled_category = array();
            foreach ($disabled as $key => $value) {
                $disabled_category[] = $value->categoryname;
            }

            $disabledbrand = $this->disabledbrand->result(array(
                'userid' => $this->merchant->id,
            ));

            $disabled_brand = array();
            foreach ($disabledbrand as $key => $value) {
                $disabled_brand[] = $value->brandname;
            }

            $where = array(
                'id' => $product,
                'userid' => $this->merchant->id,
                'subcategory_apikey' => 'PRABAYAR'
            );

            if ($this->merchant->multivendor != 1) {
                $vendor = getCurrentVendor('PPOB', $this->merchant->id);
                $where["(vendor = '$vendor' OR vendor IS NULL) ="] = true;
                $where['vendorid'] = null;
            } else {
                $where["((vendorid IS NOT NULL AND vendorenabled = 1) OR vendor IS NULL) ="] = true;
            }

            $getproduct = $this->msproduct->where_not_in('category', $disabled_category)
                ->where_not_in('brand', $disabled_brand)
                ->get($where);

            if ($currentuser->roleid == null) {
                $getrole = $this->msrole->get(array(
                    'createdby' => $this->merchant->id,
                    'isdefault' => '1'
                ))->row();
            } else {
                $getrole = $this->msrole->get(array(
                    'createdby' => $this->merchant->id,
                    'id' => $currentuser->roleid
                ))->row();
            }

            if ($getrole != null) {
                if ($getrole->discounttype == 'Simple') {
                    $getrolediscount = $this->msrolediscount->get(array(
                        'userid' => $this->merchant->id,
                        'roleid' => $getrole->id
                    ))->row();

                    $discount = $getrolediscount->trxdiscount ?? 0;
                } else {
                    $discount = $this->msrolediscountadv->get(array(
                        'createdby' => $this->merchant->id,
                        'roleid' => $getrole->id,
                        'servicetype' => 'Prabayar'
                    ))->result();
                }
            } else {
                $getrole = new stdClass();
                $getrole->discounttype = 'Simple';
                $discount = 0;
            }

            $productRow = $getproduct->row();

            $fixproductprice = $productRow->price;

            if ($getrole->discounttype == 'Simple') {
                $fixproductprice = $productRow->price - $discount;
            } else {
                $found = false;

                foreach ($discount as $val) {
                    if ($found) continue;

                    if ($val->startrange <= $productRow->price && $val->endrange >= $productRow->price && strtoupper($val->servicetype) == 'PRABAYAR') {
                        if ($val->discounttype == 'Persentase') {
                            $fixproductprice = $productRow->price - ($productRow->price * $val->nominal / 100);

                            $found = true;
                        } else {
                            $fixproductprice = $productRow->price - $val->nominal;

                            $found = true;
                        }
                    }
                }
            }

            if ($getproduct->num_rows() == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            if ($productRow->status != 1) {
                throw new Exception('Produk sedang gangguan');
            }

            if ($productRow->isstock == 1) {
                $stock = $productRow->stock;

                if ($stock == 0) {
                    throw new Exception('Produk sedang kosong');
                }
            }

            if ($productRow->isdatabase == 1 && $productRow->databasecategory == 'Stock Product') {
                $databaseid = $productRow->databaseid;

                $stockproduct = $this->msstockproduct->get(array(
                    'id' => $databaseid
                ))->row();

                if ($stockproduct == null) {
                    throw new Exception('Produk tidak tersedia');
                } else {
                    $available_stock = $this->msstockproductdetail->total(array(
                        'stockproductid' => $databaseid,
                        'status' => 'Tersedia'
                    ));

                    if ($available_stock == 0) {
                        throw new Exception('Produk sedang kosong');
                    }
                }
            }

            if ($currentuser->balance < $fixproductprice) {
                throw new Exception('Saldo anda tidak mencukupi');
            }

            $getpending = $this->trorder->total(array(
                'userid' => getCurrentIdUser(),
                'serviceid' => $productRow->id,
                'target' => $target,
                'status' => 'pending'
            ));

            if ($getpending > 0) {
                throw new Exception('Transaksi sedang diproses');
            }

            $queuetransaction = false;
            if ($productRow->vendor != null) {
                if (preg_match('/(mobile legends)|(mobilelegends)|(mobile legend)|(mobilelegend)/', strtolower($productRow->brand)) && $productRow->vendor == 'VIPayment') {
                    if ($zoneid == null) {
                        throw new Exception('Zone ID wajib diisi');
                    }
                }

                if ($this->merchant->multivendor != 1) {
                    $apikeys = getCurrentAPIKeys('PPOB', $this->merchant->id);
                    if ($apikeys->balance < $productRow->vendorprice) {
                        $queuetransaction = true;
                    }
                } else {
                    if (getCurrentBalanceVendor($this->merchant->id, $productRow->vendorid) < $productRow->vendorprice) {
                        $queuetransaction = true;
                    }
                }
            }

            $originprice = $productRow->price;

            $potonganprofit = $originprice - $fixproductprice;

            if ($productRow->isstock == 1) {
                $this->msproduct->update(array(
                    'id' => $productRow->id
                ), array(
                    'stock' => $stock - 1
                ));
            }

            $insert = array();
            $insert['userid'] = getCurrentIdUser();
            $insert['clientcode'] = generateTransactionNumber('PRABAYAR');
            $insert['serviceid'] = $productRow->id;
            $insert['target'] = $target;
            $insert['price'] = $fixproductprice;
            $insert['profit'] = ($productRow->profit - $potonganprofit);
            $insert['currentsaldo'] = $currentuser->balance;
            $insert['status'] = 'pending';
            $insert['type'] = 'PPOB';
            $insert['queuetransaction'] = $queuetransaction ? 1 : 0;
            $insert['category_apikey'] = $productRow->category_apikey;
            $insert['subcategory_apikey'] = $productRow->subcategory_apikey;
            $insert['orderplatform'] = 'web';
            $insert['productcode'] = $productRow->code;
            $insert['vendor'] = $productRow->vendor;
            $insert['productname_order'] = $productRow->productname;
            $insert['merchantid_order'] = $this->merchant->id;
            $insert['status_payment'] = 'sukses';
            $insert['vendorid'] = $productRow->vendorid;

            if ($productRow->vendor != null) {
                if (preg_match('/(mobile legends)|(mobilelegends)|(mobile legend)|(mobilelegend)/', strtolower($productRow->brand)) && $productRow->vendor == 'VIPayment') {
                    $insert['zoneid'] = $zoneid;
                }
            }

            $this->trorder->insert($insert);
            $orderid = $this->db->insert_id();

            $inserthistorybalance = array();
            $inserthistorybalance['userid'] = getCurrentIdUser();
            $inserthistorybalance['type'] = 'OUT';
            $inserthistorybalance['nominal'] = $fixproductprice;
            $inserthistorybalance['currentbalance'] = $currentuser->balance;
            $inserthistorybalance['orderid'] = $orderid;
            $inserthistorybalance['createdby'] = getCurrentIdUser();
            $inserthistorybalance['createddate'] = getCurrentDate();

            $this->historybalance->insert($inserthistorybalance);

            $update = array();
            $update['balance'] = $currentuser->balance - $fixproductprice;

            $this->msusers->update(array(
                'id' => getCurrentIdUser()
            ), $update);

            if ($this->db->trans_status() == FALSE) {
                throw new Exception('Transaksi gagal');
            }

            pullTransaction($this->merchant->id);

            if ($queuetransaction == false && $productRow->vendor != null && ENVIRONMENT == 'production') {
                if ($this->merchant->multivendor != 1) {
                    if ($vendor == 'Digiflazz') {
                        $digiflazz = new Digiflazz(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));

                        $servercode = generateTransactionNumber('ORDER');
                        $topup = $digiflazz->topup($productRow->code, $target, $servercode);

                        $result = json_decode($topup);

                        if (isset($result->data->status)) {
                            if ($result->data->status != 'Gagal') {
                                $update = array();
                                $update['servercode'] = $servercode;
                                $update['note'] = $result->data->message;
                                $update['sn'] = $result->data->sn;
                                $update['status'] = strtolower($result->data->status);
                                $update['jsonresponse'] = json_encode($result);

                                $this->trorder->update(array(
                                    'id' => $orderid
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($orderid, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Transaksi berhasil');
                            } else {
                                if ($result != null) {
                                    log_message_user('error', '[DIGIFLAZZ PRABAYAR] Response: ' . json_encode($result), $this->merchant->id);
                                }

                                $responsecode = getResponseCodeDigiflazz($result->data->rc);
                                throw new Exception($responsecode);
                            }
                        } else {
                            throw new Exception('Transaksi gagal');
                        }
                    } else if ($vendor == 'VIPayment') {
                        $vipayment = new VIPayment(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));

                        if ($productRow->vendor == 'VIPayment' && $productRow->type == 'game') {
                            if (preg_match('/(mobile legends)|(mobilelegends)|(mobile legend)|(mobilelegend)/', strtolower($productRow->brand))) {
                                $order = $vipayment->order_game($productRow->code, $target, $zoneid);
                            } else {
                                $order = $vipayment->order_game($productRow->code, $target, null);
                            }
                        } else {
                            $order = $vipayment->order_prepaid($productRow->code, $target);
                        }

                        if ($order->result) {
                            $update = array();
                            $update['servercode'] = $order->data->trxid;
                            $update['jsonresponse'] = json_encode($order);

                            $this->trorder->update(array(
                                'id' => $orderid
                            ), $update);

                            $this->db->trans_commit();

                            $this->send_notification($orderid, $this->merchant->id, $currentuser->phonenumber);

                            return JSONResponseDefault('OK', $order->message);
                        } else {
                            if ($order != null) {
                                log_message_user('error', '[VIPAYMENT PRABAYAR] Response: ' . json_encode($order), $this->merchant->id);
                            }

                            throw new Exception($order->message);
                        }
                    }
                } else {
                    $order = $this->msvendordetail->select('a.*, b.default_config')
                        ->join('msvendor b', 'b.id = a.vendorid')
                        ->get(array(
                            'a.vendorid' => $productRow->vendorid,
                            'a.apitype' => 'Order'
                        ));

                    if ($order->num_rows() == 0) {
                        throw new Exception('Konfigurasi vendor tidak ditemukan');
                    }

                    $vendor_order = $order->row();

                    if ($vendor_order->default_config == null) {
                        throw new Exception('Konfigurasi vendor tidak ditemukan');
                    }

                    $response_indicator = json_decode($vendor_order->response_indicator);
                    $response_setting = json_decode($vendor_order->response_setting);

                    $dynamicvendor = new DynamicVendor($productRow->vendorid, json_decode($vendor_order->default_config, true));
                    $order = $dynamicvendor->order($orderid);

                    if (
                        ($response_indicator->index == null && isset($order[$response_indicator->key]) && (
                            $response_indicator->datatype == 'string' && $order[$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'number' && $order[$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'boolean' && $order[$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'exists' && isset($order[$response_indicator->key])
                        ))
                        || ($response_indicator->index != null && isset($order[$response_indicator->index][$response_indicator->key]) && (
                            $response_indicator->datatype == 'string' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'number' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'boolean' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'exists' && isset($order[$response_indicator->index][$response_indicator->key])
                        ))
                    ) {
                        $var_referenceid = $response_setting->referenceid ?? null;
                        $var_price = $response_setting->price ?? null;
                        $var_status = $response_setting->status ?? null;
                        $var_note = $response_setting->note ?? null;
                        $var_sn = $response_setting->sn ?? null;
                        $var_errorrefund = $response_setting->errorrefund;

                        $exploding_errorefund = explode('[#]', strtolower($var_errorrefund ?? ''));

                        if ($response_setting->index != null) {
                            $order = $order[$response_setting->index] ?? null;
                        }

                        $referenceid = $var_referenceid != null ? ($order[$var_referenceid] ?? null) : null;
                        $price = $var_price != null ? ($order[$var_price] ?? null) : null;
                        $status = $var_status != null ? ($order[$var_status] ?? null) : null;
                        $note = $var_note != null ? ($order[$var_note] ?? null) : null;
                        $sn = $var_sn != null ? ($order[$var_sn] ?? null) : null;

                        if ($status != null) {
                            if (in_array($status, $exploding_errorefund)) {
                                log_message_user('error', '[MULTIVENDOR ORDER] Failed to parsing error refund, response: ' . json_encode($order), $this->merchant->id);

                                throw new Exception('Gagal melakukan transaksi');
                            }
                        } else {
                            if ($var_status == null) {
                                $status = 'pending';
                            } else if ($var_status != null && $status == null) {
                                log_message_user('error', '[MULTIVENDOR ORDER] Failed to parsing status, response: ' . json_encode($order), $this->merchant->id);

                                throw new Exception('Gagal melakukan transaksi');
                            }
                        }

                        $update = array();
                        $update['jsonresponse'] = json_encode($order);

                        if ($referenceid != null) {
                            $update['servercode'] = $referenceid;
                        }

                        if ($price != null) {
                            $update['price'] = $price;
                        }

                        if ($status != null) {
                            $update['status'] = $status;
                        }

                        if ($note != null) {
                            $update['note'] = $note;
                        }

                        if ($sn != null) {
                            $update['sn'] = $sn;
                        }

                        $this->trorder->update(array(
                            'id' => $orderid
                        ), $update);

                        $this->db->trans_commit();

                        $this->send_notification($orderid, $this->merchant->id, $currentuser->phonenumber);

                        return JSONResponseDefault('OK', 'Transaksi berhasil');
                    } else {
                        log_message_user('error', '[MULTIVENDOR ORDER] Failed to parse response, response: ' . json_encode($order), $this->merchant->id);

                        throw new Exception('Gagal melakukan transaksi');
                    }
                }
            }

            if ($productRow->isdatabase == 1 && $productRow->databasecategory == 'Stock Product') {
                $stockproductdetail = $this->msstockproductdetail->get(array(
                    'stockproductid' => $databaseid,
                    'status' => 'Tersedia'
                ))->row();

                $updatestockproductdetail = array();
                $updatestockproductdetail['status'] = 'Tidak Tersedia';
                $updatestockproductdetail['orderid'] = $orderid;

                $this->msstockproductdetail->update(array(
                    'id' => $stockproductdetail->id
                ), $updatestockproductdetail);

                $update = array();
                $update['status'] = 'success';
                $update['sn'] = stringEncryption('decrypt', $stockproductdetail->data);

                $this->trorder->update(array(
                    'id' => $orderid
                ), $update);
            }

            $this->db->trans_commit();

            $this->send_notification($orderid, $this->merchant->id, $currentuser->phonenumber);

            return JSONResponseDefault('OK', 'Transaksi berhasil');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function order_smm()
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if ($this->merchant->licenseid == null) {
                throw new Exception('Anda belum memiliki lisensi');
            }

            $category = getPost('category');

            $cekcategory = $this->msplatformsosmed->select('a.name')
                ->where(array(
                    'a.userid' => $this->merchant->id,
                    'a.name' => $category
                ))
                ->get();

            if ($cekcategory->num_rows() == 0) {
                throw new Exception('Kategori tidak ditemukan');
            }

            $row = $cekcategory->row();

            $productcategory = array();
            if ($this->merchant->multivendor != 1) {
                $vendor = getCurrentVendor('SMM', $this->merchant->id);

                $product = $this->msproduct->select('category')
                    ->group_by('category')
                    ->result(array(
                        'userid' => $this->merchant->id,
                        'category_apikey' => 'SMM',
                        'subcategory_apikey' => 'SMM',
                        "(vendor = '$vendor' OR vendorid IS NULL) =" => true
                    ));

                foreach ($product as $key => $value) {
                    if (!in_array($value->category, $productcategory)) {
                        $productcategory[] = $value->category;
                    }
                }
            } else {
                $vendor = $this->msvendor->result(array(
                    'userid' => $this->merchant->id,
                    'category' => 'SMM',
                    'isactive' => 1
                ));

                foreach ($vendor as $key => $value) {
                    $product = $this->msproduct->select('category')
                        ->group_by('category')
                        ->result(array(
                            'userid' => $this->merchant->id,
                            'category_apikey' => 'SMM',
                            'subcategory_apikey' => 'SMM',
                            'vendorid' => $value->id
                        ));

                    foreach ($product as $key => $value) {
                        if (!in_array($value->category, $productcategory)) {
                            $productcategory[] = $value->category;
                        }
                    }
                }
            }

            $disabled = $this->disabledcategory->result(array(
                'userid' => $this->merchant->id,
                'category_apikey' => 'SMM'
            ));

            $disabled_category = array();
            foreach ($disabled as $key => $value) {
                $disabled_category[] = $value->categoryname;
            }

            $detailplatform = $this->msdetailplatform->select('a.category')
                ->where_not_in('a.category', $disabled_category)
                ->where_in('a.category', $productcategory)
                ->result(array(
                    'a.platformid' => $row->id,
                ));

            $content = viewTemplate($this->merchant->id, "dashboard/smm_order", array(
                'category' => $row->name,
                'subcategory' => $detailplatform
            ), true);

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $content,
            ), JSON_INVALID_UTF8_IGNORE);
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_order_smm()
    {
        try {
            $this->db->trans_begin();

            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                $this->db->trans_rollback();

                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            }

            $product = getPost('product');
            $additional = getPost('additional');
            $target = getPost('target');
            $qty = getPost('qty');
            $pin = getPost('pin');

            if ($product == null) {
                throw new Exception('Produk wajib diisi');
            } else if ($target == null) {
                throw new Exception('Target wajib diisi');
            } else if ($qty == null) {
                throw new Exception('Jumlah wajib diisi');
            } else if (!is_numeric($qty)) {
                throw new Exception('Jumlah harus berupa angka');
            } else if ($pin == null) {
                throw new Exception('PIN Transaksi wajib diisi');
            }

            $currentuser = getCurrentUser(null, true);

            if (stringEncryption('encrypt', $pin) != $currentuser->pin) {
                throw new Exception('PIN Transaksi yang anda masukkan salah');
            }

            $disabled = $this->disabledcategory->result(array(
                'userid' => $this->merchant->id,
                'category_apikey' => 'SMM'
            ));

            $disabled_category = array();
            foreach ($disabled as $key => $value) {
                $disabled_category[] = $value->categoryname;
            }

            $where = array(
                'id' => $product,
                'userid' => $this->merchant->id,
                'subcategory_apikey' => 'SMM',
                'category !=' => null
            );

            if ($this->merchant->multivendor != 1) {
                $vendor = getCurrentVendor('SMM', $this->merchant->id);
                $where["(vendor = '$vendor' OR vendor IS NULL) ="] = true;
                $where['vendorid'] = null;
            } else {
                $where["((vendorid IS NOT NULL AND vendorenabled = 1) OR vendor IS NULL) ="] = true;
            }

            $getproduct = $this->msproduct->where_not_in('category', $disabled_category)
                ->get($where);

            if ($getproduct->num_rows() == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            $productRow = $getproduct->row();

            if ($productRow->status != 1) {
                throw new Exception('Produk sedang gangguan');
            }

            if ($productRow->minorder > $qty) {
                throw new Exception('Jumlah minimal order adalah ' . IDR($productRow->minorder));
            }

            if ($productRow->maxorder < $qty) {
                throw new Exception('Jumlah maksimal order adalah ' . IDR($productRow->maxorder));
            }

            if ($currentuser->roleid == null) {
                $getrole = $this->msrole->get(array(
                    'createdby' => $this->merchant->id,
                    'isdefault' => '1'
                ))->row();
            } else {
                $getrole = $this->msrole->get(array(
                    'createdby' => $this->merchant->id,
                    'id' => $currentuser->roleid
                ))->row();
            }

            if ($getrole != null) {
                if ($getrole->discounttype == 'Simple') {
                    $getrolediscount = $this->msrolediscount->get(array(
                        'userid' => $this->merchant->id,
                        'roleid' => $getrole->id
                    ))->row();

                    $discount = $getrolediscount->trxdiscount ?? 0;
                } else {
                    $discount = $this->msrolediscountadv->get(array(
                        'createdby' => $this->merchant->id,
                        'roleid' => $getrole->id,
                        'servicetype' => 'SMM'
                    ))->result();
                }
            } else {
                $getrole = new stdClass();
                $getrole->discounttype = 'Simple';
                $discount = 0;
            }

            $fixproductprice = 0;

            if ($getrole->discounttype == 'Simple') {
                $fixproductprice = $productRow->price - $discount;
            } else {
                $found = false;

                foreach ($discount as $val) {
                    if ($found) continue;

                    if ($val->startrange <= $productRow->price && $val->endrange >= $productRow->price) {
                        if ($val->discounttype == 'Persentase') {
                            $fixproductprice = $productRow->price - ($productRow->price * $val->nominal / 100);

                            $found = true;
                        } else {
                            $fixproductprice = $productRow->price - $val->nominal;

                            $found = true;
                        }
                    }
                }

                if ($found == false) {
                    $fixproductprice = $productRow->price;
                }
            }

            $totalharga = ($fixproductprice / 1000) * $qty;
            $totalhargaorigin = ($productRow->price / 1000) * $qty;

            $potonganprofit = $totalhargaorigin - $totalharga;

            $totalhargabyvendor = ($productRow->vendorprice / 1000) * $qty;
            $profit = (($productRow->profit / 1000) * $qty) - $potonganprofit;

            if ($currentuser->balance < $totalharga) {
                throw new Exception('Saldo anda tidak mencukupi');
            }

            $getpending = $this->trorder->total(array(
                'userid' => getCurrentIdUser(),
                'serviceid' => $productRow->id,
                'status' => 'pending',
                'target' => $target,
            ));

            if ($getpending > 0) {
                throw new Exception('Anda memiliki transaksi yang masih dalam proses');
            }

            $queuetransaction = false;

            if ($this->merchant->multivendor != 1) {
                $apikeys = getCurrentAPIKeys('SMM', $this->merchant->id);
                if ($apikeys->balance < $totalhargabyvendor) {
                    $queuetransaction = true;
                }
            } else {
                if (getCurrentBalanceVendor($this->merchant->id, $productRow->vendorid) < $totalhargabyvendor) {
                    $queuetransaction = true;
                }
            }

            $insert = array();
            $insert['userid'] = getCurrentIdUser();
            $insert['clientcode'] = generateTransactionNumber('SMM');
            $insert['serviceid'] = $productRow->id;
            $insert['target'] = $target;
            $insert['qty'] = $qty;
            $insert['price'] = $totalharga;
            $insert['profit'] = $profit;
            $insert['currentsaldo'] = $currentuser->balance;
            $insert['status'] = 'pending';
            $insert['type'] = 'SMM';
            $insert['queuetransaction'] = $queuetransaction ? 1 : 0;
            $insert['category_apikey'] = $productRow->category_apikey;
            $insert['subcategory_apikey'] = $productRow->subcategory_apikey;
            $insert['orderplatform'] = 'web';
            $insert['productcode'] = $productRow->code;
            $insert['vendor'] = $productRow->vendor;
            $insert['productname_order'] = $productRow->productname;
            $insert['merchantid_order'] = $this->merchant->id;
            $insert['status_payment'] = 'sukses';
            $insert['vendorid'] = $productRow->vendorid;

            if ($productRow->iscustom == 1) {
                $insert['additional'] = $additional;
            }

            $this->trorder->insert($insert);
            $insert_id = $this->db->insert_id();

            $inserthistorybalance = array();
            $inserthistorybalance['userid'] = getCurrentIdUser();
            $inserthistorybalance['type'] = 'OUT';
            $inserthistorybalance['nominal'] = $totalharga;
            $inserthistorybalance['currentbalance'] = $currentuser->balance;
            $inserthistorybalance['orderid'] = $insert_id;
            $inserthistorybalance['createdby'] = getCurrentIdUser();
            $inserthistorybalance['createddate'] = getCurrentDate();

            $this->historybalance->insert($inserthistorybalance);

            $update = array();
            $update['balance'] = $currentuser->balance - $totalharga;

            $this->msusers->update(array(
                'id' => getCurrentIdUser()
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Transaksi gagal');
            }

            pullTransaction($this->merchant->id);

            if ($productRow->code != null) {
                if ($queuetransaction == false && ENVIRONMENT == 'production') {
                    if ($this->merchant->multivendor != 1) {
                        if ($vendor == 'BuzzerPanel') {
                            $buzzerpanel = new BuzzerPanel(stringEncryption('decrypt', $apikeys->apikey), stringEncryption('decrypt', $apikeys->secretkey));
                            $order = $buzzerpanel->order($productRow->code, $target, $qty);

                            if (isset($order->status) && $order->status) {
                                $update = array();
                                $update['servercode'] = $order->data->id;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Pembelian berhasil');
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[BUZZERPANEL ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception($order->data->msg);
                            }
                        } else if ($vendor == 'MedanPedia') {
                            $medanpedia = new MedanPedia(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));
                            $order = $medanpedia->order($productRow->code, $target, $qty, $productRow->iscustom == 1 ? $additional : '');

                            if (isset($order->status) && $order->status) {
                                $update = array();
                                $update['servercode'] = $order->data->id;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Pembelian berhasil');
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[MEDANPEDIA ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                if (isset($order->data)) {
                                    throw new Exception($order->data);
                                } else {
                                    throw new Exception('Gagal melakukan pembelian');
                                }
                            }
                        } else if ($vendor == 'IrvanKede') {
                            if ($productRow->iscustom == null) {
                                $additional = '';
                            }

                            $irvankede = new IrvanKede(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));
                            $order = $irvankede->order(array(
                                'service' => $productRow->code,
                                'target' => $target,
                                'quantity' => $qty,
                                'custom_comments' => $additional,
                                'custom_link' => $additional
                            ));

                            if ($order->status) {
                                $update = array();
                                $update['servercode'] = $order->data->id;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Pembelian berhasil');
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[IRVANKEDE ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception($order->data);
                            }
                        } else if ($vendor == 'DailyPanel') {
                            $dailypanel = new DailyPanel(stringEncryption('decrypt', $apikeys->apikey), stringEncryption('decrypt', $apikeys->secretkey));
                            $order = $dailypanel->order($productRow->code, $target, $qty, $productRow->iscustom == 1 ? $additional : '');

                            if (isset($order->success) && $order->success) {
                                $update = array();
                                $update['servercode'] = $order->msg->order_id;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Pembelian berhasil');
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[DAILYPANEL ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception($order->msg->error);
                            }
                        } else if ($vendor == 'WStore') {
                            $wstore = new WStore(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey), stringEncryption('decrypt', $apikeys->secretkey));
                            $order = $wstore->order($productRow->code, $target, $qty);

                            if (isset($order->response) && $order->response) {
                                $update = array();
                                $update['servercode'] = $order->data->id;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Pembelian berhasil');
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[WSTORE ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception($order->data->msg);
                            }
                        } else if ($vendor == 'UNDRCTRL') {
                            $undrctrl = new UNDRCTRL(stringEncryption('decrypt', $apikeys->apikey));
                            $order = $undrctrl->order($productRow->code, $target, $qty);

                            if (isset($order->order)) {
                                $update = array();
                                $update['servercode'] = $order->order;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Pembelian berhasil');
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[UNDRCTRL ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception('Gagal melakukan pembelian');
                            }
                        } else if ($vendor == 'SosmedOnline') {
                            $sosmedonline = new SosmedOnline(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));
                            $order = $sosmedonline->order($productRow->code, $target, $qty, $productRow->iscustom == 1 ? $additional : '');

                            if (isset($order->status) && $order->status) {
                                $update = array();
                                $update['servercode'] = $order->data->id;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Pembelian berhasil');
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[SOSMEDONLINE ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception('Gagal melakukan pembelian');
                            }
                        } else if ($vendor == 'SosmedOnlineVIP') {
                            $sosmedonlinevip = new SosmedOnlineVIP(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));
                            $order = $sosmedonlinevip->order($productRow->code, $target, $qty, $productRow->iscustom == 1 ? $additional : '');

                            if (isset($order->status) && $order->status) {
                                $update = array();
                                $update['servercode'] = $order->data->id;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Pembelian berhasil');
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[SOSMEDONLINEVIP ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception('Gagal melakukan pembelian');
                            }
                        } else if ($vendor == 'DjuraganSosmed') {
                            $djuragansosmed = new DjuraganSosmed(stringEncryption('decrypt', $apikeys->apikey));
                            $order = $djuragansosmed->order($productRow->code, $target, $qty);

                            if (isset($order->order)) {
                                $update = array();
                                $update['servercode'] = $order->order;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Pembelian berhasil');
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[DJURAGANSOSMED ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception('Gagal melakukan pembelian');
                            }
                        } else if ($vendor == 'SMMRaja') {
                            $smmraja = new SMMRaja(stringEncryption('decrypt', $apikeys->apikey));
                            $order = $smmraja->order($productRow->code, $target, $qty);

                            if (isset($order->order)) {
                                $update = array();
                                $update['servercode'] = $order->order;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Pembelian berhasil');
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[SMMRAJA ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception('Gagal melakukan pembelian');
                            }
                        } else if ($vendor == 'SMMIllusion') {
                            $smmillusion = new SMMIllusion(stringEncryption('decrypt', $apikeys->apikey));
                            $order = $smmillusion->order($productRow->code, $target, $qty);

                            if (isset($order->order)) {
                                $update = array();
                                $update['servercode'] = $order->order;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Pembelian berhasil');
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[SMMILLUSION ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception('Gagal melakukan pembelian');
                            }
                        } else if ($vendor == 'V1Pedia') {
                            $v1pedia = new V1Pedia(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey), stringEncryption('decrypt', $apikeys->secretkey));
                            $order = $v1pedia->order($productRow->code, $target, $qty);

                            if (isset($order->response) && $order->response) {
                                $update = array();
                                $update['servercode'] = $order->data->id;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Pembelian berhasil');
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[V1PEDIA ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception('Gagal melakukan pembelian');
                            }
                        }
                    } else {
                        $order = $this->msvendordetail->select('a.*, b.default_config')
                            ->join('msvendor b', 'b.id = a.vendorid')
                            ->get(array(
                                'a.vendorid' => $productRow->vendorid,
                                'a.apitype' => 'Order'
                            ));

                        if ($order->num_rows() == 0) {
                            throw new Exception('Konfigurasi vendor tidak ditemukan');
                        }

                        $vendor_order = $order->row();

                        if ($vendor_order->default_config == null) {
                            throw new Exception('Konfigurasi vendor tidak ditemukan');
                        }

                        $response_indicator = json_decode($vendor_order->response_indicator);
                        $response_setting = json_decode($vendor_order->response_setting);

                        $dynamicvendor = new DynamicVendor($productRow->vendorid, json_decode($vendor_order->default_config, true));
                        $order = $dynamicvendor->order($insert_id);

                        if (
                            ($response_indicator->index == null && isset($order[$response_indicator->key]) && (
                                $response_indicator->datatype == 'string' && $order[$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'number' && $order[$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'boolean' && $order[$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'exists' && isset($order[$response_indicator->key])
                            ))
                            || ($response_indicator->index != null && isset($order[$response_indicator->index][$response_indicator->key]) && (
                                $response_indicator->datatype == 'string' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'number' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'boolean' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'exists' && isset($order[$response_indicator->index][$response_indicator->key])
                            ))
                        ) {
                            $var_referenceid = $response_setting->referenceid ?? null;
                            $var_price = $response_setting->price ?? null;
                            $var_status = $response_setting->status ?? null;
                            $var_note = $response_setting->note ?? null;
                            $var_sn = $response_setting->sn ?? null;
                            $var_errorrefund = $response_setting->errorrefund;

                            $exploding_errorefund = explode('[#]', strtolower($var_errorrefund ?? ''));

                            if ($response_setting->index != null) {
                                $order = $order[$response_setting->index] ?? null;
                            }

                            $referenceid = $var_referenceid != null ? ($order[$var_referenceid] ?? null) : null;
                            $price = $var_price != null ? ($order[$var_price] ?? null) : null;
                            $status = $var_status != null ? ($order[$var_status] ?? null) : null;
                            $note = $var_note != null ? ($order[$var_note] ?? null) : null;
                            $sn = $var_sn != null ? ($order[$var_sn] ?? null) : null;

                            if ($status != null) {
                                if (in_array($status, $exploding_errorefund)) {
                                    log_message_user('error', '[MULTIVENDOR ORDER] Failed to parsing error refund, response: ' . json_encode($order), $this->merchant->id);

                                    throw new Exception('Gagal melakukan transaksi');
                                }
                            } else {
                                if ($var_status == null) {
                                    $status = 'pending';
                                } else if ($var_status != null && $status == null) {
                                    log_message_user('error', '[MULTIVENDOR ORDER] Failed to parsing status, response: ' . json_encode($order), $this->merchant->id);

                                    throw new Exception('Gagal melakukan transaksi');
                                }
                            }

                            $update = array();
                            $update['jsonresponse'] = json_encode($order);

                            if ($referenceid != null) {
                                $update['servercode'] = $referenceid;
                            }

                            if ($price != null) {
                                $update['price'] = $price;
                            }

                            if ($status != null) {
                                $update['status'] = $status;
                            }

                            if ($note != null) {
                                $update['note'] = $note;
                            }

                            if ($sn != null) {
                                $update['sn'] = $sn;
                            }

                            $this->trorder->update(array(
                                'id' => $insert_id
                            ), $update);

                            $this->db->trans_commit();

                            $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                            return JSONResponseDefault('OK', 'Transaksi berhasil');
                        } else {
                            log_message_user('error', '[MULTIVENDOR ORDER] Failed to parse response, response: ' . json_encode($order), $this->merchant->id);

                            throw new Exception('Gagal melakukan transaksi');
                        }
                    }
                }
            }

            $this->db->trans_commit();

            $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

            return JSONResponseDefault('OK', 'Pembelian berhasil');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function modalsubprovider()
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        $data = stringEncryption('decrypt', getPost('data'));
        $data = json_decode($data);
        $category = $data->category;
        $brand = $data->brand;

        $disabled = $this->disabledcategory->result(array(
            'userid' => $this->merchant->id,
            'category_apikey' => 'PPOB',
        ));

        $disabled_category = array();
        foreach ($disabled as $key => $value) {
            $disabled_category[] = $value->categoryname;
        }

        $where = array(
            'userid' => $this->merchant->id,
            'subcategory_apikey' => 'PRABAYAR',
            'status' => 1,
            'category' => $category,
            'brand' => $brand,
            'type !=' => null
        );

        if ($this->merchant->multivendor != 1) {
            $vendor = getCurrentVendor('PPOB', $this->merchant->id);
            $where['vendor'] = $vendor;
            $where['vendorid'] = null;
        } else {
            $where['vendorid !='] = null;
            $where['vendorenabled'] = 1;
        }

        $subcategoryproduct = $this->db->select('type')
            ->from('msproduct')
            ->where($where)
            ->group_by('type')
            ->order_by('type', 'ASC');

        if (count($disabled_category) > 0) {
            $this->db->where_not_in('category', $disabled_category);
        }

        $subcategoryproduct = $this->db->get()->result();

        $data = array();
        $data['subcategoryproduct'] = $subcategoryproduct;
        $data['category'] = $category;
        $data['brand'] = $brand;

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => viewTemplate($this->merchant->id, "dashboard/modalsubprovider", $data, true)
        ));
    }

    public function ppob_product()
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        $key = getGet('key');
        $key = stringEncryption('decrypt', $key);

        if ($key == null) {
            return show_404();
        }

        $key = json_decode($key, true);

        if (!isset($key['category']) || !isset($key['brand'])) {
            return show_404();
        }
        $category = $key['category'];
        $subcategory = $key['subcategory'];
        $brand = $key['brand'];

        $disabled = $this->disabledcategory->result(array(
            'userid' => $this->merchant->id,
            'category_apikey' => 'PPOB'
        ));

        $disabled_category = array();
        foreach ($disabled as $key => $value) {
            $disabled_category[] = $value->categoryname;
        }

        $disabledbrand = $this->disabledbrand->result(array(
            'userid' => $this->merchant->id,
        ));

        $disabled_brand = array();
        foreach ($disabledbrand as $key => $value) {
            $disabled_brand[] = $value->brandname;
        }

        $this->msproduct->order_by('a.price', 'ASC')
            ->where_not_in('category', $disabled_category)
            ->where_not_in('brand', $disabled_brand);

        if ($subcategory != null) {
            $this->msproduct->where('a.type', $subcategory);
        }

        $where = array(
            'userid' => $this->merchant->id,
            'subcategory_apikey' => 'PRABAYAR',
            'category' => $category,
            'brand' => $brand,
            'status' => 1
        );

        if ($this->merchant->multivendor != 1) {
            $vendor = getCurrentVendor('PPOB', $this->merchant->id);
            $where["(vendor = '$vendor' OR vendor IS NULL) ="] = true;
            $where['vendorid'] = null;
        } else {
            $where["((vendorid IS NOT NULL AND vendorenabled = 1) OR vendor IS NULL) ="] = true;
        }

        $get = $this->msproduct->result($where);

        $currentuser = getCurrentUser();

        if (($currentuser->roleid ?? null) == null) {
            $getrole = $this->msrole->get(array(
                'createdby' => $this->merchant->id,
                'isdefault' => '1'
            ))->row();
        } else {
            $getrole = $this->msrole->get(array(
                'createdby' => $this->merchant->id,
                'id' => $currentuser->roleid
            ))->row();
        }

        if ($getrole != null) {
            if ($getrole->discounttype == 'Simple') {
                $getrolediscount = $this->msrolediscount->get(array(
                    'userid' => $this->merchant->id,
                    'roleid' => $getrole->id,
                ))->row();

                $discount = $getrolediscount->trxdiscount ?? 0;
            } else {
                $discount = $this->msrolediscountadv->get(array(
                    'createdby' => $this->merchant->id,
                    'roleid' => $getrole->id,
                    'servicetype' => 'Prabayar'
                ))->result();
            }
        } else {
            $getrole = new stdClass();
            $getrole->discounttype = 'Simple';
            $discount = 0;
        }

        $getbrand = $this->brandimage->select('a.*,b.asseturl')
            ->join('msicons b', 'b.id = a.assetid', 'left')
            ->get(array('brandname' => $brand))
            ->row();

        $str = new String_Helper();

        $data = array();
        $data['title'] = 'Product';
        $data['content'] = 'dashboard/ppob_product';
        $data['product'] = $get;
        $data['discount'] = $discount;
        $data['CDN_MAIN'] = $str::CDN_MAIN;
        $data['brand'] = $getbrand;
        $data['getrole'] = $getrole;

        return viewTemplate($this->merchant->id, "master", $data);
    }
}
