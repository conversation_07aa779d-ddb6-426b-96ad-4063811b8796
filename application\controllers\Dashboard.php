<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsProduct $msproduct
 * @property TrOrder $trorder
 * @property MsUsers $msusers
 * @property Tickets $tickets
 * @property ProductPriceLog $productpricelog
 * @property MsNews $msnews
 * @property HistoryBalance $historybalance
 * @property CI_DB_mysqli_driver $db
 * @property Datatables $datatables
 * @property ApiKeys_WhatsApp $apikeys_whatsapp
 * @property MsVendor $msvendor
 */
class Dashboard extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsProduct', 'msproduct');
        $this->load->model('TrOrder', 'trorder');
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('Tickets', 'tickets');
        $this->load->model('ProductPriceLog', 'productpricelog');
        $this->load->model('MsNews', 'msnews');
        $this->load->model('ApiKeys_WhatsApp', 'apikeys_whatsapp');
        $this->load->model('HistoryBalance', 'historybalance');
        $this->load->model('MsVendor', 'msvendor');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        }

        if (isUser()) {
            $currentiduser = getCurrentIdUser();
            $dashboard_filter = getSessionValue('DASHBOARD_FILTER');

            if ($dashboard_filter != null) {
                $month = date('m', strtotime($dashboard_filter));
                $year = date('Y', strtotime($dashboard_filter));

                $extract_filter[0] = $year;
                $extract_filter[1] = $month;
            } else {
                $extract_filter[0] = getCurrentDate('Y');
                $extract_filter[1] = getCurrentDate('m');

                $dashboard_filter = getCurrentDate('Y-m');
            }

            $data = array();
            $data['title'] = 'Server PPOB & SMM - Beranda';
            $data['content'] = 'dashboard';
            $data['member'] = $this->msusers->total(array(
                'a.merchantid' => $currentiduser,
                'a.isdeleted' => null,
            ));
            $data['saldomember'] = $this->msusers->sum('a.balance', array(
                'a.merchantid' => $currentiduser,
                'a.isdeleted' => null
            ));
            $data['currentiduser'] = $currentiduser;

            if (getCurrentUser()->multivendor != 1) {
                $apikeysppob = getCurrentAPIKeys('PPOB', $currentiduser);
                $apikeyssmm = getCurrentAPIKeys('SMM', $currentiduser);

                $data['balanceppob'] = $apikeysppob != null ? $apikeysppob->balance : 0;
                $data['balancesmm'] = $apikeyssmm != null ? $apikeyssmm->balance : 0;
            } else {
                $apikeysppob = $this->msvendor->sum('balance', array(
                    'category' => 'PPOB',
                    'userid' => getCurrentIdUser()
                ));
                $apikeyssmm = $this->msvendor->sum('balance', array(
                    'category' => 'SMM',
                    'userid' => getCurrentIdUser()
                ));

                $data['balanceppob'] = $apikeysppob;
                $data['balancesmm'] = $apikeyssmm;
            }

            $queue = array(
                'a.queuetransaction' => 1,
                "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true,
            );

            $data['queue'] = $this->trorder->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->total($queue);

            $pending = array(
                "(LOWER(a.status) = 'pending' OR LOWER(a.status) = 'in progress' OR LOWER(a.status) = 'processing') = " => true,
                "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true,
                "(a.queuetransaction IS NULL OR a.queuetransaction = 0) = " => true,
            );

            if ($dashboard_filter != null && $extract_filter != null) {
                $pending['MONTH(a.createddate) ='] = $extract_filter[1];
                $pending['YEAR(a.createddate) ='] = $extract_filter[0];
            }

            $data['pending'] = $this->trorder->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->total($pending);

            $failed = array(
                "(LOWER(a.status) = 'failed' OR LOWER(a.status) = 'gagal' OR LOWER(a.status) = 'partial' OR LOWER(a.status) = 'error' OR LOWER(a.status) = 'canceled') =" => true,
                "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true,
            );

            if ($dashboard_filter != null && $extract_filter != null) {
                $failed['MONTH(a.createddate) ='] = $extract_filter[1];
                $failed['YEAR(a.createddate) ='] = $extract_filter[0];
            }

            $data['failed'] = $this->trorder->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->total($failed);

            $success = array(
                "(LOWER(a.status) = 'success' OR LOWER(a.status) = 'sukses' OR LOWER(a.status) = 'completed') =" => true,
                "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true,
            );

            if ($dashboard_filter != null && $extract_filter != null) {
                $success['MONTH(a.createddate) ='] = $extract_filter[1];
                $success['YEAR(a.createddate) ='] = $extract_filter[0];
            }

            $data['success'] = $this->trorder->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->total($success);

            $omsetppob = array(
                "(LOWER(a.status) = 'success' OR LOWER(a.status) = 'sukses' OR LOWER(a.status) = 'completed') =" => true,
                'a.type' => 'PPOB',
                "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true,
            );

            if ($dashboard_filter != null && $extract_filter != null) {
                $omsetppob['MONTH(a.createddate) ='] = $extract_filter[1];
                $omsetppob['YEAR(a.createddate) ='] = $extract_filter[0];
            }

            $data['omsetppob'] = $this->trorder->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->sum('a.price', $omsetppob);

            $omsetsmm = array(
                "(LOWER(a.status) = 'success' OR LOWER(a.status) = 'sukses' OR LOWER(a.status) = 'completed') =" => true,
                'a.type' => 'SMM',
                "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true,
            );

            if ($dashboard_filter != null && $extract_filter != null) {
                $omsetsmm['MONTH(a.createddate) ='] = $extract_filter[1];
                $omsetsmm['YEAR(a.createddate) ='] = $extract_filter[0];
            }

            $data['omsetsmm'] = $this->trorder->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->sum('a.price', $omsetsmm);

            $profit = array(
                "(LOWER(a.status) = 'success' OR LOWER(a.status) = 'sukses' OR LOWER(a.status) = 'completed') =" => true,
                "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true,
            );

            if ($dashboard_filter != null && $extract_filter != null) {
                $profit['MONTH(a.createddate) ='] = $extract_filter[1];
                $profit['YEAR(a.createddate) ='] = $extract_filter[0];
            }

            $data['profit'] = $this->trorder->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->sum('a.profit', $profit);

            $unrealized_profit = array(
                "(LOWER(a.status) = 'pending' OR LOWER(a.status) = 'in progress' OR LOWER(a.status) = 'processing') =" => true,
                "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true,
            );

            if ($dashboard_filter != null && $extract_filter != null) {
                $unrealized_profit['MONTH(a.createddate) ='] = $extract_filter[1];
                $unrealized_profit['YEAR(a.createddate) ='] = $extract_filter[0];
            }

            $data['unrealized_profit'] = $this->trorder->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->sum('a.profit', $unrealized_profit);

            $lasttransaction = array(
                "(c.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true,
            );

            if ($dashboard_filter != null && $extract_filter != null) {
                $lasttransaction['MONTH(a.createddate) ='] = $extract_filter[1];
                $lasttransaction['YEAR(a.createddate) ='] = $extract_filter[0];
            }

            $data['lasttransaction'] = $this->trorder->select('a.*, b.productname, c.name AS buyername')
                ->join('msproduct b', 'b.id = a.serviceid', 'LEFT')
                ->join('msusers c', 'c.id = a.userid', 'LEFT')
                ->limit(10)
                ->order_by('a.createddate', 'DESC')
                ->result($lasttransaction);

            $ticket = array(
                'b.merchantid' => $currentiduser
            );

            if ($dashboard_filter != null && $extract_filter != null) {
                $ticket['MONTH(a.createddate) ='] = $extract_filter[1];
                $ticket['YEAR(a.createddate) ='] = $extract_filter[0];
            }

            $data['ticket'] = $this->tickets->join('msusers b', 'b.id = a.userid')->total($ticket);

            $ticketpending = array(
                'b.merchantid' => $currentiduser,
                'a.status' => 'Pending'
            );

            if ($dashboard_filter != null && $extract_filter != null) {
                $ticketpending['MONTH(a.createddate) ='] = $extract_filter[1];
                $ticketpending['YEAR(a.createddate) ='] = $extract_filter[0];
            }

            $data['ticketpending'] = $this->tickets->join('msusers b', 'b.id = a.userid')->total($ticketpending);

            $ticketdone = array(
                'b.merchantid' => $currentiduser,
                'a.status' => 'Done'
            );

            if ($dashboard_filter != null && $extract_filter != null) {
                $ticketdone['MONTH(a.createddate) ='] = $extract_filter[1];
                $ticketdone['YEAR(a.createddate) ='] = $extract_filter[0];
            }

            $data['ticketdone'] = $this->tickets->join('msusers b', 'b.id = a.userid')->total($ticketdone);

            $order = $this->db->select("c.name, a.id, GROUP_CONCAT( 'Pembelian produk ', b.category_apikey, ': ', b.productname ) AS description, 'minus' AS type, a.price AS nominal, a.createddate")
                ->from('trorder a')
                ->join('msproduct b', 'b.id = a.serviceid')
                ->join('msusers c', 'c.id = a.userid', 'LEFT')
                ->where("(LOWER(a.status) = 'sukses' OR LOWER(a.status) = 'success' OR LOWER(a.status) = 'completed') =", true)
                ->group_by('a.id')
                ->where('b.userid', $currentiduser);

            if ($dashboard_filter != null && $extract_filter != null) {
                $order = $order->where('MONTH(a.createddate) =', $extract_filter[1])
                    ->where('YEAR(a.createddate) =', $extract_filter[0])
                    ->get_compiled_select();
            } else {
                $order = $order->get_compiled_select();
            }

            $deposit = $this->db->select("b.name, a.id, 'Topup saldo akun' AS description, 'plus' AS type, a.nominal, a.createddate")
                ->from('deposit a')
                ->join('msusers b', 'b.id = a.userid')
                ->where('a.status', 'Success')
                ->where('b.merchantid', $currentiduser);

            if ($dashboard_filter != null && $extract_filter != null) {
                $deposit = $deposit->where('MONTH(a.createddate) =', $extract_filter[1])
                    ->where('YEAR(a.createddate) =', $extract_filter[0])
                    ->get_compiled_select();
            } else {
                $deposit = $deposit->get_compiled_select();
            }

            $data['mutation'] = $this->db->query("$order UNION $deposit ORDER BY createddate DESC LIMIT 10")->result();
            $data['productlog'] = $this->productpricelog->select('a.type, a.oldprice, a.newprice, b.productname')
                ->join('msproduct b', 'b.id = a.productid')
                ->limit(10)
                ->group_by('a.productid')
                ->order_by('a.createddate', 'DESC')
                ->result(array(
                    'a.userid' => $currentiduser,
                    'DATE(a.createddate) =' => getCurrentDate('Y-m-d'),
                    "(a.newprice = b.price) =" => true
                ));

            $data['transaction_month'] = $this->trorder->select('MONTH(a.createddate) AS bulan, YEAR(a.createddate) AS tahun')
                ->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->group_by('MONTH(a.createddate), YEAR(a.createddate)')
                ->order_by('a.createddate', 'DESC')
                ->result(array(
                    "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true,
                ));

            $data['extract_filter'] = $extract_filter;
            $data['news'] = $this->msnews->select('a.createddate, a.content, c.name, c.badge')
                ->join('msusers b', 'b.id = a.userid')
                ->join('msnewstype c', 'c.id = a.typeid')
                ->order_by('a.createddate', 'DESC')
                ->result(array(
                    'b.role' => 'Admin'
                ));
            $data['replied_ticket'] = $this->tickets->join('msusers b', 'b.id = a.userid')
                ->total(array(
                    'status' => 'User Membalas',
                    'b.merchantid' => $currentiduser,
                ));
            $data['best_category'] = $this->trorder->select('c.category, COUNT(*) AS total')
                ->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->join('msproduct c', 'c.id = a.serviceid')
                ->group_by('c.category')
                ->order_by('total', 'DESC')
                ->limit(10)
                ->result(array(
                    'MONTH(a.createddate) =' => $extract_filter[1],
                    'YEAR(a.createddate) =' => $extract_filter[0],
                    "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true,
                    "(a.status = 'success' OR a.status = 'sukses' OR a.status = 'completed') =" => true
                ));
        } else {
            $data = array();
            $data['title'] = 'Server PPOB & SMM - Beranda';

            if (isAdmin()) {
                $data['content'] = 'dashboard_admin';
                $data['member'] = $this->msusers->total(array(
                    'merchantid' => null,
                    'role' => 'User',
                ));
                $data['active_member'] = $this->msusers->total(array('merchantid' => null, 'expireddate >' => getCurrentDate()));
            }
        }

        return $this->load->view('master', $data);
    }

    public function process_set_filter()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        }

        $transactiondate = getGet('transactiondate');

        if ($transactiondate != 'All') {
            $extract = explode('-', $transactiondate);

            if (!isset($extract[0]) || !isset($extract[1])) {
                return redirect(base_url('dashboard'));
            }

            if (!checkdate($extract[1], 1, $extract[0])) {
                return redirect(base_url('dashboard'));
            }

            setSessionValue(array(
                'DASHBOARD_FILTER' => $transactiondate
            ));
        } else {
            setSessionValue(array(
                'DASHBOARD_FILTER' => null
            ));
        }

        return redirect(base_url('dashboard'));
    }

    public function process_reset_filter()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        }

        setSessionValue(array(
            'DASHBOARD_FILTER' => null
        ));

        return redirect(base_url('dashboard'));
    }

    public function queuetransaction()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('dashboard/queuetransaction', array(), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function datatables_queuetransaction()
    {
        try {
            if (isLogin() && (isUser())) {
                $data = array();

                $datatables = $this->datatables->make('TrOrder', 'QueryDatatables', 'SearchDatatables');

                $where = array(
                    'c.merchantid' => getCurrentIdUser(),
                    'a.queuetransaction' => 1
                );

                foreach ($datatables->getData($where) as $key => $value) {
                    $detail = array();
                    $detail[] = $value->clientcode;
                    $detail[] = DateFormat($value->createddate, 'd F Y H:i:s');
                    $detail[] = $value->productname ?? '- Layanan telah dihapus -';
                    $detail[] = IDR($value->price);
                    $detail[] = $value->hostvendor ?? '- Vendor tidak diketahui -';
                    $detail[] = $value->buyername;
                    $detail[] = "<button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"cancelTransaction('" . stringEncryption('encrypt', $value->id) . "')\">
                        <i class=\"fa fa-close\"></i>
                        <span>Cancel</span>
                    </button>";

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    private function send_notification($orderid, $userid, $phonenumber)
    {
        try {
            // Kirim Firebase notification untuk order
            sendFirebaseNotificationOrder($orderid, $userid);

            $apikeys_whatsapp = $this->apikeys_whatsapp->get(array(
                'userid' => $userid,
            ));

            if ($apikeys_whatsapp->num_rows() == 0) {
                return false;
            }

            $row = $apikeys_whatsapp->row();

            $wablasingateway = new WABlasinGateway(stringEncryption('decrypt', $row->apikey), stringEncryption('decrypt', $row->devicecode));

            $messagenotification = replaceParameterNotification($orderid, $userid);
            if ($messagenotification != null && $phonenumber != null) {
                $phonenumber = changePrefixPhone($phonenumber);

                $send = $wablasingateway->sendMessage($phonenumber, $messagenotification);

                if (!isset($send->RESULT) || (isset($send->RESULT) && $send->RESULT != 'OK')) {
                    log_message_user('error', '[WHATSAPP SEND MESSAGE] Send notification failed: ' . json_encode($send), $userid);
                }

                return true;
            }

            return false;
        } catch (Exception $ex) {
            return false;
        }
    }

    public function process_cancel_transaction()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Transaksi tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);
            $currentiduser = getCurrentIdUser();

            $get = $this->trorder->select('a.*, b.phonenumber')
                ->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->get(array(
                    'a.id' => $id,
                    'a.queuetransaction' => 1,
                    'LOWER(a.status) =' => 'pending',
                    "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true
                ));

            if ($get->num_rows() == 0) {
                throw new Exception('Transaksi tidak ditemukan');
            }

            $row = $get->row();

            $update = array();
            $update['status'] = 'gagal';
            $update['queuetransaction'] = 0;

            $this->trorder->update(array(
                'id' => $id
            ), $update);

            pullTransaction($row->userid ?? $row->clientip);

            if ($row->userid != null) {
                $check_history_balance = $this->historybalance->total(array(
                    'userid' => $row->userid,
                    'type' => 'IN',
                    'orderid' => $id,
                ));

                if ($check_history_balance == 0) {
                    $currentbalance = getCurrentBalance($row->userid, true);

                    $inserthistorybalance = array();
                    $inserthistorybalance['userid'] = $row->userid;
                    $inserthistorybalance['type'] = 'IN';

                    if ($row->paymenttype == 'Otomatis' && $row->fee != null) {
                        $inserthistorybalance['nominal'] = $row->price - round($row->fee);
                    } else {
                        $inserthistorybalance['nominal'] = $row->price;
                    }

                    $inserthistorybalance['currentbalance'] = $currentbalance;
                    $inserthistorybalance['orderid'] = $id;
                    $inserthistorybalance['createdby'] = $row->userid;
                    $inserthistorybalance['createddate'] = getCurrentDate();

                    $this->historybalance->insert($inserthistorybalance);

                    $updateUser = array();

                    if ($row->paymenttype == 'Otomatis' && $row->fee != null) {
                        $updateUser['balance'] = $currentbalance + ($row->price - round($row->fee));
                    } else {
                        $updateUser['balance'] = $currentbalance + $row->price;
                    }

                    $this->msusers->update(array(
                        'id' => $row->userid
                    ), $updateUser);
                }
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal membatalkan transaksi');
            }

            $this->db->trans_commit();

            $this->send_notification($id, $currentiduser, $row->phonenumber ?? $row->phonenumber_order);

            return JSONResponseDefault('OK', 'Transaksi berhasil dibatalkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function detailprofit()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $dashboard_filter = getSessionValue('DASHBOARD_FILTER');
            $currentiduser = getCurrentIdUser();

            if ($dashboard_filter != null) {
                $month = date('m', strtotime($dashboard_filter));
                $year = date('Y', strtotime($dashboard_filter));
            } else {
                $month = date('m');
                $year = date('Y');
            }

            $data = array();
            $data['profit_date'] = $this->trorder->select('DATE(a.createddate) AS date, SUM(a.price) AS omzet, SUM(a.profit) AS profit')
                ->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->where_in('a.status', array('success', 'sukses', 'completed'))
                ->group_by('DATE(a.createddate)')
                ->order_by('DATE(a.createddate)', 'ASC')
                ->result(array(
                    'MONTH(a.createddate) =' => $month,
                    'YEAR(a.createddate) =' => $year,
                    "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true
                ));
            $data['profit_categoryproduct'] = $this->trorder->select('c.category, SUM(a.price) AS omzet, SUM(a.profit) AS profit')
                ->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->join('msproduct c', 'c.id = a.serviceid')
                ->where_in('a.status', array('success', 'sukses', 'completed'))
                ->group_by('c.category')
                ->order_by('profit', 'DESC')
                ->result(array(
                    'MONTH(a.createddate) =' => $month,
                    'YEAR(a.createddate) =' => $year,
                    "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true
                ));

            $categoryproduct_label = array();
            $categoryproduct_profit = array();
            $categoryproduct_color = array();
            foreach ($data['profit_categoryproduct'] as $key => $value) {
                $categoryproduct_label[] = $value->category;
                $categoryproduct_profit[] = $value->profit;
                $categoryproduct_color[] = generateRandomColor();
            }

            $data['profit_categoryproduct_label'] = $categoryproduct_label;
            $data['profit_categoryproduct_profit'] = $categoryproduct_profit;
            $data['profit_categoryproduct_color'] = $categoryproduct_color;

            $this->trorder->select('CASE WHEN a.category_apikey IS NULL THEN c.category_apikey ELSE a.category_apikey END AS category_apikey, SUM(a.price) AS omzet, SUM(a.profit) AS profit', false)
                ->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->join('msproduct c', 'c.id = a.serviceid', 'LEFT')
                ->where_in('a.status', array('success', 'sukses', 'completed'))
                ->group_by('category_apikey')
                ->order_by('profit', 'DESC')
                ->result(array(
                    'MONTH(a.createddate) =' => $month,
                    'YEAR(a.createddate) =' => $year,
                    "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true
                ));

            $profit_business = $this->db->last_query();

            $data['profit_business'] = $this->db->query("SELECT category_apikey, SUM(omzet) AS omzet, SUM(profit) AS profit FROM ($profit_business) x GROUP BY x.category_apikey")->result();

            $business_label = array();
            $business_profit = array();
            $business_color = array();
            foreach ($data['profit_business'] as $key => $value) {
                $business_label[] = $value->category_apikey;
                $business_profit[] = $value->profit;
                $business_color[] = generateRandomColor();
            }

            $data['profit_business_label'] = $business_label;
            $data['profit_business_profit'] = $business_profit;
            $data['profit_business_color'] = $business_color;

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('dashboard/detailprofit', $data, true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function datatables_monitoring_member()
    {
        try {
            if (isLogin() && isAdmin()) {
                $data = array();

                $datatable = $this->datatables->make('MsUsers', 'QueryDatatables_Monitoring_Member', 'SearchDatatables');

                foreach (
                    $datatable->getData(array(
                        'a.merchantid' => null,
                        'a.expireddate >' => getCurrentDate()
                    )) as $key => $value
                ) {
                    $detail = array();

                    $actions = "";

                    $actions .= "<a href=\"https://$value->domain\" class=\"btn btn-success btn-sm mb-1\" target=\"_blank\">
                        <i class=\"fas fa-paper-plane\"></i>
                        <span>Kunjungi Website</span>
                    </a>

                    <button type=\"button\" class=\"btn btn-danger btn-sm mb-1\" onclick=\"historyLogError('" . stringEncryption('encrypt', $value->id) . "')\">
                        <i class=\"fas fa-history\"></i>
                        <span>Riwayat Error</span>
                    </button>
                    
                    <button type=\"button\" class=\"btn btn-primary btn-sm mb-1\" onclick=\"modalReport('" . stringEncryption('encrypt', $value->id) . "')\">
                        <i class=\"fas fa-file\"></i>
                        <span>Laporan Profit dan Omset</span>
                    </button>";

                    if ($actions == "") {
                        $actions = "N/A";
                    }



                    $detail[] = $value->companyname ?? '-';
                    $detail[] = $value->email;
                    $detail[] = $value->totalorder > 0 ? IDR($value->totalorder) : 0;
                    $detail[] = $value->totalordermonth > 0 ? IDR($value->totalordermonth) : 0;
                    $detail[] = $value->totalordertoday > 0 ? IDR($value->totalordertoday) : 0;
                    $detail[] = $actions;

                    $data[] = $detail;
                }

                return $datatable->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function reportprofitomset()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isAdmin()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->msusers->get(array(
                'id' => $id,
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('dashboard/reportprofitomset', array(
                    'report' => $row
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function datatables_reportprofitomset()
    {
        try {
            if (isLogin() && isAdmin()) {
                $id = getPost('id');
                $date = getPost('date');

                $id = stringEncryption('decrypt', $id);

                $data = array();

                $month = date('m', strtotime($date));
                $year = date('Y', strtotime($date));

                $extract_filter[0] = $year;
                $extract_filter[1] = $month;

                $datatable = $this->datatables->make('TrOrder', 'QueryDatatables_omsetprofit', 'SearchDatatables');

                $totalall_profit = 0;
                $totalall_omset = 0;
                foreach (
                    $datatable->getData(array(
                        "(LOWER(a.status) = 'success' OR LOWER(a.status) = 'sukses' OR LOWER(a.status) = 'completed') =" => true,
                        "(b.merchantid = '$id' OR a.merchantid_order = '$id') =" => true,
                        "MONTH(a.createddate)" => $extract_filter[1],
                        "YEAR(a.createddate)" => $extract_filter[0]
                    )) as $key => $value
                ) {
                    $detail = array();

                    $detail[] = $value->dateorder != null ? DateFormat($value->dateorder, 'd F Y') : '-';
                    $detail[] = IDR($value->totalomset) ?? 0;
                    $detail[] = IDR($value->totalprofit) ?? 0;
                    $data[] = $detail;

                    $totalall_profit += $value->totalprofit;
                    $totalall_omset += $value->totalomset;
                }
                $total = array();
                $total[] = "<b>Total</b>";
                $total[] = "<b>" . IDR($totalall_omset) . "</b>";
                $total[] = "<b>" . IDR($totalall_profit) . "</b>";
                $data[] = $total;

                return $datatable->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function historyerror()
    {
        try {
            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isAdmin()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $this->load->view('dashboard/historyerror', array(
                    'id' => stringEncryption('decrypt', getPost('id'))
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function datatables_historyerror()
    {
        try {
            if (isLogin() && isAdmin()) {
                $id = getPost('id');

                if ($id == null) {
                    throw new Exception('Data tidak ditemukan');
                }

                $data = array();
                $datatables = $this->datatables->make('ErrorLogger', 'QueryDatatables', 'SearchDatatables');

                foreach ($datatables->getData(array('userid' => $id)) as $key => $value) {
                    $detail = array();
                    $detail[] = "<span class=\"badge badge-danger\">$value->type</span>";
                    $detail[] = $value->message;
                    $detail[] = DateFormat($value->createddate, 'd F Y H:i:s');

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
