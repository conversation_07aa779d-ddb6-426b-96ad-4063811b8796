<?php
defined('BASEPATH') or exit('No direct script access allowed');

class HistoryLogin extends MY_Model
{
    protected $table = 'historylogin';

    public $SearchDatatables = array();

    public function QueryDatatables()
    {
        $this->db->select('a.createddate, a.ipaddress, a.useragent')
            ->from($this->table . ' a')
            ->order_by('a.createddate', 'DESC');

        return $this;
    }
}