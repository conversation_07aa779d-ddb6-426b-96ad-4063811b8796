<?php defined('BASEPATH') or die('No direct script access allowed!'); ?>

<div class="modal fade" id="ModalHistoryLogin" tabindex="-1" role="dialog" aria-labelledby="ModalHistoryLoginLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="ModalHistoryLoginLabel">Riwayat Login - <?= $user->name ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-striped table-row-bordered gy-5 datatables-history-login">
                        <thead>
                            <tr class="fw-semibold fs-6 text-muted">
                                <th><PERSON>gal</th>
                                <th>IP Address</th>
                                <th>User Agent</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Tutup</button>
            </div>
        </div>
    </div>
</div>

<script>
$('#table-history-login').DataTable({
    processing: true,
    serverSide: true,
    ajax: {
        url: '<?= base_url("data/datatables_history_login") ?>',
        type: 'POST',
        data: {
            id: '<?= getPost("id") ?>'
        }
    },
    order: [
        [0, 'desc']
    ],
    columns: [{
            data: 0
        },
        {
            data: 1
        },
        {
            data: 2
        }
    ]
});
</script>
</script>