<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property MsUsers $msusers
 * @property CI_Output $output
 * @property MobileSession $mobilesession
 * @property MsPaymentGateway $mspaymentgateway
 * @property MobileOtp $mobileotp
 * @property ApiKeys_WhatsApp $apikeys_whatsapp
 */
class Auth extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsUsers', 'msusers');
        $this->load->model('MobileSession', 'mobilesession');
        $this->load->model('ForgotPassword', 'forgotpassword');
        $this->load->model('MsPaymentGateway', 'mspaymentgateway');
        $this->load->model('MobileOtp', 'mobileotp');
        $this->load->model('ApiKeys_WhatsApp', 'apikeys_whatsapp');
    }

    public function login()
    {
        $email = getPost('email');
        $password = getPost('password');
        $fcm_token = getPost('fcm_token');

        if ($email == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Email / Username tidak boleh kosong'
            ));
        }

        if ($password == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Password tidak boleh kosong'
            ));
        }

        $email = removeSymbol($email);
        $password = removeSymbol($password);

        $user = $this->msusers->get(array(
            "(a.email = '" . $email . "' OR a.username = '" . $email . "') =" => true,
            'a.merchantid' => $this->merchant->id,
        ));

        if ($user->num_rows() == 0) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Email / Username atau password yang anda masukkan salah'
            ));
        }

        $row = $user->row();

        if (!password_verify($password, $row->password)) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Email / Username atau password yang anda masukkan salah'
            ));
        }

        if ($row->isemailverified != 1) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Email belum terverifikasi'
            ));
        }

        if ($row->isdeleted == 1) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Akun anda telah dihapus'
            ));
        }

        $data = array();
        $data['userid'] = $row->id;
        $data['token'] = stringEncryption('encrypt', json_encode(array(
            'id' => $row->id,
            'name' => $row->name,
            'role' => $row->role,
            'merchantid' => $row->merchantid,
            'createddate' => getCurrentDate()
        )));
        $data['createddate'] = getCurrentDate();
        $data['createdby'] = $row->id;
        $data['fcm_token'] = $fcm_token;

        $mobileSession = $this->mobilesession->insert($data);

        if (!$mobileSession) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Login gagal'
            ));
        }

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => true,
            'message' => 'Login berhasil',
            'data' => array(
                'token' => $data['token']
            )
        ));
    }

    private function validateToken($token)
    {
        if ($token == null) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $token = stringEncryption('decrypt', $token);

        if ($token == null) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $token = json_decode($token);

        if (json_last_error() != JSON_ERROR_NONE) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $validate = $this->mobilesession->total(array(
            'token' => getPost('token')
        ));

        if ($validate == 0) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $user = $this->msusers->get(array(
            'id' => $token->id,
            'merchantid' => $this->merchant->id,
        ));

        if ($user->num_rows() == 0) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $row = $user->row();

        $midtrans = $this->mspaymentgateway->get(array(
            'userid' => $row->merchantid,
            'type' => 'Payment Gateway',
            'vendor' => 'Midtrans'
        ))->row();

        $midtransClientKey = null;

        if ($midtrans != null) {
            $detail = json_decode(stringEncryption('decrypt', $midtrans->detail), true);
            $midtransClientKey = $detail['clientkey'] ?? null;
        }

        return array(
            'status' => true,
            'message' => 'Data berhasil diambil',
            'data' => $row,
            'midtrans' => [
                'clientkey' => $midtransClientKey
            ]
        );
    }

    public function profile()
    {
        $token = getPost('token');

        $validate = $this->validateToken($token);

        if ($validate['status'] == false) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => $validate['message']
            ));
        }

        $row = $validate['data'];

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => true,
            'message' => 'Data berhasil diambil',
            'data' => array(
                'name' => $row->name,
                'email' => $row->email,
                'balance' => (int)$row->balance ?? 0,
                'phonenumber' => $row->phonenumber,
                'profile_image' => $row->profile_image,
                'role' => $row->role,
                'midtrans' => $validate['midtrans']
            )
        ));
    }

    public function logout()
    {
        $token = getPost('token');

        $this->mobilesession->delete(array(
            'token' => $token
        ));

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => true,
            'message' => 'Logout berhasil'
        ));
    }

    public function register()
    {
        try {
            $this->db->trans_begin();

            $name = getPost('name');
            $email = getPost('email');
            $password = getPost('password');
            $confirmpassword = getPost('confirmpassword');
            $pin = getPost('pin');

            if ($name == null) {
                throw new Exception('Nama tidak boleh kosong');
            }

            if ($email == null) {
                throw new Exception('Email tidak boleh kosong');
            }

            if ($password == null) {
                throw new Exception('Password tidak boleh kosong');
            }

            if ($confirmpassword == null) {
                throw new Exception('Konfirmasi password tidak boleh kosong');
            }

            if ($pin == null) {
                throw new Exception('Pin tidak boleh kosong');
            }

            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Format email tidak valid');
            }

            if (!is_numeric($pin)) {
                throw new Exception('Pin harus berupa angka');
            }

            if (strlen($pin) != 6) {
                throw new Exception('Pin harus 6 digit');
            }

            $name = removeSymbol($name);

            $get = $this->msusers->total(array(
                'email' => $email,
                'merchantid' => $this->merchant->id
            ));

            if ($get > 0) {
                throw new Exception('Email sudah terdaftar');
            }

            if ($password != $confirmpassword) {
                throw new Exception('Password tidak sama');
            }

            $insert = array();
            $insert['name'] = $name;
            $insert['email'] = $email;
            $insert['password'] = password_hash($password, PASSWORD_DEFAULT);
            $insert['role'] = 'User';
            $insert['merchantid'] = $this->merchant->id;
            $insert['pin'] = stringEncryption('encrypt', $pin);

            $this->msusers->insert($insert);
            $userid = $this->db->insert_id();

            $merchant = $this->msusers->get(array(
                'id' => $this->merchant->id
            ))->row();

            if ($merchant->isregisterwithoutverification != 1) {
                $smtpemailconfig = json_decode(stringEncryption('decrypt', $this->merchant->smtpemailconfig));

                if ($smtpemailconfig != null) {
                    $sendMail = sendMail($smtpemailconfig->host, $smtpemailconfig->username, $smtpemailconfig->password, $smtpemailconfig->port, $email, $this->merchant->companyname, 'Verifikasi Akun ' . $this->merchant->companyname, viewTemplate($this->merchant->id, 'auth/register/verification', array(
                        'hash' => stringEncryption('encrypt', $userid)
                    ), true));

                    if ($sendMail == false) {
                        log_message_user('error', '[SMTP EMAIL] Gagal mengirimkan kode verifikasi menggunakan akun SMTP anda, Trigger Back menggunakan akun SMTP bawaan Server PPOB & SMM', $this->merchant->id);

                        $sendMail = sendMail(String_Helper::SMTP_HOST, String_Helper::SMTP_USERNAME, String_Helper::SMTP_PASSWORD, String_Helper::SMTP_PORT, $email, $this->merchant->companyname, 'Verifikasi Akun ' . $this->merchant->companyname, viewTemplate($this->merchant->id, 'auth/register/verification', array(
                            'hash' => stringEncryption('encrypt', $userid)
                        ), true));
                    }
                } else {
                    $sendMail = sendMail(String_Helper::SMTP_HOST, String_Helper::SMTP_USERNAME, String_Helper::SMTP_PASSWORD, String_Helper::SMTP_PORT, $email, $this->merchant->companyname, 'Verifikasi Akun ' . $this->merchant->companyname, viewTemplate($this->merchant->id, 'auth/register/verification', array(
                        'hash' => stringEncryption('encrypt', $userid)
                    ), true));
                }

                if ($sendMail == false) {
                    throw new Exception('Gagal mengirimkan kode verifikasi');
                }
            } else {
                $this->msusers->update(array(
                    'id' => $userid
                ), array(
                    'isemailverified' => 1
                ));
            }

            $this->db->trans_commit();

            $this->output->set_status_header(200);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => true,
                'message' => 'Pendaftaran berhasil'
            ));
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => $ex->getMessage()
            ));
        }
    }

    public function forgot_password()
    {
        try {
            $this->db->trans_begin();

            $email = getPost('email');

            if ($email == null) {
                throw new Exception('Email wajib diisi');
            } else if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Email yang anda masukkan tidak valid');
            }

            $get = $this->msusers->get(array(
                'email' => $email,
                'merchantid' => $this->merchant->id,
                'isdeleted' => null
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Email yang anda masukkan tidak terdaftar');
            }

            $row = $get->row();

            $get = $this->forgotpassword->get(array(
                'userid' => $row->id,
            ));

            if ($get->num_rows() > 0) {
                $row = $get->row();

                $tenminutes = date('Y-m-d H:i:s', strtotime("$row->createddate +10 minutes"));

                if ($tenminutes < getCurrentDate()) {
                    throw new Exception('Anda tidak dapat meminta link reset password hingga ' . date('d F Y H:i:s', strtotime($tenminutes)));
                } else {
                    $this->forgotpassword->delete(array(
                        'id' => $row->id
                    ));
                }
            }

            $request = generateTransactionNumber('FORGOT');
            $hash = stringEncryption('encrypt', $request);

            $insert = array();
            $insert['userid'] = $row->id;
            $insert['hash'] = $hash;

            $this->forgotpassword->insert($insert);

            $smtpemailconfig = json_decode(stringEncryption('decrypt', $this->merchant->smtpemailconfig));

            if ($smtpemailconfig != null) {
                $sendMail = sendMail($smtpemailconfig->host, $smtpemailconfig->username, $smtpemailconfig->password, $smtpemailconfig->port, $email, $this->merchant->companyname, 'Reset Password ' . $this->merchant->companyname, viewTemplate($this->merchant->id, 'auth/forgot_password/verification', array(
                    'hash' => $hash
                ), true));

                if ($sendMail == false) {
                    log_message_user('error', '[SMTP EMAIL] Gagal mengirimkan kode verifikasi menggunakan akun SMTP anda, Trigger Back menggunakan akun SMTP bawaan Server PPOB & SMM', $this->merchant->id);

                    $sendMail = sendMail(String_Helper::SMTP_HOST, String_Helper::SMTP_USERNAME, String_Helper::SMTP_PASSWORD, String_Helper::SMTP_PORT, $email, $this->merchant->companyname, 'Reset Password ' . $this->merchant->companyname, viewTemplate($this->merchant->id, 'auth/forgot_password/verification', array(
                        'hash' => $hash
                    ), true));
                }
            } else {
                $sendMail = sendMail(String_Helper::SMTP_HOST, String_Helper::SMTP_USERNAME, String_Helper::SMTP_PASSWORD, String_Helper::SMTP_PORT, $email, $this->merchant->companyname, 'Reset Password ' . $this->merchant->companyname, viewTemplate($this->merchant->id, 'auth/forgot_password/verification', array(
                    'hash' => $hash
                ), true));
            }

            if ($sendMail == false) {
                throw new Exception('Gagal mengirimkan kode verifikasi');
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengirimkan kode verifikasi');
            }

            $this->db->trans_commit();

            $this->output->set_status_header(200);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => true,
                'message' => 'Kode verifikasi telah dikirimkan ke email anda'
            ));
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => $ex->getMessage()
            ));
        }
    }

    private function send_notification($userid, $phonenumber, $message)
    {
        try {
            $apikeys_whatsapp = $this->apikeys_whatsapp->get(array(
                'userid' => $userid,
            ));

            if ($apikeys_whatsapp->num_rows() == 0) {
                return false;
            }

            $row = $apikeys_whatsapp->row();

            $wablasingateway = new WABlasinGateway(stringEncryption('decrypt', $row->apikey), stringEncryption('decrypt', $row->devicecode));
            $phonenumber = changePrefixPhone($phonenumber);

            $send = $wablasingateway->sendMessage($phonenumber, $message);

            if (!isset($send->RESULT) || (isset($send->RESULT) && $send->RESULT != 'OK')) {
                log_message_user('error', '[WHATSAPP SEND MESSAGE] Send notification failed: ' . json_encode($send), $userid);
            }

            return true;
        } catch (Exception $ex) {
            return false;
        }
    }

    public function request_otp()
    {
        $phonenumber = getPost('phonenumber');

        if ($phonenumber == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Nomor WhatsApp tidak boleh kosong'
            ));
        }

        // Format phone number
        $phonenumber = removeSymbol($phonenumber);

        // Ensure phone number is in correct format (add 62 prefix if needed)
        $phonenumber = changePrefixPhone($phonenumber);

        // Validate phone number format
        if (!is_numeric($phonenumber)) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Format nomor WhatsApp tidak valid'
            ));
        }

        // Check if phone number is registered to multiple accounts
        $userCount = $this->msusers->total(array(
            'a.phonenumber' => $phonenumber,
            'a.merchantid' => $this->merchant->id,
        ));

        if ($userCount > 1) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Nomor WhatsApp terdaftar pada lebih dari satu akun. Silahkan gunakan metode login lain.'
            ));
        }

        // Get user by phone number
        $user = $this->msusers->get(array(
            'a.phonenumber' => $phonenumber,
            'a.merchantid' => $this->merchant->id,
        ));

        if ($user->num_rows() == 0) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Nomor WhatsApp tidak terdaftar'
            ));
        }

        $row = $user->row();

        if ($row->isemailverified != 1) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Email belum terverifikasi'
            ));
        }

        if ($row->isdeleted == 1) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Akun anda telah dihapus'
            ));
        }

        // Generate 6-digit OTP
        $otp = sprintf("%06d", mt_rand(1, 999999));

        // Check if there's an existing OTP request in the last 2 minutes
        $existingOtp = $this->mobileotp->get(array(
            'userid' => $row->id,
            'createddate >' => date('Y-m-d H:i:s', strtotime('-2 minutes'))
        ));

        if ($existingOtp->num_rows() > 0) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Mohon tunggu 2 menit sebelum meminta kode OTP baru'
            ));
        }

        // Insert new OTP
        $otpData = array(
            'userid' => $row->id,
            'otp_code' => $otp,
            'createddate' => getCurrentDate(),
        );

        $this->mobileotp->insert($otpData);

        // Send OTP via WhatsApp
        $message = "Kode OTP untuk login ke " . $this->merchant->companyname . " adalah: " . $otp . ". Kode berlaku selama 5 menit.";

        $this->send_notification($this->merchant->id, $phonenumber, $message);

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => true,
            'message' => 'Kode OTP telah dikirim ke nomor WhatsApp Anda',
        ));
    }

    public function verify_otp()
    {
        $phonenumber = getPost('phonenumber');
        $otp = getPost('otp');
        $fcm_token = getPost('fcm_token');

        if ($phonenumber == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Nomor WhatsApp tidak boleh kosong'
            ));
        }

        if ($otp == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Kode OTP tidak boleh kosong'
            ));
        }

        // Format phone number
        $phonenumber = removeSymbol($phonenumber);

        // Ensure phone number is in correct format
        $phonenumber = changePrefixPhone($phonenumber);

        // Check if phone number is registered to multiple accounts
        $userCount = $this->msusers->total(array(
            'a.phonenumber' => $phonenumber,
            'a.merchantid' => $this->merchant->id,
        ));

        if ($userCount > 1) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Nomor WhatsApp terdaftar pada lebih dari satu akun. Silahkan gunakan metode login lain.'
            ));
        }

        // Get user by phone number
        $user = $this->msusers->get(array(
            'a.phonenumber' => $phonenumber,
            'a.merchantid' => $this->merchant->id,
        ));

        if ($user->num_rows() == 0) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Nomor WhatsApp tidak terdaftar'
            ));
        }

        $row = $user->row();

        // Verify OTP
        $otpVerify = $this->mobileotp->get(array(
            'userid' => $row->id,
            'otp_code' => $otp,
        ));

        if ($otpVerify->num_rows() == 0) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Kode OTP tidak valid atau telah kadaluarsa'
            ));
        } else {
            $otpRow = $otpVerify->row();
            $createddate = $otpRow->createddate;
            $fiveMinutesLater = date('Y-m-d H:i:s', strtotime('+5 minutes', strtotime($createddate)));

            if ($fiveMinutesLater < getCurrentDate()) {
                $this->mobileotp->delete(array(
                    'id' => $otpVerify->row()->id
                ));

                $this->output->set_status_header(400);
                $this->output->set_content_type('application/json');

                return JSONResponse(array(
                    'status' => false,
                    'message' => 'Kode OTP tidak valid atau telah kadaluarsa'
                ));
            }
        }

        // Delete the OTP record after successful verification
        $this->mobileotp->delete(array(
            'id' => $otpVerify->row()->id
        ));

        // Create session token
        $data = array();
        $data['userid'] = $row->id;
        $data['token'] = stringEncryption('encrypt', json_encode(array(
            'id' => $row->id,
            'name' => $row->name,
            'role' => $row->role,
            'merchantid' => $row->merchantid,
            'createddate' => getCurrentDate()
        )));
        $data['createddate'] = getCurrentDate();
        $data['createdby'] = $row->id;
        $data['fcm_token'] = $fcm_token;

        $mobileSession = $this->mobilesession->insert($data);

        if (!$mobileSession) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Login gagal'
            ));
        }

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => true,
            'message' => 'Login berhasil',
            'data' => array(
                'token' => $data['token']
            )
        ));
    }

    public function resend_otp()
    {
        $phonenumber = getPost('phonenumber');

        if ($phonenumber == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Nomor WhatsApp tidak boleh kosong'
            ));
        }

        // Format phone number
        $phonenumber = removeSymbol($phonenumber);

        // Ensure phone number is in correct format (add 62 prefix if needed)
        $phonenumber = changePrefixPhone($phonenumber);

        // Validate phone number format
        if (!is_numeric($phonenumber)) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Format nomor WhatsApp tidak valid'
            ));
        }

        // Check if phone number is registered to multiple accounts
        $userCount = $this->msusers->total(array(
            'a.phonenumber' => $phonenumber,
            'a.merchantid' => $this->merchant->id,
        ));

        if ($userCount > 1) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Nomor WhatsApp terdaftar pada lebih dari satu akun. Silahkan gunakan metode login lain.'
            ));
        }

        // Get user by phone number
        $user = $this->msusers->get(array(
            'a.phonenumber' => $phonenumber,
            'a.merchantid' => $this->merchant->id,
        ));

        if ($user->num_rows() == 0) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Nomor WhatsApp tidak terdaftar'
            ));
        }

        $row = $user->row();

        if ($row->isemailverified != 1) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Email belum terverifikasi'
            ));
        }

        if ($row->isdeleted == 1) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Akun anda telah dihapus'
            ));
        }

        // Check if there's an existing OTP request in the last 2 minutes
        $existingOtp = $this->mobileotp->get(array(
            'userid' => $row->id,
            'createddate >' => date('Y-m-d H:i:s', strtotime('-2 minutes'))
        ));

        if ($existingOtp->num_rows() > 0) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Mohon tunggu 2 menit sebelum meminta kode OTP baru'
            ));
        }

        // Delete any existing OTP for this user
        $this->mobileotp->delete(array(
            'userid' => $row->id
        ));

        // Generate 6-digit OTP
        $otp = sprintf("%06d", mt_rand(1, 999999));

        // Insert new OTP
        $otpData = array(
            'userid' => $row->id,
            'otp_code' => $otp,
            'createddate' => getCurrentDate(),
        );

        $this->mobileotp->insert($otpData);

        // Send OTP via WhatsApp
        $message = "Kode OTP untuk login ke " . $this->merchant->companyname . " adalah: " . $otp . ". Kode berlaku selama 5 menit.";

        $this->send_notification($this->merchant->id, $phonenumber, $message);

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => true,
            'message' => 'Kode OTP telah dikirim ulang ke nomor WhatsApp Anda',
        ));
    }
}
