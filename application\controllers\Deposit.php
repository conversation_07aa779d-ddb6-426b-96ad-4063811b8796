<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsUsers $users
 * @property Deposits $deposits
 */
class Deposit extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Deposits', 'deposits');
        $this->load->model('MsUsers', 'users');
    }

    public function topup()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Topup';
        $data['content'] = 'deposit/topup';

        return $this->load->view('master', $data);
    }

    public function payment_gateway()
    {
        return array(
            'bni_va',
            'bri_va',
            'echannel',
            'permata_va'
        );
    }

    public function payment_gateway_alias()
    {
        return array(
            'bni_va' => 'Bank BNI',
            'bri_va' => 'Bank BRI',
            'echannel' => 'Bank MANDIRI',
            'permata_va' => 'Bank PERMATA'
        );
    }

    public function process_topup()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $payment = getPost('payment');
            $nominal = getPost('nominal');
            $nominal = str_replace(',', '', $nominal);
            $nominal = str_replace('.', '', $nominal);

            if ($payment == null) {
                throw new Exception('Pembayaran wajib diisi');
            } else if ($nominal == null) {
                throw new Exception('Nominal wajib diisi');
            } else if ($payment != 'Bank BCA') {
                if (!in_array($payment, $this->payment_gateway())) {
                    throw new Exception('Pembayaran tidak ditemukan');
                }
            } else if (!is_numeric($nominal)) {
                throw new Exception('Nominal harus berupa angka');
            } else if ($nominal < 10000) {
                throw new Exception('Minimal deposit Rp 10.000');
            } else if ($nominal > 5000000) {
                throw new Exception('Maksimal deposit Rp 5.000.000');
            }

            $spam = $this->deposits->total(array(
                'userid' => getCurrentIdUser(),
                'status' => 'Pending'
            ));

            if ($spam >= 2) {
                throw new Exception('Terdapat 2 permintaan topup yang masih pending, Silahkan selesaikan pembayaran terlebih dahulu');
            }

            $uniqueadmin = rand(100, 999);

            $insert = array();
            $insert['userid'] = getCurrentIdUser();
            $insert['code'] = generateTransactionNumber('DEPOSIT');
            $insert['payment'] = $payment;
            $insert['nominal'] = $nominal + $uniqueadmin;
            $insert['uniqueadmin'] = $uniqueadmin;
            $insert['status'] = 'Pending';
            $insert['depositplatform'] = 'web';

            if (!in_array($payment, $this->payment_gateway())) {
                $insert['note'] = "Silahkan transfer sebesar Rp " . IDR($insert['nominal']) . ",- Ke Rekening: BCA, no. 4400190840, a.n. MOCH ARIZAL FAUZI. Batas waktu transfer 24 jam";
            }

            $insert['paymenttype'] = 'Otomatis';

            $this->deposits->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal melakukan permintaan deposit');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Permintaan topup berhasil');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function history()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        }

        $status = $this->deposits->select('a.status')
            ->where(array(
                'a.userid' => getCurrentIdUser(),
                'a.merchantid' => null
            ))
            ->group_by('a.status')
            ->order_by('a.status', 'ASC')
            ->get()
            ->result();

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Riwayat';
        $data['content'] = 'deposit/history';
        $data['status'] = $status;

        return $this->load->view('master', $data);
    }

    public function datatables_history()
    {
        try {
            if (isLogin() && isUser()) {
                $data = array();
                $date = getPost('date');
                $depositcode = getPost('depositcode');
                $statusfilter = getPost('status');

                $datatable = $this->datatables->make('Deposits', 'QueryDatatables', 'SearchDatatables');

                $where = array(
                    'a.userid' => getCurrentIdUser(),
                    'a.merchantid' => null
                );

                if ($date != null && isset(explode(' - ', $date)[0]) && isset(explode(' - ', $date)[1])) {
                    $startdate = explode(' - ', $date)[0];
                    $startdate = date('Y-m-d', strtotime($startdate));

                    $enddate = explode(' - ', $date)[1];
                    $enddate = date('Y-m-d', strtotime($enddate));

                    $where['DATE(a.createddate) >='] = $startdate;
                    $where['DATE(a.createddate) <='] = $enddate;
                } else {
                    $where['DATE(a.createddate) >='] = date('Y-m-01');
                    $where['DATE(a.createddate) <='] = date('Y-m-t');
                }

                if ($depositcode != null) {
                    $where['a.code'] = $depositcode;
                }

                if ($statusfilter != null) {
                    $where['a.status'] = $statusfilter;
                }


                foreach ($datatable->getData($where) as $key => $value) {
                    $detail = array();

                    if ($value->status == 'Pending') {
                        $status = "<span class=\"badge badge-warning\">Menunggu</span>";
                    } else if ($value->status == 'Success' || $value->status == 'Paid' || $value->status == 'settlement') {
                        $status = "<span class=\"badge badge-success\">Berhasil</span>";
                    } else {
                        $status = "<span class=\"badge badge-danger\">Gagal</span>";
                    }

                    $actions = "";

                    if ($value->status == 'Pending') {
                        if (in_array($value->payment, $this->payment_gateway()) && isUser()) {
                            $actions .= "<a href=\"" . base_url('deposit/history/pay/' . stringEncryption('encrypt', $value->id)) . "\" class=\"btn btn-primary btn-sm mb-1 me-1\">
                                    <i class=\"fa fa-credit-card\"></i>
                                    <span>Bayar</span>
                                </a>";
                        }

                        $actions .= "<button type=\"button\" class=\"btn btn-danger btn-sm mb-1 me-1\" onclick=\"cancelTopup('" . stringEncryption('encrypt', $value->id) . "')\">
                                    <i class=\"fa fa-close\"></i>
                                    <span>Cancel</span>
                                </button>";
                    }

                    if ($actions == "") {
                        $actions = "N/A";
                    }

                    $detail[] = DateFormat($value->createddate, 'd F Y H:i:s');
                    $detail[] = $value->code;
                    $detail[] = in_array($value->payment, $this->payment_gateway()) ? $this->payment_gateway_alias()[$value->payment] : $value->payment;
                    $detail[] = IDR($value->nominal);
                    $detail[] = $value->note ?? '-';
                    $detail[] = $status;
                    $detail[] = $actions;

                    $data[] = $detail;
                }

                return $datatable->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_cancel()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if (!isUser()) {
                throw new Exception('Anda tidak dapat mengakses fitur ini');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Permintaan topup tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->deposits->select('a.*')
                ->join('msusers b', 'b.id = a.userid')
                ->get(array(
                    'a.id' => $id,
                    'a.userid' => getCurrentIdUser(),
                    'a.merchantid' => null,
                    'a.status' => 'Pending'
                ));

            if ($get->num_rows() == 0) {
                throw new Exception('Permintaan topup tidak ditemukan');
            }

            $update = array();
            $update['status'] = 'Cancel';

            $this->deposits->update(array(
                'id' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal membatalkan permintaan deposit');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Permintaan topup dibatalkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function pay($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                $this->db->trans_rollback();

                return redirect(base_url('auth/login'));
            } else if (!isUser()) {
                $this->db->trans_rollback();

                return redirect(base_url('dashboard'));
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->deposits->select('a.*')
                ->join('msusers b', 'b.id = a.userid')
                ->get(array(
                    'a.id' => $id,
                    'a.userid' => getCurrentIdUser(),
                    'a.merchantid' => null,
                    'a.status' => 'Pending'
                ));

            if ($get->num_rows() == 0) {
                $this->db->trans_rollback();

                return redirect(base_url('deposit/history'));
            }

            $row = $get->row();

            if ($row->checkouturl != null) {
                $this->db->trans_rollback();

                return redirect($row->checkouturl);
            }

            $params = array(
                'transaction_details' => array(
                    'order_id' => $row->code,
                    'gross_amount' => $row->nominal
                ),
                'enabled_payments' => array($row->payment),
                'callbacks' => array(
                    'finish' => base_url('deposit/history/pay/finish/' . stringEncryption('encrypt', $row->id))
                )
            );

            $paymentUrl = Midtrans\Snap::createTransaction($params)->redirect_url;

            $update = array();
            $update['checkouturl'] = $paymentUrl;

            $this->deposits->update(array(
                'id' => $row->id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                $this->db->trans_rollback();

                return redirect(base_url('deposit/history?error=Terjadi kesalahan saat memproses pembayaran'));
            }

            $this->db->trans_commit();

            return redirect($paymentUrl);
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return redirect(base_url('deposit/history?error=' . $ex->getMessage()));
        }
    }

    public function finish($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                $this->db->trans_rollback();

                return redirect(base_url('auth/login'));
            } else if (!isUser()) {
                $this->db->trans_rollback();

                return redirect(base_url('dashboard'));
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->deposits->select('a.*')
                ->join('msusers b', 'b.id = a.userid')
                ->get(array(
                    'a.id' => $id,
                    'a.userid' => getCurrentIdUser(),
                    'a.merchantid' => null,
                    'a.status' => 'Pending'
                ));

            if ($get->num_rows() == 0) {
                $this->db->trans_rollback();

                return redirect(base_url('deposit/history'));
            }

            $row = $get->row();

            $status = (object)Midtrans\Transaction::status($row->code);

            if ($status->transaction_status == 'settlement') {
                $currentbalance = getCurrentBalance($row->userid, true);

                $updateUser = array();
                $updateUser['balance'] = $currentbalance + $row->nominal;

                $this->users->update(array(
                    'id' => $row->userid
                ), $updateUser);

                $update = array();
                $update['status'] = $status->transaction_status;

                $this->deposits->update(array(
                    'id' => $row->id
                ), $update);
            }

            if ($this->db->trans_status() === FALSE) {
                $this->db->trans_rollback();

                return redirect(base_url('deposit/history?error=Terjadi kesalahan saat memproses pembayaran'));
            }

            $this->db->trans_commit();

            return redirect(base_url('deposit/history'));
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return redirect(base_url('deposit/history?error=' . $ex->getMessage()));
        }
    }
}
