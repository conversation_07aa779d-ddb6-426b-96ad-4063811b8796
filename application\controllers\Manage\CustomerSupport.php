<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property MsCustomerSupport $customersupport
 * @property CI_Upload $upload
 * @property Datatables $datatables
 * @property CI_DB_query_builder $db
 */
class CustomerSupport extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsCustomerSupport', 'customersupport');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Kontak';
        $data['content'] = 'manage/customersupport/index';

        return $this->load->view('master', $data);
    }

    public function add()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Tambah Kontak';
        $data['content'] = 'manage/customersupport/add';

        return $this->load->view('master', $data);
    }

    public function process_add_customersupport()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda belum login!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $name = getPost('name');
            $icon = isset($_FILES['icon']) ? $_FILES['icon'] : null;
            $link = getPost('link');

            if (empty(strlen($name))) {
                throw new Exception('Nama tidak boleh kosong.');
            } else if ($icon['size'] == 0) {
                throw new Exception('Gambar tidak boleh kosong.');
            } else if (empty(strlen($link))) {
                throw new Exception('Link tidak boleh kosong.');
            }

            $config = array(
                'upload_path' => './uploads/',
                'allowed_types' => 'jpg|jpeg|png',
                'encrypt_name' => true,
            );

            $this->load->library('upload', $config);

            if (!$this->upload->do_upload('icon')) {
                throw new Exception($this->upload->display_errors('', ''));
            } else {
                $icon = $this->upload->data();

                $data = array(
                    'name' => $name,
                    'link' => $link,
                    'icon' => $icon['file_name'],
                    'createddate' => getCurrentDate(),
                    'createdby' => getCurrentIdUser(),
                );

                $this->customersupport->insert($data);

                if ($this->db->trans_status() === FALSE) {
                    throw new Exception('Gagal menambahkan kontak!');
                }

                $this->db->trans_commit();

                return JSONResponseDefault('OK', 'Kontak berhasil ditambahkan!');
            }
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function datatables_customersupport()
    {
        try {
            if (isLogin() && isUser() && getCurrentUser()->licenseid != null) {
                $data = array();

                $datatables = $this->datatables->make('MsCustomerSupport', 'QueryDatatables', 'SearchDatatables');

                foreach (
                    $datatables->getData(array(
                        'createdby' => getCurrentIdUser()
                    )) as $key => $value
                ) {
                    $detail = array();
                    $detail[] = "<img src=\"" . base_url('uploads/' . $value->icon) . "\" width=\"25\">";
                    $detail[] = $value->name;
                    $detail[] = $value->link;
                    $detail[] = "<a href=\"" . base_url('manage/customersupport/edit/' . $value->id) . "\") class=\"btn btn-icon btn-warning btn-sm mb-1\">
                        <i class=\"fa fa-edit\"></i>
                    </a>

                    <a href=\"javascript:;\" class=\"btn btn-icon btn-danger btn-sm  mb-1\" onclick=\"deleteCustomerSupport('" . stringEncryption('encrypt', $value->id) . "')\">
                        <i class=\"fa fa-trash\"></i>
                    </a>";

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Anda tidak memiliki akses!');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function edit($id)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $get = $this->customersupport->get(array(
            'id = ' => $id,
            'createdby' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('manage/customersupport'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Ubah Kontak';
        $data['content'] = 'manage/customersupport/edit';
        $data['customersupport'] = $get->row();

        return $this->load->view('master', $data);
    }

    public function process_edit_customersupport($id)
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda belum login!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $get = $this->customersupport->get(array(
                'id' => $id,
                'createdby' => getCurrentIdUser()
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Kontak tidak ditemukan!');
            }

            $row = $get->row();

            $name = getPost('name');
            $icon = isset($_FILES['icon']) ? $_FILES['icon'] : null;
            $link = getPost('link');

            if (empty(strlen($name))) {
                throw new Exception('Nama tidak boleh kosong.');
            } else if (empty(strlen($link))) {
                throw new Exception('Link tidak boleh kosong.');
            }

            if ($icon['size'] > 0) {
                $config = array(
                    'upload_path' => './uploads/',
                    'allowed_types' => 'jpg|jpeg|png',
                    'encrypt_name' => true,
                );

                $this->load->library('upload', $config);

                $update = array();
                if ($this->upload->do_upload('icon')) {;
                    $icon = $this->upload->data();
                    $update['icon'] = $icon['file_name'];

                    if (file_exists('./uploads/' . $row->icon)) {
                        @unlink('./uploads/' . $row->icon);
                    }
                }
            }

            $update['name'] = $name;
            $update['link'] = $link;
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $this->customersupport->update(array(
                'id = ' => $id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal mengubah kontak!');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Kontak berhasil diubah!');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_delete_customersupport()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Anda belum login!');
            } else if (!isUser()) {
                throw new Exception('Anda tidak memiliki akses!');
            } else if (getCurrentUser()->licenseid == null) {
                throw new Exception('Anda tidak memiliki akses!');
            }

            $id = stringEncryption('decrypt', getPost('id'));

            $customersupport = $this->customersupport->get(array(
                'id' => $id,
                'createdby' => getCurrentIdUser()
            ));

            if ($customersupport->num_rows() == 0) {
                throw new Exception('Kontak tidak ditemukan!');
            }

            $row = $customersupport->row();

            if (file_exists('./uploads/' . $row->icon)) {
                @unlink('./uploads/' . $row->icon);
            }

            $this->customersupport->delete(array(
                'id' => $id
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menghapus kontak!');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Kontak berhasil dihapus!');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
