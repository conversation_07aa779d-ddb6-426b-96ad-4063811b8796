<?php

use <PERSON>reait\Firebase\Exception\Messaging\InvalidMessage;
use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification;

defined('BASEPATH') or die('No direct script access allowed!');

function JSONResponse($data = array(), $flags = 0)
{
    echo json_encode($data, $flags);
}

function JSONResponseDefault($result, $message)
{
    return JSONResponse(array(
        'RESULT' => $result,
        'MESSAGE' => $message
    ));
}

function getPost($index, $default = null)
{
    $CI = &get_instance();

    if ($CI->input->post($index)) {
        if (is_string($CI->input->post($index))) {
            return trim($CI->input->post($index));
        } else {
            return $CI->input->post($index);
        }
    }

    return $default;
}

function getGet($index, $default = null)
{
    $CI = &get_instance();

    if ($CI->input->get($index)) {
        return $CI->input->get($index);
    }

    return $default;
}

function getSessionValue($index)
{
    $CI = &get_instance();

    return $CI->session->userdata($index);
}

function setSessionValue($index)
{
    $CI = &get_instance();

    return $CI->session->set_userdata($index);
}

function hasSessionValue($index)
{
    $CI = &get_instance();

    return $CI->session->has_userdata($index);
}

function unsetSessionValue($index)
{
    $CI = &get_instance();

    return $CI->session->unset_userdata($index);
}

function destroySession()
{
    $CI = &get_instance();

    return $CI->session->sess_destroy();
}

function isLogin()
{
    return getSessionValue('ISLOGIN');
}

function isUser()
{
    return getSessionValue('ROLE') == 'User';
}

function isAdmin()
{
    return getSessionValue('ROLE') == 'Admin';
}

function getCurrentDate($format = 'Y-m-d H:i:s')
{
    return date($format);
}

function getCurrentIdUser()
{
    return getSessionValue('USERID');
}

function getMerchantId()
{
    $CI = &get_instance();

    $CI->db->select('a.id, a.merchantid')
        ->from('msusers a')
        ->where('a.id', getSessionValue('USERID'));

    $get = $CI->db->get()
        ->row();

    return $get->merchantid;
}

function getReferralCode()
{
    $CI = &get_instance();

    $get = $CI->db->select('a.referralcode')
        ->from('msusers a')
        ->where('a.id', getCurrentIdUser())
        ->get()
        ->row();

    return $get->referralcode;
}

function getMerchantDomain($userid)
{
    $CI = &get_instance();

    $merchant = $CI->db->select('domain')
        ->get_where('msusers', array('id' => $userid))
        ->row();

    if ($merchant && $merchant->domain) {
        // Check if domain has protocol
        if (strpos($merchant->domain, 'http') === 0) {
            return rtrim($merchant->domain, '/') . '/';
        } else {
            // Add https protocol by default
            return 'https://' . rtrim($merchant->domain, '/') . '/';
        }
    }

    // Fallback to live/demo URL structure
    return 'https://server-ppobsmm.com/live/demo/';
}

function transactionFeature()
{
    return array(
        'transaction/prabayar',
        'transaction/pascabayar',
        'transaction/smm'
    );
}

function depositFeature()
{
    return array(
        'deposit/topup',
        'deposit/history'
    );
}

function paymentGatewayFeature()
{
    return array(
        'paymentgateway/manual',
        'paymentgateway/automatic'
    );
}

function usersFeature()
{
    return array(
        'users/data',
        'users/data/deleted',
        'users/transfer/saldo',
        'users/deposit/history',
        'users/ticket/history',
        'users/verificationkyc',
        'admins/deposit/history',
        'admins/transfer/saldo',
        'manage/member',
        'manage/historyrequestuploadplaystore',
        'admins/invoice/history',
    );
}

function productFeature()
{
    return array(
        'database/stockproduct',
        'product/prabayar',
        'product/prabayar/add',
        'product/pascabayar',
        'product/smm',
        'product/smm/add',
        'product/information'
    );
}

function managementFeature()
{
    return array(
        'manage/addpages',
        'manage/addpages/add',
        'manage/category/product',
        'manage/news',
        'manage/vendor',
        'manage/privacypolicy',
        'manage/termsofservice',
        'manage/slider',
        'manage/icons',
        'manage/role',
        'manage/platformsosmed',
        'manage/livechat',
        'manage/historytopup',
        'manage/customapp',
        'manage/faq',
        'manage/customersupport',
        'manage/onvayaacademy',
        'manage/prefixoperator',
        'manage/broadcastemailmarketing',
        'manage/broadcastemailmarketing/add'
    );
}

function adminOnlyFeature()
{
    return array(
        'manage/onvayaacademy',
        'manage/onvayaacademy/add',
        'manage/onvayaacademy/edit',
        'manage/onvayaacademy/datatables',
        'manage/onvayaacademy/delete'
    );
}

function academyFeature()
{
    return array(
        'academy',
        'academy/detail'
    );
}

function reportFeature()
{
    return array(
        'report/profit/monthly'
    );
}

function accountFeature()
{
    return array(
        'settings/account',
        'settings/password',
        'settings/seo',
        'settings/apikey',
        'settings/margins',
        'settings/domain',
        'settings/theme',
        'settings/smtp',
        'settings/logmessage',
        'settings/struk',
        'referral'
    );
}

function stringEncryption($action, $string)
{
    $output = false;

    $encrypt_method = 'AES-256-CBC'; // Default
    $secret_key = 'karpeldedvtech'; // Change the key!
    $secret_iv = 'owr216he890';  // Change the init vector!

    // hash
    $key = hash('sha256', $secret_key);

    // iv - encrypt method AES-256-CBC expects 16 bytes - else you will get a warning
    $iv = substr(hash('sha256', $secret_iv), 0, 16);

    if ($action == 'encrypt') {
        $output = openssl_encrypt($string ?? '', $encrypt_method, $key, 0, $iv);
        $output = base64_encode($output);
    } else if ($action == 'decrypt') {
        $output = openssl_decrypt(base64_decode($string ?? ''), $encrypt_method, $key, 0, $iv);
    }

    return $output;
}

function getCurrentVendor($category, $userid = null)
{
    $CI = &get_instance();

    $get = $CI->db->get_where('apikeys', array(
        'userid' => $userid == null ? getCurrentIdUser() : $userid,
        'category' => $category
    ))->row();

    return $get != null ? $get->vendor : null;
}

function IDR($nominal, $digit = 0, $pemisah = '.', $rupiah = ',')
{
    return number_format($nominal ?? 0, $digit, $pemisah, $rupiah);
}

function getCurrentUser($userid = null, $lock = false)
{
    $CI = &get_instance();

    $CI->db->select('a.*, b.name as licensename', false);
    $CI->db->from('msusers a');
    $CI->db->join('mslicense b', 'b.id = a.licenseid', 'LEFT');
    $CI->db->where('a.id', $userid === null ? getCurrentIdUser() : $userid);

    if ($lock) {
        $query = $CI->db->get_compiled_select();
        $query .= " FOR UPDATE";
        return $CI->db->query($query)->row();
    }

    // Jika tidak menggunakan locking
    return $CI->db->get()->row();
}


function getCurrentBalance($userid = null, $lock = false)
{
    return getCurrentUser($userid, $lock)->balance;
}

function generateTransactionNumber($prefix)
{
    $user_id = null;

    $random_suffix = rand(1000, 9999);
    $seven_digits = str_pad($random_suffix, 5, "0", STR_PAD_LEFT);

    if (isLogin()) {
        $user_id = getCurrentIdUser();

        return $prefix . '-' . getCurrentDate('YmdHis') . '-' . $seven_digits . '-' . $user_id;
    } else {
        return $prefix . '-' . getCurrentDate('YmdHis') . '-' . $seven_digits;
    }
}

function password_strength_check($password, $min_len = 8, $max_len = 70, $req_digit = 1, $req_lower = 1, $req_upper = 1, $req_symbol = 1)
{
    // Build regex string depending on requirements for the password
    $regex = '/^';

    if ($req_digit == 1) {
        $regex .= '(?=.*\d)'; // Match at least 1 digit
    }

    if ($req_lower == 1) {
        $regex .= '(?=.*[a-z])'; // Match at least 1 lowercase letter
    }

    if ($req_upper == 1) {
        $regex .= '(?=.*[A-Z])'; // Match at least 1 uppercase letter
    }

    if ($req_symbol == 1) {
        $regex .= '(?=.*[^a-zA-Z\d])'; // Match at least 1 character that is none of the above
    }

    $regex .= '.{' . $min_len . ',' . $max_len . '}$/';

    if (preg_match($regex, $password)) {
        return TRUE;
    } else {
        return FALSE;
    }
}

function remove_emoji($text)
{
    $text = iconv('UTF-8', 'ISO-8859-15//IGNORE', $text);
    $text = preg_replace('/\s+/', ' ', $text);
    return iconv('ISO-8859-15', 'UTF-8', $text);
}

function removeSymbol($value)
{
    $value = stripslashes($value ?? '');
    $value = htmlspecialchars($value);
    $value = remove_emoji($value);

    return $value;
}

function DateFormat($date, $format = null)
{
    if ($format == null) {
        $format = 'd/m/Y';
    }

    $date = str_replace('/', '-', $date ?? '');

    return date($format, strtotime($date));
}

function getProfitProduct($category, $price, $userid = null)
{
    $CI = &get_instance();
    $userid = $userid == null ? getCurrentIdUser() : $userid;

    $min_price = $CI->db->select('minprice, maxprice, profit, margintype')
        ->order_by('minprice', 'ASC')
        ->get_where('msprofit', array(
            'userid' => $userid,
            'category' => $category
        ))->row();

    $margintype = "Nominal";
    if ($min_price != null) {
        $margintype = $min_price->margintype;
        $profit = $min_price->profit;
        $max_price = $min_price->maxprice;
        $min_price = $min_price->minprice;
    } else {
        $profit = 0;
        $min_price = 0;
        $max_price = 0;
    }

    if ($price > $max_price) {
        $profit_get = $CI->db->select('profit, margintype')
            ->get_where('msprofit', array(
                'minprice <' => (int)$price,
                'maxprice >' => (int)$price,
                'userid' => $userid,
                'category' => $category
            ))->row();

        if ($profit_get != null) {
            $margintype = $profit_get->margintype;
            $profit = $profit_get->profit;
        } else {
            $profit = 0;
        }
    } else {
        $profit = 0;
    }

    return array(
        'profit' => $profit,
        'margintype' => $margintype
    );
}

function getPriceWithProfit($category, $price, $userid = null)
{
    $profit = getProfitProduct($category, $price, $userid);

    if ($profit['margintype'] == 'Nominal') {
        $price = $profit['profit'];
    } else if ($profit['margintype'] == 'Persentase') {
        $price = $price * ($profit['profit'] / 100);
    }

    return $price;
}

function get_setting_web($configname)
{
    $CI = &get_instance();

    $get = $CI->db->select('configvalue')
        ->from('configs')
        ->where('configname', $configname)
        ->get()
        ->row();

    if ($get != null) {
        return $get->configvalue;
    }

    return false;
}

function getCurrentAPIKeys($category, $userid = null)
{
    $CI = &get_instance();
    $userid = $userid == null ? getCurrentIdUser() : $userid;

    $get = $CI->db->get_where('apikeys', array(
        'userid' => $userid,
        'category' => $category
    ))->row();

    return $get;
}

function getPriceWithProfitV2($category, $price, $profits = array())
{
    foreach ($profits as $key => $value) {
        if ($key != $category) continue;

        foreach ($value as $k => $v) {
            if ($price >= $v['minprice'] && $v['maxprice'] >= $price) {
                if ($v['margintype'] == 'Nominal') {
                    $price = $v['profit'];
                } else if ($v['margintype'] == 'Persentase') {
                    $price = $price * ($v['profit'] / 100);
                }

                return $price;
            }
        }
    }

    return 0;
}

function syncProduct($vendor, $category, $usercode, $apikey, $secretkey, $currencyrate, $userid = null, $public_key = null, $base_url = null, $dontadd_product = false)
{
    $do_checkproduct = true;

    $CI = &get_instance();

    $services = $CI->msproduct->select('a.category, a.type, a.id, a.code, a.productname, a.price, a.admin, a.status, a.dontupdate, a.dontupdatecategory, a.oldcategoryname')
        ->result(array(
            'userid' => $userid,
            'vendor' => $vendor,
            'category_apikey' => $category
        ));

    $log = $CI->productpricelog->select('a.id, a.type, a.productid, a.oldprice, a.newprice')
        ->result(array(
            'userid' => $userid,
            'DATE(createddate) =' => getCurrentDate('Y-m-d')
        ));

    $profit = $CI->db->select('category, minprice, maxprice, profit, margintype')
        ->from('msprofit')
        ->where('userid', $userid)
        ->order_by('minprice', 'ASC')
        ->get()
        ->result();

    $profits = array();
    foreach ($profit as $key => $value) {
        $profits[$value->category][] = array(
            'minprice' => $value->minprice,
            'maxprice' => $value->maxprice,
            'profit' => $value->profit,
            'margintype' => $value->margintype
        );
    }

    $old_services = array();
    $history_log = array();
    $updated_services = array();
    $dont_updatecategory = array();

    foreach ($services as $key => $value) {
        $old_services[$value->code] = array(
            'productid' => $value->id,
            'productname' => $value->productname,
            'price' => $value->price,
            'admin' => $value->admin,
            'status' => $value->status,
            'dontupdate' => $value->dontupdate,
            'dontupdatecategory' => $value->dontupdatecategory,
            'type' => $value->type,
            'description_null' => empty($value->description),
            'category' => $value->category,
            'oldcategoryname' => $value->oldcategoryname
        );

        if (!empty($value->oldcategoryname) && !array_key_exists($value->oldcategoryname, $dont_updatecategory)) {
            $dont_updatecategory[$value->oldcategoryname] = array(
                'category' => $value->category
            );
        }
    }

    foreach ($log as $key => $value) {
        $history_log[$value->productid] = array(
            'logid' => $value->id,
            'type' => $value->type,
            'oldprice' => $value->oldprice,
            'newprice' => $value->newprice
        );
    }

    $insert_batch = array();
    $insert_log = array();
    $update_log = array();
    $update_batch = array();

    if ($category == 'PPOB') {
        if ($vendor == 'Digiflazz') {
            $digiflazz = new Digiflazz(stringEncryption('decrypt', $usercode), stringEncryption('decrypt', $apikey));
            $prepaid = $digiflazz->price_list('prepaid');

            if (isset($prepaid->data) && !isset($prepaid->data->rc)) {
                foreach ($prepaid->data as $key => $value) {
                    $profit = getPriceWithProfitV2('Prabayar', $value->price, $profits);
                    $price = $value->price + $profit;

                    if (!array_key_exists($value->buyer_sku_code, $old_services)) {
                        $insert = array();
                        $insert['userid'] = $userid;
                        $insert['vendor'] = $vendor;
                        $insert['code'] = $value->buyer_sku_code;
                        $insert['productname'] = $value->product_name;
                        $insert['description'] = $value->desc;
                        $insert['status'] = ($value->buyer_product_status == 1 && $value->seller_product_status == 1);
                        $insert['price'] = $price;
                        $insert['admin'] = 0;
                        $insert['vendorprice'] = $value->price;
                        $insert['profit'] = $profit;
                        $insert['commission'] = 0;

                        if (!array_key_exists($value->category, $dont_updatecategory)) {
                            $insert['category'] = $value->category;
                            $insert['oldcategoryname'] = null;
                            $insert['dontupdatecategory'] = null;
                        } else {
                            $insert['category'] = $dont_updatecategory[$value->category]['category'];
                            $insert['oldcategoryname'] = $value->category;
                            $insert['dontupdatecategory'] = 1;
                        }

                        $insert['brand'] = $value->brand;
                        $insert['category_apikey'] = $category;
                        $insert['subcategory_apikey'] = 'PRABAYAR';
                        $insert['type'] = $value->type;
                        $insert['createddate'] = getCurrentDate();

                        $insert_batch[] = $insert;
                    } else {
                        $status = ($value->buyer_product_status == 1 && $value->seller_product_status == 1);

                        if (
                            (ceil($price) != $old_services[$value->buyer_sku_code]['price'] && floor($price) != $old_services[$value->buyer_sku_code]['price'])
                            || $value->product_name != $old_services[$value->buyer_sku_code]['productname']
                            || $status != $old_services[$value->buyer_sku_code]['status']
                            || $value->type != $old_services[$value->buyer_sku_code]['type']
                            || ($old_services[$value->buyer_sku_code]['oldcategoryname'] == null && array_key_exists($value->category, $dont_updatecategory))
                        ) {
                            if (ceil($price) != $old_services[$value->buyer_sku_code]['price'] && floor($price) != $old_services[$value->buyer_sku_code]['price']) {
                                if ($price > $old_services[$value->buyer_sku_code]['price']) {
                                    $type = 'UP';
                                } else {
                                    $type = 'DOWN';
                                }

                                if (!array_key_exists($old_services[$value->buyer_sku_code]['productid'], $history_log)) {
                                    $insert = array();
                                    $insert['userid'] = $userid;
                                    $insert['productid'] = $old_services[$value->buyer_sku_code]['productid'];
                                    $insert['type'] = $type;
                                    $insert['oldprice'] = $old_services[$value->buyer_sku_code]['price'];
                                    $insert['newprice'] = $price;
                                    $insert['createddate'] = getCurrentDate();

                                    $insert_log[] = $insert;
                                } else {
                                    $update = array();
                                    $update['id'] = $history_log[$old_services[$value->buyer_sku_code]['productid']]['logid'];
                                    $update['type'] = $type;
                                    $update['oldprice'] = $old_services[$value->buyer_sku_code]['price'];
                                    $update['newprice'] = $price;
                                    $update['updateddate'] = getCurrentDate();

                                    $update_log[] = $update;
                                }
                            }

                            $update = array();
                            $update['id'] = $old_services[$value->buyer_sku_code]['productid'];
                            if ($old_services[$value->buyer_sku_code]['dontupdate'] == null || $old_services[$value->buyer_sku_code]['dontupdate'] == 0) {
                                $update['productname'] = $value->product_name;
                                $update['status'] = ($value->buyer_product_status == 1 && $value->seller_product_status == 1);
                                $update['admin'] = 0;
                                $update['price'] = $price;
                                $update['profit'] = $profit;
                                $update['commission'] = 0;
                            }
                            $update['vendorprice'] = $value->price;

                            if (!array_key_exists($value->category, $dont_updatecategory)) {
                                $update['category'] = $value->category;
                            } else {
                                $update['category'] = $dont_updatecategory[$value->category]['category'];
                                $update['oldcategoryname'] = $value->category;
                                $update['dontupdatecategory'] = 1;
                            }

                            $update['brand'] = $value->brand;
                            $update['updateddate'] = getCurrentDate();
                            $update['type'] = $value->type;

                            $update_batch[] = $update;
                        }
                    }

                    $updated_services[$value->buyer_sku_code] = $value;
                }
            } else {
                $do_checkproduct = false;
            }

            $pasca = $digiflazz->price_list('pasca');

            if (isset($pasca->data) && !isset($pasca->data->rc)) {
                foreach ($pasca->data as $key => $value) {
                    $profit = getPriceWithProfitV2('Pascabayar', $value->admin, $profits);
                    $price = $value->admin + $profit;

                    if (!array_key_exists($value->buyer_sku_code, $old_services)) {
                        $insert = array();
                        $insert['userid'] = $userid;
                        $insert['vendor'] = $vendor;
                        $insert['code'] = $value->buyer_sku_code;
                        $insert['productname'] = $value->product_name;
                        $insert['description'] = $value->desc;
                        $insert['status'] = ($value->buyer_product_status == 1 && $value->seller_product_status == 1);
                        $insert['price'] = 0;
                        $insert['admin'] = $price;
                        $insert['vendorprice'] = $value->admin;
                        $insert['profit'] = $profit;
                        $insert['commission'] = $value->commission + $profit;

                        if (!array_key_exists($value->category, $dont_updatecategory)) {
                            $insert['category'] = $value->category;
                            $insert['oldcategoryname'] = null;
                            $insert['dontupdatecategory'] = null;
                        } else {
                            $insert['category'] = $dont_updatecategory[$value->category]['category'];
                            $insert['oldcategoryname'] = $value->category;
                            $insert['dontupdatecategory'] = 1;
                        }

                        $insert['brand'] = $value->brand;
                        $insert['category_apikey'] = $category;
                        $insert['subcategory_apikey'] = 'PASCABAYAR';
                        $insert['type'] = null;
                        $insert['createddate'] = getCurrentDate();

                        $insert_batch[] = $insert;
                    } else {
                        $status = ($value->buyer_product_status == 1 && $value->seller_product_status == 1);

                        if (
                            (ceil($price) != $old_services[$value->buyer_sku_code]['admin'] && floor($price) != $old_services[$value->buyer_sku_code]['admin'])
                            || $status != $old_services[$value->buyer_sku_code]['status']
                            || ($old_services[$value->buyer_sku_code]['oldcategoryname'] == null && array_key_exists($value->category, $dont_updatecategory))
                        ) {
                            if (ceil($price) != $old_services[$value->buyer_sku_code]['admin'] && floor($price) != $old_services[$value->buyer_sku_code]['admin']) {
                                if ($price > $old_services[$value->buyer_sku_code]['admin']) {
                                    $type = 'UP';
                                } else {
                                    $type = 'DOWN';
                                }

                                if (!array_key_exists($old_services[$value->buyer_sku_code]['productid'], $history_log)) {
                                    $insert = array();
                                    $insert['userid'] = $userid;
                                    $insert['productid'] = $old_services[$value->buyer_sku_code]['productid'];
                                    $insert['type'] = $type;
                                    $insert['oldprice'] = $old_services[$value->buyer_sku_code]['admin'];
                                    $insert['newprice'] = $price;
                                    $insert['createddate'] = getCurrentDate();

                                    $insert_log[] = $insert;
                                } else {
                                    $update = array();
                                    $update['id'] = $history_log[$old_services[$value->buyer_sku_code]['productid']]['logid'];
                                    $update['type'] = $type;
                                    $update['oldprice'] = $old_services[$value->buyer_sku_code]['admin'];
                                    $update['newprice'] = $price;
                                    $update['updateddate'] = getCurrentDate();

                                    $update_log[] = $update;
                                }
                            }

                            $update = array();
                            $update['id'] = $old_services[$value->buyer_sku_code]['productid'];
                            if ($old_services[$value->buyer_sku_code]['dontupdate'] == null || $old_services[$value->buyer_sku_code]['dontupdate'] == 0) {
                                $update['productname'] = $value->product_name;
                                $update['status'] = ($value->buyer_product_status == 1 && $value->seller_product_status == 1);
                                $update['admin'] = $price;
                                $update['price'] = 0;
                                $update['profit'] = $profit;
                                $update['commission'] = $value->commission + $profit;
                            }
                            $update['vendorprice'] = $value->admin;

                            if (!array_key_exists($value->category, $dont_updatecategory)) {
                                $update['category'] = $value->category;
                            } else {
                                $update['category'] = $dont_updatecategory[$value->category]['category'];
                                $update['oldcategoryname'] = $value->category;
                                $update['dontupdatecategory'] = 1;
                            }

                            $update['brand'] = $value->brand;
                            $update['updateddate'] = getCurrentDate();

                            $update_batch[] = $update;
                        }
                    }

                    $updated_services[$value->buyer_sku_code] = $value;
                }
            } else {
                $do_checkproduct = false;
            }
        } else if ($vendor == 'VIPayment') {
            $vipayment = new VIPayment(stringEncryption('decrypt', $usercode), stringEncryption('decrypt', $apikey));
            $prepaid = $vipayment->services_prepaid();

            if (isset($prepaid->result) && $prepaid->result) {
                foreach ($prepaid->data as $key => $value) {
                    $profit = getPriceWithProfitV2('Prabayar', $value->price->basic, $profits);
                    $price = $value->price->basic + $profit;

                    if (!array_key_exists($value->code, $old_services)) {
                        $insert = array();
                        $insert['userid'] = $userid;
                        $insert['vendor'] = $vendor;
                        $insert['code'] = $value->code;
                        $insert['productname'] = $value->name;
                        $insert['description'] = $value->note;
                        $insert['status'] = $value->status == 'available' ? 1 : 0;
                        $insert['price'] = $price;
                        $insert['admin'] = 0;
                        $insert['vendorprice'] = $value->price->basic;
                        $insert['profit'] = $profit;
                        $insert['commission'] = 0;

                        if (!array_key_exists($value->type, $dont_updatecategory)) {
                            $insert['category'] = $value->type;
                            $insert['oldcategoryname'] = null;
                            $insert['dontupdatecategory'] = null;
                        } else {
                            $insert['category'] = $dont_updatecategory[$value->type]['category'];
                            $insert['oldcategoryname'] = $value->type;
                            $insert['dontupdatecategory'] = 1;
                        }

                        $insert['brand'] = $value->brand;
                        $insert['category_apikey'] = $category;
                        $insert['subcategory_apikey'] = 'PRABAYAR';
                        $insert['type'] = $value->category;
                        $insert['createddate'] = getCurrentDate();

                        $insert_batch[] = $insert;
                    } else {
                        $status = $value->status == 'available' ? 1 : 0;

                        if (
                            (ceil($price) != $old_services[$value->code]['price'] && floor($price) != $old_services[$value->code]['price'])
                            || $status != $old_services[$value->code]['status']
                            || ($old_services[$value->code]['oldcategoryname'] == null && array_key_exists($value->type, $dont_updatecategory))
                        ) {
                            if (ceil($price) != $old_services[$value->code]['price'] && floor($price) != $old_services[$value->code]['price']) {
                                if ($price > $old_services[$value->code]['price']) {
                                    $type = 'UP';
                                } else {
                                    $type = 'DOWN';
                                }

                                if (!array_key_exists($old_services[$value->code]['productid'], $history_log)) {
                                    $insert = array();
                                    $insert['userid'] = $userid;
                                    $insert['productid'] = $old_services[$value->code]['productid'];
                                    $insert['type'] = $type;
                                    $insert['oldprice'] = $old_services[$value->code]['price'];
                                    $insert['newprice'] = $price;
                                    $insert['createddate'] = getCurrentDate();

                                    $insert_log[] = $insert;
                                } else {
                                    $update = array();
                                    $update['id'] = $history_log[$old_services[$value->code]['productid']]['logid'];
                                    $update['type'] = $type;
                                    $update['oldprice'] = $old_services[$value->code]['price'];
                                    $update['newprice'] = $price;
                                    $update['updateddate'] = getCurrentDate();

                                    $update_log[] = $update;
                                }
                            }

                            $update = array();
                            $update['id'] = $old_services[$value->code]['productid'];
                            if ($old_services[$value->code]['dontupdate'] == null || $old_services[$value->code]['dontupdate'] == 0) {
                                $update['productname'] = $value->name;
                                $update['status'] = $value->status == 'available' ? 1 : 0;
                                $update['admin'] = 0;
                                $update['price'] = $price;
                                $update['profit'] = $profit;
                                $update['commission'] = 0;
                            }
                            $update['vendorprice'] = $value->price->basic;

                            if (!array_key_exists($value->type, $dont_updatecategory)) {
                                $update['category'] = $value->type;
                            } else {
                                $update['category'] = $dont_updatecategory[$value->type]['category'];
                                $update['oldcategoryname'] = $value->type;
                                $update['dontupdatecategory'] = 1;
                            }

                            $update['brand'] = $value->brand;
                            $update['updateddate'] = getCurrentDate();

                            $update_batch[] = $update;
                        }
                    }

                    $updated_services[$value->code] = $value;
                }
            } else {
                $do_checkproduct = false;
            }

            $postpaid = $vipayment->services_postpaid();

            if (isset($postpaid->result) && $postpaid->result) {
                foreach ($postpaid->data as $key => $value) {
                    $profit = getPriceWithProfitV2('Pascabayar', $value->price->basic, $profits);
                    $price = $value->price->basic + $profit;

                    if (!array_key_exists($value->code, $old_services)) {
                        $insert = array();
                        $insert['userid'] = $userid;
                        $insert['vendor'] = $vendor;
                        $insert['code'] = $value->code;
                        $insert['productname'] = $value->name;
                        $insert['description'] = $value->note;
                        $insert['status'] = $value->status == 'available' ? 1 : 0;
                        $insert['price'] = 0;
                        $insert['admin'] = 0;
                        $insert['vendorprice'] = $value->price->basic;
                        $insert['profit'] = $profit;
                        $insert['commission'] = 0;

                        if (!array_key_exists($value->type, $dont_updatecategory)) {
                            $insert['category'] = $value->type;
                            $insert['oldcategoryname'] = null;
                            $insert['dontupdatecategory'] = null;
                        } else {
                            $insert['category'] = $dont_updatecategory[$value->type]['category'];
                            $insert['oldcategoryname'] = $value->type;
                            $insert['dontupdatecategory'] = 1;
                        }

                        $insert['brand'] = $value->brand;
                        $insert['category_apikey'] = $category;
                        $insert['subcategory_apikey'] = 'PASCABAYAR';
                        $insert['type'] = $value->category;
                        $insert['createddate'] = getCurrentDate();

                        $insert_batch[] = $insert;
                    } else {
                        $status = $value->status == 'available' ? 1 : 0;

                        if (
                            (ceil($price) != $old_services[$value->code]['admin'] && floor($price) != $old_services[$value->code]['admin'])
                            || $status != $old_services[$value->code]['status']
                            || ($old_services[$value->code]['oldcategoryname'] == null && array_key_exists($value->type, $dont_updatecategory))
                        ) {
                            if (ceil($price) != $old_services[$value->code]['admin'] && floor($price) != $old_services[$value->code]['admin']) {
                                if ($price > $old_services[$value->code]['admin']) {
                                    $type = 'UP';
                                } else {
                                    $type = 'DOWN';
                                }

                                if (!array_key_exists($old_services[$value->code]['productid'], $history_log)) {
                                    $insert = array();
                                    $insert['userid'] = $userid;
                                    $insert['productid'] = $old_services[$value->code]['productid'];
                                    $insert['type'] = $type;
                                    $insert['oldprice'] = $old_services[$value->code]['admin'];
                                    $insert['newprice'] = $price;
                                    $insert['createddate'] = getCurrentDate();

                                    $insert_log[] = $insert;
                                } else {
                                    $update = array();
                                    $update['id'] = $history_log[$old_services[$value->code]['productid']]['logid'];
                                    $update['type'] = $type;
                                    $update['oldprice'] = $old_services[$value->code]['admin'];
                                    $update['newprice'] = $price;
                                    $update['updateddate'] = getCurrentDate();

                                    $update_log[] = $update;
                                }
                            }

                            $update = array();
                            $update['id'] = $old_services[$value->code]['productid'];
                            if ($old_services[$value->code]['dontupdate'] == null || $old_services[$value->code]['dontupdate'] == 0) {
                                $update['productname'] = $value->name;
                                $update['status'] = $value->status == 'available' ? 1 : 0;
                                $update['admin'] = $price;
                                $update['price'] = 0;
                                $update['profit'] = $profit;
                                $update['commission'] = 0;
                            }
                            $update['vendorprice'] = $value->price->basic;

                            if (!array_key_exists($value->type, $dont_updatecategory)) {
                                $update['category'] = $value->type;
                            } else {
                                $update['category'] = $dont_updatecategory[$value->type]['category'];
                                $update['oldcategoryname'] = $value->type;
                                $update['dontupdatecategory'] = 1;
                            }

                            $update['brand'] = $value->brand;
                            $update['updateddate'] = getCurrentDate();

                            $update_batch[] = $update;
                        }
                    }

                    $updated_services[$value->code] = $value;
                }
            } else {
                $do_checkproduct = false;
            }

            $game = $vipayment->services_game();

            if (isset($game->result) && $game->result) {
                foreach ($game->data as $key => $value) {
                    $profit = getPriceWithProfitV2('Pascabayar', $value->price->basic, $profits);
                    $price = $value->price->basic + $profit;

                    if (!array_key_exists($value->code, $old_services)) {
                        $insert = array();
                        $insert['userid'] = $userid;
                        $insert['vendor'] = $vendor;
                        $insert['code'] = $value->code;
                        $insert['productname'] = $value->name;
                        $insert['description'] = '';
                        $insert['status'] = $value->status == 'available' ? 1 : 0;
                        $insert['price'] = $price;
                        $insert['admin'] = 0;
                        $insert['vendorprice'] = $value->price->basic;
                        $insert['profit'] = $profit;
                        $insert['commission'] = 0;

                        if (!array_key_exists('game', $dont_updatecategory)) {
                            $insert['category'] = 'game';
                            $insert['oldcategoryname'] = null;
                            $insert['dontupdatecategory'] = null;
                        } else {
                            $insert['category'] = $dont_updatecategory['game']['category'];
                            $insert['oldcategoryname'] = 'game';
                            $insert['dontupdatecategory'] = 1;
                        }

                        $insert['brand'] = $value->game;
                        $insert['category_apikey'] = $category;
                        $insert['subcategory_apikey'] = 'PRABAYAR';
                        $insert['type'] = $value->game;
                        $insert['createddate'] = getCurrentDate();

                        $insert_batch[] = $insert;
                    } else {
                        $status = $value->status == 'available' ? 1 : 0;

                        if (
                            (ceil($price) != $old_services[$value->code]['price'] && floor($price) != $old_services[$value->code]['price'])
                            || $status != $old_services[$value->code]['status']
                            || ($old_services[$value->code]['oldcategoryname'] == null && array_key_exists('game', $dont_updatecategory))
                        ) {
                            if (ceil($price) != $old_services[$value->code]['price'] && floor($price) != $old_services[$value->code]['price']) {
                                if ($price > $old_services[$value->code]['price']) {
                                    $type = 'UP';
                                } else {
                                    $type = 'DOWN';
                                }

                                if (!array_key_exists($old_services[$value->code]['productid'], $history_log)) {
                                    $insert = array();
                                    $insert['userid'] = $userid;
                                    $insert['productid'] = $old_services[$value->code]['productid'];
                                    $insert['type'] = $type;
                                    $insert['oldprice'] = $old_services[$value->code]['price'];
                                    $insert['newprice'] = $price;
                                    $insert['createddate'] = getCurrentDate();

                                    $insert_log[] = $insert;
                                } else {
                                    $update = array();
                                    $update['id'] = $history_log[$old_services[$value->code]['productid']]['logid'];
                                    $update['type'] = $type;
                                    $update['oldprice'] = $old_services[$value->code]['price'];
                                    $update['newprice'] = $price;
                                    $update['updateddate'] = getCurrentDate();

                                    $update_log[] = $update;
                                }
                            }

                            $update = array();
                            $update['id'] = $old_services[$value->code]['productid'];
                            if ($old_services[$value->code]['dontupdate'] == null || $old_services[$value->code]['dontupdate'] == 0) {
                                $update['productname'] = $value->name;
                                $update['status'] = $value->status == 'available' ? 1 : 0;
                                $update['admin'] = 0;
                                $update['price'] = $price;
                                $update['profit'] = $profit;
                                $update['commission'] = 0;
                            }
                            $update['vendorprice'] = $value->price->basic;

                            if (!array_key_exists('game', $dont_updatecategory)) {
                                $update['category'] = 'game';
                            } else {
                                $update['category'] = $dont_updatecategory['game']['category'];
                                $update['oldcategoryname'] = 'game';
                                $update['dontupdatecategory'] = 1;
                            }

                            $update['brand'] = $value->game;
                            $update['updateddate'] = getCurrentDate();

                            $update_batch[] = $update;
                        }
                    }

                    $updated_services[$value->code] = $value;
                }
            } else {
                $do_checkproduct = false;
            }
        }
    } else if ($category == 'SMM') {
        if ($vendor == 'BuzzerPanel') {
            $buzzerpanel = new BuzzerPanel(stringEncryption('decrypt', $apikey), stringEncryption('decrypt', $secretkey));
            $product = $buzzerpanel->services();

            if (isset($product->status) && $product->status) {
                foreach ($product->data as $key => $value) {
                    $profit = getPriceWithProfitV2('Media Sosial', $value->price, $profits);
                    $price = $value->price + $profit;

                    if (!array_key_exists($value->id, $old_services)) {
                        $insert = array();
                        $insert['userid'] = $userid;
                        $insert['vendor'] = $vendor;
                        $insert['code'] = $value->id;
                        $insert['productname'] = $value->name;
                        $insert['description'] = $value->note;
                        $insert['status'] = 1;
                        $insert['price'] = $price;
                        $insert['vendorprice'] = $value->price;
                        $insert['profit'] = $profit;

                        if (!array_key_exists($value->category, $dont_updatecategory)) {
                            $insert['category'] = $value->category;
                            $insert['oldcategoryname'] = null;
                            $insert['dontupdatecategory'] = null;
                        } else {
                            $insert['category'] = $dont_updatecategory[$value->category]['category'];
                            $insert['oldcategoryname'] = $value->category;
                            $insert['dontupdatecategory'] = 1;
                        }

                        $insert['category_apikey'] = $category;
                        $insert['subcategory_apikey'] = 'SMM';
                        $insert['minorder'] = $value->min;
                        $insert['maxorder'] = $value->max;
                        $insert['type'] = $value->jenis;
                        $insert['createddate'] = getCurrentDate();

                        $insert_batch[] = $insert;
                    } else {
                        if (
                            (ceil($price) != $old_services[$value->id]['price'] && floor($price) != $old_services[$value->id]['price'])
                            || ($old_services[$value->id]['oldcategoryname'] == null && array_key_exists($value->category, $dont_updatecategory))
                        ) {
                            if ($price > $old_services[$value->id]['price']) {
                                $type = 'UP';
                            } else {
                                $type = 'DOWN';
                            }

                            if (!array_key_exists($old_services[$value->id]['productid'], $history_log)) {
                                $insert = array();
                                $insert['userid'] = $userid;
                                $insert['productid'] = $old_services[$value->id]['productid'];
                                $insert['type'] = $type;
                                $insert['oldprice'] = $old_services[$value->id]['price'];
                                $insert['newprice'] = $price;
                                $insert['createddate'] = getCurrentDate();

                                $insert_log[] = $insert;
                            } else {
                                $update = array();
                                $update['id'] = $history_log[$old_services[$value->id]['productid']]['logid'];
                                $update['type'] = $type;
                                $update['oldprice'] = $old_services[$value->id]['price'];
                                $update['newprice'] = $price;
                                $update['updateddate'] = getCurrentDate();

                                $update_log[] = $update;
                            }

                            $update = array();
                            $update['id'] = $old_services[$value->id]['productid'];
                            if ($old_services[$value->id]['dontupdate'] == null || $old_services[$value->id]['dontupdate'] == 0) {
                                $update['productname'] = $value->name;
                                $update['description'] = $value->note;
                                $update['status'] = 1;
                            }
                            $update['price'] = $price;
                            $update['vendorprice'] = $value->price;
                            $update['profit'] = $profit;

                            if (!array_key_exists($value->category, $dont_updatecategory)) {
                                $update['category'] = $value->category;
                                $update['oldcategoryname'] = null;
                                $update['dontupdatecategory'] = 0;
                            } else {
                                $update['category'] = $dont_updatecategory[$value->category]['category'];
                                $update['oldcategoryname'] = $value->category;
                                $update['dontupdatecategory'] = 1;
                            }

                            $update['minorder'] = $value->min;
                            $update['maxorder'] = $value->max;
                            $update['type'] = $value->jenis;
                            $update['updateddate'] = getCurrentDate();

                            $update_batch[] = $update;
                        }
                    }

                    $updated_services[$value->id] = $value;
                }
            } else {
                $do_checkproduct = false;
            }
        } else if ($vendor == 'MedanPedia') {
            $medanpedia = new MedanPedia(stringEncryption('decrypt', $usercode), stringEncryption('decrypt', $apikey));
            $product = $medanpedia->services();

            if (isset($product->status) && $product->status) {
                foreach ($product->data as $key => $value) {
                    $profit = getPriceWithProfitV2('Media Sosial', $value->price, $profits);
                    $price = $value->price + $profit;

                    if (!array_key_exists($value->id, $old_services)) {
                        $insert = array();
                        $insert['userid'] = $userid;
                        $insert['vendor'] = $vendor;
                        $insert['code'] = $value->id;
                        $insert['productname'] = $value->name;
                        $insert['description'] = $value->description;
                        $insert['status'] = 1;
                        $insert['price'] = $price;
                        $insert['vendorprice'] = $value->price;
                        $insert['profit'] = $profit;

                        if (!array_key_exists($value->category, $dont_updatecategory)) {
                            $insert['category'] = $value->category;
                            $insert['oldcategoryname'] = null;
                            $insert['dontupdatecategory'] = null;
                        } else {
                            $insert['category'] = $dont_updatecategory[$value->category]['category'];
                            $insert['oldcategoryname'] = $value->category;
                            $insert['dontupdatecategory'] = 1;
                        }

                        $insert['category_apikey'] = $category;
                        $insert['subcategory_apikey'] = 'SMM';
                        $insert['minorder'] = $value->min;
                        $insert['maxorder'] = $value->max;
                        $insert['type'] = $value->type;
                        $insert['createddate'] = getCurrentDate();

                        $insert_batch[] = $insert;
                    } else {
                        if (
                            (ceil($price) != $old_services[$value->id]['price'] && floor($price) != $old_services[$value->id]['price'])
                            || ($old_services[$value->id]['oldcategoryname'] == null && array_key_exists($value->category, $dont_updatecategory))
                        ) {
                            if ($price > $old_services[$value->id]['price']) {
                                $type = 'UP';
                            } else {
                                $type = 'DOWN';
                            }

                            if (!array_key_exists($old_services[$value->id]['productid'], $history_log)) {
                                $insert = array();
                                $insert['userid'] = $userid;
                                $insert['productid'] = $old_services[$value->id]['productid'];
                                $insert['type'] = $type;
                                $insert['oldprice'] = $old_services[$value->id]['price'];
                                $insert['newprice'] = $price;
                                $insert['createddate'] = getCurrentDate();

                                $insert_log[] = $insert;
                            } else {
                                $update = array();
                                $update['id'] = $history_log[$old_services[$value->id]['productid']]['logid'];
                                $update['type'] = $type;
                                $update['oldprice'] = $old_services[$value->id]['price'];
                                $update['newprice'] = $price;
                                $update['updateddate'] = getCurrentDate();

                                $update_log[] = $update;
                            }

                            $update = array();
                            $update['id'] = $old_services[$value->id]['productid'];
                            if ($old_services[$value->id]['dontupdate'] == null || $old_services[$value->id]['dontupdate'] == 0) {
                                $update['productname'] = $value->name;
                                $update['description'] = $value->description;
                                $update['status'] = 1;
                            }
                            $update['price'] = $price;
                            $update['vendorprice'] = $value->price;
                            $update['profit'] = $profit;

                            if (!array_key_exists($value->category, $dont_updatecategory)) {
                                $update['category'] = $value->category;
                                $update['oldcategoryname'] = null;
                                $update['dontupdatecategory'] = 0;
                            } else {
                                $update['category'] = $dont_updatecategory[$value->category]['category'];
                                $update['oldcategoryname'] = $value->category;
                                $update['dontupdatecategory'] = 1;
                            }

                            $update['minorder'] = $value->min;
                            $update['maxorder'] = $value->max;
                            $update['type'] = $value->type;
                            $update['updateddate'] = getCurrentDate();

                            $update_batch[] = $update;
                        }
                    }

                    $updated_services[$value->id] = $value;
                }
            } else {
                $do_checkproduct = false;
            }
        } else if ($vendor == 'IrvanKede') {
            $irvankede = new IrvanKede(stringEncryption('decrypt', $usercode), stringEncryption('decrypt', $apikey));
            $services = $irvankede->services();

            if (isset($services->status) && $services->status) {
                foreach ($services->data as $key => $value) {
                    $profit = getPriceWithProfitV2('Media Sosial', $value->price, $profits);
                    $price = $value->price + $profit;

                    if (!array_key_exists($value->id, $old_services)) {
                        $insert = array();
                        $insert['userid'] = $userid;
                        $insert['vendor'] = $vendor;
                        $insert['code'] = $value->id;
                        $insert['productname'] = $value->name;
                        $insert['description'] = $value->note;
                        $insert['status'] = $value->status == 1 ? 1 : 0;
                        $insert['price'] = $price;
                        $insert['vendorprice'] = $value->price;
                        $insert['profit'] = $profit;

                        if (!array_key_exists($value->category, $dont_updatecategory)) {
                            $insert['category'] = $value->category;
                            $insert['oldcategoryname'] = null;
                            $insert['dontupdatecategory'] = null;
                        } else {
                            $insert['category'] = $dont_updatecategory[$value->category]['category'];
                            $insert['oldcategoryname'] = $value->category;
                            $insert['dontupdatecategory'] = 1;
                        }

                        $insert['category_apikey'] = $category;
                        $insert['subcategory_apikey'] = 'SMM';
                        $insert['minorder'] = $value->min;
                        $insert['maxorder'] = $value->max;
                        $insert['type'] = null;
                        $insert['createddate'] = getCurrentDate();

                        $insert_batch[] = $insert;
                    } else {
                        if (
                            (ceil($price) != $old_services[$value->id]['price'] && floor($price) != $old_services[$value->id]['price'])
                            || $value->status != $old_services[$value->id]['status']
                            || ($old_services[$value->id]['oldcategoryname'] == null && array_key_exists($value->category, $dont_updatecategory))
                        ) {
                            if (ceil($price) != $old_services[$value->id]['price'] && floor($price) != $old_services[$value->id]['price']) {
                                if ($price > $old_services[$value->id]['price']) {
                                    $type = 'UP';
                                } else {
                                    $type = 'DOWN';
                                }

                                if (!array_key_exists($old_services[$value->id]['productid'], $history_log)) {
                                    $insert = array();
                                    $insert['userid'] = $userid;
                                    $insert['productid'] = $old_services[$value->id]['productid'];
                                    $insert['type'] = $type;
                                    $insert['oldprice'] = $old_services[$value->id]['price'];
                                    $insert['newprice'] = $price;
                                    $insert['createddate'] = getCurrentDate();

                                    $insert_log[] = $insert;
                                } else {
                                    $update = array();
                                    $update['id'] = $history_log[$old_services[$value->id]['productid']]['logid'];
                                    $update['type'] = $type;
                                    $update['oldprice'] = $old_services[$value->id]['price'];
                                    $update['newprice'] = $price;
                                    $update['updateddate'] = getCurrentDate();

                                    $update_log[] = $update;
                                }
                            }

                            $update = array();
                            $update['id'] = $old_services[$value->id]['productid'];
                            if ($old_services[$value->id]['dontupdate'] == null || $old_services[$value->id]['dontupdate'] == 0) {
                                $update['productname'] = $value->name;
                                $update['description'] = $value->note;
                                $update['status'] = $value->status == 1 ? 1 : 0;
                            }
                            $update['price'] = $price;
                            $update['vendorprice'] = $value->price;
                            $update['profit'] = $profit;

                            if (!array_key_exists($value->category, $dont_updatecategory)) {
                                $update['category'] = $value->category;
                                $update['oldcategoryname'] = null;
                                $update['dontupdatecategory'] = 0;
                            } else {
                                $update['category'] = $dont_updatecategory[$value->category]['category'];
                                $update['oldcategoryname'] = $value->category;
                                $update['dontupdatecategory'] = 1;
                            }

                            $update['minorder'] = $value->min;
                            $update['maxorder'] = $value->max;
                            $update['type'] = null;
                            $update['updateddate'] = getCurrentDate();

                            $update_batch[] = $update;
                        }
                    }

                    $updated_services[$value->id] = $value;
                }
            } else {
                $do_checkproduct = false;
            }
        } else if ($vendor == 'DailyPanel') {
            $dailypanel = new DailyPanel(stringEncryption('decrypt', $apikey), stringEncryption('decrypt', $secretkey));
            $services = $dailypanel->services();

            if (isset($services->success) && $services->success) {
                foreach ($services->msg as $key => $value) {
                    $profit = getPriceWithProfitV2('Media Sosial', $value->harga, $profits);
                    $price = $value->harga + $profit;

                    if (!array_key_exists($value->id, $old_services)) {
                        $insert = array();
                        $insert['userid'] = $userid;
                        $insert['vendor'] = $vendor;
                        $insert['code'] = $value->id;
                        $insert['productname'] = $value->layanan;
                        $insert['description'] = $value->keterangan;
                        $insert['status'] = $value->status == 'Tersedia' ? 1 : 0;
                        $insert['price'] = $price;
                        $insert['vendorprice'] = $value->harga;
                        $insert['profit'] = $profit;

                        if (!array_key_exists($value->kategori, $dont_updatecategory)) {
                            $insert['category'] = $value->kategori;
                            $insert['oldcategoryname'] = null;
                            $insert['dontupdatecategory'] = null;
                        } else {
                            $insert['category'] = $dont_updatecategory[$value->kategori]['category'];
                            $insert['oldcategoryname'] = $value->kategori;
                            $insert['dontupdatecategory'] = 1;
                        }

                        $insert['category_apikey'] = $category;
                        $insert['subcategory_apikey'] = 'SMM';
                        $insert['minorder'] = $value->min;
                        $insert['maxorder'] = $value->maks;
                        $insert['type'] = null;
                        $insert['createddate'] = getCurrentDate();

                        $insert_batch[] = $insert;
                    } else {
                        $status = $value->status == 'Tersedia' ? 1 : 0;

                        if (
                            (ceil($price) != $old_services[$value->id]['price'] && floor($price) != $old_services[$value->id]['price'])
                            || $status != $old_services[$value->id]['status']
                            || ($old_services[$value->id]['oldcategoryname'] == null && array_key_exists($value->kategori, $dont_updatecategory))
                        ) {
                            if (ceil($price) != $old_services[$value->id]['price'] && floor($price) != $old_services[$value->id]['price']) {
                                if ($price > $old_services[$value->id]['price']) {
                                    $type = 'UP';
                                } else {
                                    $type = 'DOWN';
                                }

                                if (!array_key_exists($old_services[$value->id]['productid'], $history_log)) {
                                    $insert = array();
                                    $insert['userid'] = $userid;
                                    $insert['productid'] = $old_services[$value->id]['productid'];
                                    $insert['type'] = $type;
                                    $insert['oldprice'] = $old_services[$value->id]['price'];
                                    $insert['newprice'] = $price;
                                    $insert['createddate'] = getCurrentDate();

                                    $insert_log[] = $insert;
                                } else {
                                    $update = array();
                                    $update['id'] = $history_log[$old_services[$value->id]['productid']]['logid'];
                                    $update['type'] = $type;
                                    $update['oldprice'] = $old_services[$value->id]['price'];
                                    $update['newprice'] = $price;
                                    $update['updateddate'] = getCurrentDate();

                                    $update_log[] = $update;
                                }
                            }

                            $update = array();
                            $update['id'] = $old_services[$value->id]['productid'];
                            if ($old_services[$value->id]['dontupdate'] == null || $old_services[$value->id]['dontupdate'] == 0) {
                                $update['productname'] = $value->layanan;
                                $update['description'] = $value->keterangan;
                                $update['status'] = $value->status == 'Tersedia' ? 1 : 0;
                            }
                            $update['price'] = $price;
                            $update['vendorprice'] = $value->harga;
                            $update['profit'] = $profit;

                            if (!array_key_exists($value->kategori, $dont_updatecategory)) {
                                $update['category'] = $value->kategori;
                                $update['oldcategoryname'] = null;
                                $update['dontupdatecategory'] = 0;
                            } else {
                                $update['category'] = $dont_updatecategory[$value->kategori]['category'];
                                $update['oldcategoryname'] = $value->kategori;
                                $update['dontupdatecategory'] = 1;
                            }

                            $update['minorder'] = $value->min;
                            $update['maxorder'] = $value->maks;
                            $update['type'] = null;
                            $update['updateddate'] = getCurrentDate();

                            $update_batch[] = $update;
                        }
                    }

                    $updated_services[$value->id] = $value;
                }
            } else {
                $do_checkproduct = false;
            }
        } else if ($vendor == 'WStore') {
            $wstore = new WStore(stringEncryption('decrypt', $usercode), stringEncryption('decrypt', $apikey), stringEncryption('decrypt', $secretkey));
            $services = $wstore->services();

            if (isset($services->response) && $services->response) {
                foreach ($services->data as $key => $value) {
                    $profit = getPriceWithProfitV2('Media Sosial', $value->price, $profits);
                    $price = $value->price + $profit;

                    if (!array_key_exists($value->id, $old_services)) {
                        $insert = array();
                        $insert['userid'] = $userid;
                        $insert['vendor'] = $vendor;
                        $insert['code'] = $value->id;
                        $insert['productname'] = $value->service_name;
                        $insert['description'] = $value->description;
                        $insert['status'] = 1;
                        $insert['price'] = $price;
                        $insert['vendorprice'] = $value->price;
                        $insert['profit'] = $profit;

                        if (!array_key_exists($value->category_name, $dont_updatecategory)) {
                            $insert['category'] = $value->category_name;
                            $insert['oldcategoryname'] = null;
                            $insert['dontupdatecategory'] = null;
                        } else {
                            $insert['category'] = $dont_updatecategory[$value->category_name]['category'];
                            $insert['oldcategoryname'] = $value->category_name;
                            $insert['dontupdatecategory'] = 1;
                        }

                        $insert['category_apikey'] = $category;
                        $insert['subcategory_apikey'] = 'SMM';
                        $insert['minorder'] = $value->min;
                        $insert['maxorder'] = $value->max;
                        $insert['type'] = null;
                        $insert['createddate'] = getCurrentDate();

                        $insert_batch[] = $insert;
                    } else {
                        if (
                            (ceil($price) != $old_services[$value->id]['price'] && floor($price) != $old_services[$value->id]['price'])
                            || ($old_services[$value->id]['oldcategoryname'] == null && array_key_exists($value->category_name, $dont_updatecategory))
                        ) {
                            if ($price > $old_services[$value->id]['price']) {
                                $type = 'UP';
                            } else {
                                $type = 'DOWN';
                            }

                            if (!array_key_exists($old_services[$value->id]['productid'], $history_log)) {
                                $insert = array();
                                $insert['userid'] = $userid;
                                $insert['productid'] = $old_services[$value->id]['productid'];
                                $insert['type'] = $type;
                                $insert['oldprice'] = $old_services[$value->id]['price'];
                                $insert['newprice'] = $price;
                                $insert['createddate'] = getCurrentDate();

                                $insert_log[] = $insert;
                            } else {
                                $update = array();
                                $update['id'] = $history_log[$old_services[$value->id]['productid']]['logid'];
                                $update['type'] = $type;
                                $update['oldprice'] = $old_services[$value->id]['price'];
                                $update['newprice'] = $price;
                                $update['updateddate'] = getCurrentDate();

                                $update_log[] = $update;
                            }

                            $update = array();
                            $update['id'] = $old_services[$value->id]['productid'];
                            if ($old_services[$value->id]['dontupdate'] == null || $old_services[$value->id]['dontupdate'] == 0) {
                                $update['productname'] = $value->service_name;
                                $update['description'] = $value->description;
                                $update['status'] = 1;
                            }
                            $update['price'] = $price;
                            $update['vendorprice'] = $value->price;
                            $update['profit'] = $profit;

                            if (!array_key_exists($value->category_name, $dont_updatecategory)) {
                                $update['category'] = $value->category_name;
                                $update['oldcategoryname'] = null;
                                $update['dontupdatecategory'] = 0;
                            } else {
                                $update['category'] = $dont_updatecategory[$value->category_name]['category'];
                                $update['oldcategoryname'] = $value->category_name;
                                $update['dontupdatecategory'] = 1;
                            }

                            $update['minorder'] = $value->min;
                            $update['maxorder'] = $value->max;
                            $update['type'] = null;
                            $update['updateddate'] = getCurrentDate();

                            $update_batch[] = $update;
                        }
                    }

                    $updated_services[$value->id] = $value;
                }
            } else {
                $do_checkproduct = false;
            }
        } else if ($vendor == 'UNDRCTRL') {
            $undrctrl = new UNDRCTRL(stringEncryption('decrypt', $apikey), stringEncryption('decrypt', $public_key));
            $services = $undrctrl->services();

            if ((is_array($services) && isset($services[0]->service)) || is_object($services)) {
                foreach ($services as $key => $value) {
                    $profit = getPriceWithProfitV2('Media Sosial', $value->rate, $profits);
                    $price = $value->rate + $profit;

                    if (!array_key_exists($value->service, $old_services)) {
                        $insert = array();
                        $insert['userid'] = $userid;
                        $insert['vendor'] = $vendor;
                        $insert['code'] = $value->service;
                        $insert['productname'] = $value->name;
                        $insert['status'] = 1;

                        if ($value->min == 1 && $value->max == 1) {
                            $insert['price'] = $price * 1000;
                            $insert['vendorprice'] = $value->rate * 1000;
                        } else {
                            $insert['price'] = $price;
                            $insert['vendorprice'] = $value->rate;
                        }

                        $insert['profit'] = $profit;

                        if (!array_key_exists($value->category, $dont_updatecategory)) {
                            $insert['category'] = $value->category;
                            $insert['oldcategoryname'] = null;
                            $insert['dontupdatecategory'] = null;
                        } else {
                            $insert['category'] = $dont_updatecategory[$value->category]['category'];
                            $insert['oldcategoryname'] = $value->category;
                            $insert['dontupdatecategory'] = 1;
                        }

                        $insert['category_apikey'] = $category;
                        $insert['subcategory_apikey'] = 'SMM';
                        $insert['minorder'] = $value->min;
                        $insert['maxorder'] = $value->max;
                        $insert['type'] = null;
                        $insert['createddate'] = getCurrentDate();

                        $insert_batch[] = $insert;
                    } else {
                        if (
                            (ceil($price) != $old_services[$value->service]['price']
                                && floor($price) != $old_services[$value->service]['price'])
                            || $old_services[$value->service]['description_null']
                            || ($old_services[$value->service]['oldcategoryname'] == null && array_key_exists($value->category, $dont_updatecategory))
                        ) {
                            if ($price > $old_services[$value->service]['price']) {
                                $type = 'UP';
                            } else {
                                $type = 'DOWN';
                            }

                            if (!array_key_exists($old_services[$value->service]['productid'], $history_log)) {
                                $insert = array();
                                $insert['userid'] = $userid;
                                $insert['productid'] = $old_services[$value->service]['productid'];
                                $insert['type'] = $type;
                                $insert['oldprice'] = $old_services[$value->service]['price'];
                                $insert['newprice'] = $price;
                                $insert['createddate'] = getCurrentDate();

                                $insert_log[] = $insert;
                            } else {
                                $update = array();
                                $update['id'] = $history_log[$old_services[$value->service]['productid']]['logid'];
                                $update['type'] = $type;
                                $update['oldprice'] = $old_services[$value->service]['price'];
                                $update['newprice'] = $price;
                                $update['updateddate'] = getCurrentDate();

                                $update_log[] = $update;
                            }

                            $update = array();
                            $update['id'] = $old_services[$value->service]['productid'];
                            if ($old_services[$value->service]['dontupdate'] == null || $old_services[$value->service]['dontupdate'] == 0) {
                                $update['productname'] = $value->name;
                                $update['status'] = 1;
                            }

                            if ($value->min == 1 && $value->max == 1) {
                                $update['price'] = $price * 1000;
                                $update['vendorprice'] = $value->rate * 1000;
                            } else {
                                $update['price'] = $price;
                                $update['vendorprice'] = $value->rate;
                            }

                            $update['profit'] = $profit;

                            if (!array_key_exists($value->category, $dont_updatecategory)) {
                                $update['category'] = $value->category;
                                $update['oldcategoryname'] = null;
                                $update['dontupdatecategory'] = 0;
                            } else {
                                $update['category'] = $dont_updatecategory[$value->category]['category'];
                                $update['oldcategoryname'] = $value->category;
                                $update['dontupdatecategory'] = 1;
                            }

                            $update['minorder'] = $value->min;
                            $update['maxorder'] = $value->max;
                            $update['type'] = null;
                            $update['updateddate'] = getCurrentDate();

                            $update_batch[] = $update;
                        }
                    }

                    $updated_services[$value->service] = $value;
                }
            } else {
                $do_checkproduct = false;
            }
        } else if ($vendor == 'SosmedOnline') {
            $sosmedonline = new SosmedOnline(stringEncryption('decrypt', $usercode), stringEncryption('decrypt', $apikey));
            $product = $sosmedonline->service();

            if (isset($product->status) && $product->status) {
                foreach ($product->data as $key => $value) {
                    $profit = getPriceWithProfitV2('Media Sosial', $value->price, $profits);
                    $price = $value->price + $profit;

                    if (!array_key_exists($value->id, $old_services)) {
                        $insert = array();
                        $insert['userid'] = $userid;
                        $insert['vendor'] = $vendor;
                        $insert['code'] = $value->id;
                        $insert['productname'] = $value->name;
                        $insert['description'] = $value->description;
                        $insert['status'] = 1;
                        $insert['price'] = $price;
                        $insert['vendorprice'] = $value->price;
                        $insert['profit'] = $profit;

                        if (!array_key_exists($value->category, $dont_updatecategory)) {
                            $insert['category'] = $value->category;
                            $insert['oldcategoryname'] = null;
                            $insert['dontupdatecategory'] = null;
                        } else {
                            $insert['category'] = $dont_updatecategory[$value->category]['category'];
                            $insert['oldcategoryname'] = $value->category;
                            $insert['dontupdatecategory'] = 1;
                        }

                        $insert['category_apikey'] = $category;
                        $insert['subcategory_apikey'] = 'SMM';
                        $insert['minorder'] = $value->min;
                        $insert['maxorder'] = $value->max;
                        $insert['type'] = null;
                        $insert['createddate'] = getCurrentDate();

                        $insert_batch[] = $insert;
                    } else {
                        if (
                            (ceil($price) != $old_services[$value->id]['price'] && floor($price) != $old_services[$value->id]['price'])
                            || ($old_services[$value->id]['oldcategoryname'] == null && array_key_exists($value->category, $dont_updatecategory))
                        ) {
                            if ($price > $old_services[$value->id]['price']) {
                                $type = 'UP';
                            } else {
                                $type = 'DOWN';
                            }

                            if (!array_key_exists($old_services[$value->id]['productid'], $history_log)) {
                                $insert = array();
                                $insert['userid'] = $userid;
                                $insert['productid'] = $old_services[$value->id]['productid'];
                                $insert['type'] = $type;
                                $insert['oldprice'] = $old_services[$value->id]['price'];
                                $insert['newprice'] = $price;
                                $insert['createddate'] = getCurrentDate();

                                $insert_log[] = $insert;
                            } else {
                                $update = array();
                                $update['id'] = $history_log[$old_services[$value->id]['productid']]['logid'];
                                $update['type'] = $type;
                                $update['oldprice'] = $old_services[$value->id]['price'];
                                $update['newprice'] = $price;
                                $update['updateddate'] = getCurrentDate();

                                $update_log[] = $update;
                            }

                            $update = array();
                            $update['id'] = $old_services[$value->id]['productid'];
                            if ($old_services[$value->id]['dontupdate'] == null || $old_services[$value->id]['dontupdate'] == 0) {
                                $update['productname'] = $value->name;
                                $update['status'] = 1;
                                $update['description'] = $value->description;
                            }
                            $update['price'] = $price;
                            $update['vendorprice'] = $value->price;
                            $update['profit'] = $profit;

                            if (!array_key_exists($value->category, $dont_updatecategory)) {
                                $update['category'] = $value->category;
                                $update['oldcategoryname'] = null;
                                $update['dontupdatecategory'] = 0;
                            } else {
                                $update['category'] = $dont_updatecategory[$value->category]['category'];
                                $update['oldcategoryname'] = $value->category;
                                $update['dontupdatecategory'] = 1;
                            }

                            $update['minorder'] = $value->min;
                            $update['maxorder'] = $value->max;
                            $update['type'] = null;
                            $update['updateddate'] = getCurrentDate();

                            $update_batch[] = $update;
                        }
                    }

                    $updated_services[$value->id] = $value;
                }
            } else {
                $do_checkproduct = false;
            }
        } else if ($vendor == 'SosmedOnlineVIP') {
            $sosmedonlinevip = new SosmedOnlineVIP(stringEncryption('decrypt', $usercode), stringEncryption('decrypt', $apikey));
            $product = $sosmedonlinevip->service();

            if (isset($product->status) && $product->status) {
                foreach ($product->data as $key => $value) {
                    $profit = getPriceWithProfitV2('Media Sosial', $value->price, $profits);
                    $price = $value->price + $profit;

                    if (!array_key_exists($value->id, $old_services)) {
                        $insert = array();
                        $insert['userid'] = $userid;
                        $insert['vendor'] = $vendor;
                        $insert['code'] = $value->id;
                        $insert['productname'] = $value->name;
                        $insert['description'] = $value->description;
                        $insert['status'] = 1;
                        $insert['price'] = $price;
                        $insert['vendorprice'] = $value->price;
                        $insert['profit'] = $profit;

                        if (!array_key_exists($value->category, $dont_updatecategory)) {
                            $insert['category'] = $value->category;
                            $insert['oldcategoryname'] = null;
                            $insert['dontupdatecategory'] = null;
                        } else {
                            $insert['category'] = $dont_updatecategory[$value->category]['category'];
                            $insert['oldcategoryname'] = $value->category;
                            $insert['dontupdatecategory'] = 1;
                        }

                        $insert['category_apikey'] = $category;
                        $insert['subcategory_apikey'] = 'SMM';
                        $insert['minorder'] = $value->min;
                        $insert['maxorder'] = $value->max;
                        $insert['type'] = null;
                        $insert['createddate'] = getCurrentDate();

                        $insert_batch[] = $insert;
                    } else {
                        if (
                            (ceil($price) != $old_services[$value->id]['price'] && floor($price) != $old_services[$value->id]['price'])
                            || ($old_services[$value->id]['oldcategoryname'] == null && array_key_exists($value->category, $dont_updatecategory))
                        ) {
                            if ($price > $old_services[$value->id]['price']) {
                                $type = 'UP';
                            } else {
                                $type = 'DOWN';
                            }

                            if (!array_key_exists($old_services[$value->id]['productid'], $history_log)) {
                                $insert = array();
                                $insert['userid'] = $userid;
                                $insert['productid'] = $old_services[$value->id]['productid'];
                                $insert['type'] = $type;
                                $insert['oldprice'] = $old_services[$value->id]['price'];
                                $insert['newprice'] = $price;
                                $insert['createddate'] = getCurrentDate();

                                $insert_log[] = $insert;
                            } else {
                                $update = array();
                                $update['id'] = $history_log[$old_services[$value->id]['productid']]['logid'];
                                $update['type'] = $type;
                                $update['oldprice'] = $old_services[$value->id]['price'];
                                $update['newprice'] = $price;
                                $update['updateddate'] = getCurrentDate();

                                $update_log[] = $update;
                            }

                            $update = array();
                            $update['id'] = $old_services[$value->id]['productid'];
                            if ($old_services[$value->id]['dontupdate'] == null || $old_services[$value->id]['dontupdate'] == 0) {
                                $update['productname'] = $value->name;
                                $update['description'] = $value->description;
                                $update['status'] = 1;
                            }
                            $update['price'] = $price;
                            $update['vendorprice'] = $value->price;
                            $update['profit'] = $profit;

                            if (!array_key_exists($value->category, $dont_updatecategory)) {
                                $update['category'] = $value->category;
                                $update['oldcategoryname'] = null;
                                $update['dontupdatecategory'] = 0;
                            } else {
                                $update['category'] = $dont_updatecategory[$value->category]['category'];
                                $update['oldcategoryname'] = $value->category;
                                $update['dontupdatecategory'] = 1;
                            }

                            $update['minorder'] = $value->min;
                            $update['maxorder'] = $value->max;
                            $update['type'] = null;
                            $update['updateddate'] = getCurrentDate();

                            $update_batch[] = $update;
                        }
                    }

                    $updated_services[$value->id] = $value;
                }
            } else {
                $do_checkproduct = false;
            }
        } else if ($vendor == 'DjuraganSosmed') {
            $djuragansosmed = new DjuraganSosmed(stringEncryption('decrypt', $apikey));
            $product = $djuragansosmed->services();

            if (isset($product[0]->service)) {
                foreach ($product as $key => $value) {
                    $profit = getPriceWithProfitV2('Media Sosial', $value->rate, $profits);
                    $price = $value->rate + $profit;

                    if (!array_key_exists($value->service, $old_services)) {
                        $insert = array();
                        $insert['userid'] = $userid;
                        $insert['vendor'] = $vendor;
                        $insert['code'] = $value->service;
                        $insert['productname'] = $value->name;
                        $insert['description'] = null;
                        $insert['status'] = 1;
                        $insert['price'] = $price;
                        $insert['vendorprice'] = $value->rate;
                        $insert['profit'] = $profit;

                        if (!array_key_exists($value->category, $dont_updatecategory)) {
                            $insert['category'] = $value->category;
                            $insert['oldcategoryname'] = null;
                            $insert['dontupdatecategory'] = null;
                        } else {
                            $insert['category'] = $dont_updatecategory[$value->category]['category'];
                            $insert['oldcategoryname'] = $value->category;
                            $insert['dontupdatecategory'] = 1;
                        }

                        $insert['category_apikey'] = $category;
                        $insert['subcategory_apikey'] = 'SMM';
                        $insert['minorder'] = $value->min;
                        $insert['maxorder'] = $value->max;
                        $insert['type'] = null;
                        $insert['createddate'] = getCurrentDate();

                        $insert_batch[] = $insert;
                    } else {
                        if (
                            (ceil($price) != $old_services[$value->service]['price'] && floor($price) != $old_services[$value->service]['price'])
                            || ($old_services[$value->service]['oldcategoryname'] == null && array_key_exists($value->category, $dont_updatecategory))
                        ) {
                            if ($price > $old_services[$value->service]['price']) {
                                $type = 'UP';
                            } else {
                                $type = 'DOWN';
                            }

                            if (!array_key_exists($old_services[$value->service]['productid'], $history_log)) {
                                $insert = array();
                                $insert['userid'] = $userid;
                                $insert['productid'] = $old_services[$value->service]['productid'];
                                $insert['type'] = $type;
                                $insert['oldprice'] = $old_services[$value->service]['price'];
                                $insert['newprice'] = $price;
                                $insert['createddate'] = getCurrentDate();

                                $insert_log[] = $insert;
                            } else {
                                $update = array();
                                $update['id'] = $history_log[$old_services[$value->service]['productid']]['logid'];
                                $update['type'] = $type;
                                $update['oldprice'] = $old_services[$value->service]['price'];
                                $update['newprice'] = $price;
                                $update['updateddate'] = getCurrentDate();

                                $update_log[] = $update;
                            }

                            $update = array();
                            $update['id'] = $old_services[$value->service]['productid'];
                            if ($old_services[$value->service]['dontupdate'] == null || $old_services[$value->service]['dontupdate'] == 0) {
                                $update['productname'] = $value->name;
                                $update['description'] = null;
                                $update['status'] = 1;
                            }
                            $update['price'] = $price;
                            $update['vendorprice'] = $value->rate;
                            $update['profit'] = $profit;

                            if (!array_key_exists($value->category, $dont_updatecategory)) {
                                $update['category'] = $value->category;
                                $update['oldcategoryname'] = null;
                                $update['dontupdatecategory'] = 0;
                            } else {
                                $update['category'] = $dont_updatecategory[$value->category]['category'];
                                $update['oldcategoryname'] = $value->category;
                                $update['dontupdatecategory'] = 1;
                            }

                            $update['minorder'] = $value->min;
                            $update['maxorder'] = $value->max;
                            $update['type'] = null;
                            $update['updateddate'] = getCurrentDate();

                            $update_batch[] = $update;
                        }
                    }

                    $updated_services[$value->service] = $value;
                }
            } else {
                $do_checkproduct = false;
            }
        } else if ($vendor == 'SMMRaja') {
            $raja = new SMMRaja(stringEncryption('decrypt', $apikey));
            $product = $raja->services();

            if (isset($product[0]->service)) {
                foreach ($product as $key => $value) {
                    $rate = $value->rate * $currencyrate;
                    $profit = getPriceWithProfitV2('Media Sosial', $rate, $profits);
                    $price = $rate + $profit;

                    if (!array_key_exists($value->service, $old_services)) {
                        $insert = array();
                        $insert['userid'] = $userid;
                        $insert['vendor'] = $vendor;
                        $insert['code'] = $value->service;
                        $insert['productname'] = $value->name;
                        $insert['description'] = $value->description;
                        $insert['status'] = 1;
                        $insert['price'] = $price;
                        $insert['vendorprice'] = $rate;
                        $insert['profit'] = $profit;

                        if (!array_key_exists($value->category, $dont_updatecategory)) {
                            $insert['category'] = $value->category;
                            $insert['oldcategoryname'] = null;
                            $insert['dontupdatecategory'] = null;
                        } else {
                            $insert['category'] = $dont_updatecategory[$value->category]['category'];
                            $insert['oldcategoryname'] = $value->category;
                            $insert['dontupdatecategory'] = 1;
                        }

                        $insert['category_apikey'] = $category;
                        $insert['subcategory_apikey'] = 'SMM';
                        $insert['minorder'] = $value->min;
                        $insert['maxorder'] = $value->max;
                        $insert['type'] = null;
                        $insert['createddate'] = getCurrentDate();

                        $insert_batch[] = $insert;
                    } else {
                        if (
                            (ceil($price) != $old_services[$value->service]['price'] && floor($price) != $old_services[$value->service]['price'])
                            || ($old_services[$value->service]['oldcategoryname'] == null && array_key_exists($value->category, $dont_updatecategory))
                        ) {
                            if ($price > $old_services[$value->service]['price']) {
                                $type = 'UP';
                            } else {
                                $type = 'DOWN';
                            }

                            if (!array_key_exists($old_services[$value->service]['productid'], $history_log)) {
                                $insert = array();
                                $insert['userid'] = $userid;
                                $insert['productid'] = $old_services[$value->service]['productid'];
                                $insert['type'] = $type;
                                $insert['oldprice'] = $old_services[$value->service]['price'];
                                $insert['newprice'] = $price;
                                $insert['createddate'] = getCurrentDate();

                                $insert_log[] = $insert;
                            } else {
                                $update = array();
                                $update['id'] = $history_log[$old_services[$value->service]['productid']]['logid'];
                                $update['type'] = $type;
                                $update['oldprice'] = $old_services[$value->service]['price'];
                                $update['newprice'] = $price;
                                $update['updateddate'] = getCurrentDate();

                                $update_log[] = $update;
                            }

                            $update = array();
                            $update['id'] = $old_services[$value->service]['productid'];
                            if ($old_services[$value->service]['dontupdate'] == null || $old_services[$value->service]['dontupdate'] == 0) {
                                $update['productname'] = $value->name;
                                $update['description'] = $value->description;
                                $update['status'] = 1;
                            }
                            $update['price'] = $price;
                            $update['vendorprice'] = $rate;
                            $update['profit'] = $profit;

                            if (!array_key_exists($value->category, $dont_updatecategory)) {
                                $update['category'] = $value->category;
                                $update['oldcategoryname'] = null;
                                $update['dontupdatecategory'] = 0;
                            } else {
                                $update['category'] = $dont_updatecategory[$value->category]['category'];
                                $update['oldcategoryname'] = $value->category;
                                $update['dontupdatecategory'] = 1;
                            }

                            $update['minorder'] = $value->min;
                            $update['maxorder'] = $value->max;
                            $update['type'] = null;
                            $update['updateddate'] = getCurrentDate();

                            $update_batch[] = $update;
                        }
                    }

                    $updated_services[$value->service] = $value;
                }
            } else {
                $do_checkproduct = false;
            }
        } else if ($vendor == 'Snow') { // belum selesai
            $snow = new SMMSnow(stringEncryption('decrypt', $apikey));
            $product = $snow->services();

            if (isset($product[0]->service)) {
                foreach ($product as $key => $value) {
                    $rate = $value->rate * $currencyrate;
                    $profit = getPriceWithProfitV2('Media Sosial', $rate, $profits);
                    $price = $rate + $profit;

                    if (!array_key_exists($value->service, $old_services)) {
                        $insert = array();
                        $insert['userid'] = $userid;
                        $insert['vendor'] = $vendor;
                        $insert['code'] = $value->service;
                        $insert['productname'] = $value->name;
                        $insert['description'] = null;
                        $insert['status'] = 1;
                        $insert['price'] = $price;
                        $insert['vendorprice'] = $rate;
                        $insert['profit'] = $profit;
                        $insert['category'] = $value->category;
                        $insert['category_apikey'] = $category;
                        $insert['subcategory_apikey'] = 'SMM';
                        $insert['minorder'] = $value->min;
                        $insert['maxorder'] = $value->max;
                        $insert['type'] = null;
                        $insert['createddate'] = getCurrentDate();

                        $insert_batch[] = $insert;
                    } else {
                        if (ceil($price) != $old_services[$value->service]['price'] && floor($price) != $old_services[$value->service]['price']) {
                            if ($price > $old_services[$value->service]['price']) {
                                $type = 'UP';
                            } else {
                                $type = 'DOWN';
                            }

                            if (!array_key_exists($old_services[$value->service]['productid'], $history_log)) {
                                $insert = array();
                                $insert['userid'] = $userid;
                                $insert['productid'] = $old_services[$value->service]['productid'];
                                $insert['type'] = $type;
                                $insert['oldprice'] = $old_services[$value->service]['price'];
                                $insert['newprice'] = $price;
                                $insert['createddate'] = getCurrentDate();

                                $insert_log[] = $insert;
                            } else {
                                $update = array();
                                $update['id'] = $history_log[$old_services[$value->service]['productid']]['logid'];
                                $update['type'] = $type;
                                $update['oldprice'] = $old_services[$value->service]['price'];
                                $update['newprice'] = $price;
                                $update['updateddate'] = getCurrentDate();

                                $update_log[] = $update;
                            }

                            $update = array();
                            $update['id'] = $old_services[$value->service]['productid'];
                            if ($old_services[$value->service]['dontupdate'] == null || $old_services[$value->service]['dontupdate'] == 0) {
                                $update['productname'] = $value->name;
                                $update['description'] = null;
                                $update['status'] = 1;
                            }
                            $update['price'] = $price;
                            $update['vendorprice'] = $rate;
                            $update['profit'] = $profit;
                            if ($old_services[$value->service]['dontupdatecategory'] == null || $old_services[$value->service]['dontupdatecategory'] == 0) {
                                $update['category'] = $value->category;
                            }
                            $update['minorder'] = $value->min;
                            $update['maxorder'] = $value->max;
                            $update['type'] = null;
                            $update['updateddate'] = getCurrentDate();

                            $update_batch[] = $update;
                        }
                    }

                    $updated_services[$value->service] = $value;
                }
            } else {
                $do_checkproduct = false;
            }
        } else if ($vendor == 'SMMIllusion') {
            $illusion = new SMMIllusion(stringEncryption('decrypt', $apikey));
            $product = $illusion->services();

            if (isset($product[0]->service)) {
                foreach ($product as $key => $value) {
                    $rate = $value->rate * $currencyrate;
                    $profit = getPriceWithProfitV2('Media Sosial', $rate, $profits);
                    $price = $rate + $profit;

                    if (!array_key_exists($value->service, $old_services)) {
                        $insert = array();
                        $insert['userid'] = $userid;
                        $insert['vendor'] = $vendor;
                        $insert['code'] = $value->service;
                        $insert['productname'] = $value->name;
                        $insert['description'] = null;
                        $insert['status'] = 1;
                        $insert['price'] = $price;
                        $insert['vendorprice'] = $rate;
                        $insert['profit'] = $profit;

                        if (!array_key_exists($value->category, $dont_updatecategory)) {
                            $insert['category'] = $value->category;
                            $insert['oldcategoryname'] = null;
                            $insert['dontupdatecategory'] = null;
                        } else {
                            $insert['category'] = $dont_updatecategory[$value->category]['category'];
                            $insert['oldcategoryname'] = $value->category;
                            $insert['dontupdatecategory'] = 1;
                        }

                        $insert['category_apikey'] = $category;
                        $insert['subcategory_apikey'] = 'SMM';
                        $insert['minorder'] = $value->min;
                        $insert['maxorder'] = $value->max;
                        $insert['type'] = $value->type;
                        $insert['createddate'] = getCurrentDate();

                        $insert_batch[] = $insert;
                    } else {
                        if (
                            (ceil($price) != $old_services[$value->service]['price'] && floor($price) != $old_services[$value->service]['price'])
                            || ($old_services[$value->service]['oldcategoryname'] == null && array_key_exists($value->category, $dont_updatecategory))
                        ) {
                            if ($price > $old_services[$value->service]['price']) {
                                $type = 'UP';
                            } else {
                                $type = 'DOWN';
                            }

                            if (!array_key_exists($old_services[$value->service]['productid'], $history_log)) {
                                $insert = array();
                                $insert['userid'] = $userid;
                                $insert['productid'] = $old_services[$value->service]['productid'];
                                $insert['type'] = $type;
                                $insert['oldprice'] = $old_services[$value->service]['price'];
                                $insert['newprice'] = $price;
                                $insert['createddate'] = getCurrentDate();

                                $insert_log[] = $insert;
                            } else {
                                $update = array();
                                $update['id'] = $history_log[$old_services[$value->service]['productid']]['logid'];
                                $update['type'] = $type;
                                $update['oldprice'] = $old_services[$value->service]['price'];
                                $update['newprice'] = $price;
                                $update['updateddate'] = getCurrentDate();

                                $update_log[] = $update;
                            }

                            $update = array();
                            $update['id'] = $old_services[$value->service]['productid'];
                            if ($old_services[$value->service]['dontupdate'] == null || $old_services[$value->service]['dontupdate'] == 0) {
                                $update['productname'] = $value->name;
                                $update['description'] = null;
                                $update['status'] = 1;
                            }
                            $update['price'] = $price;
                            $update['vendorprice'] = $rate;
                            $update['profit'] = $profit;

                            if (!array_key_exists($value->category, $dont_updatecategory)) {
                                $update['category'] = $value->category;
                                $update['oldcategoryname'] = null;
                                $update['dontupdatecategory'] = 0;
                            } else {
                                $update['category'] = $dont_updatecategory[$value->category]['category'];
                                $update['oldcategoryname'] = $value->category;
                                $update['dontupdatecategory'] = 1;
                            }

                            $update['minorder'] = $value->min;
                            $update['maxorder'] = $value->max;
                            $update['type'] = $value->type;
                            $update['updateddate'] = getCurrentDate();

                            $update_batch[] = $update;
                        }
                    }

                    $updated_services[$value->service] = $value;
                }
            } else {
                $do_checkproduct = false;
            }
        } else if ($vendor == 'V1Pedia') {
            $v1pedia = new V1Pedia(stringEncryption('decrypt', $usercode), stringEncryption('decrypt', $apikey), stringEncryption('decrypt', $secretkey));
            $services = $v1pedia->services();

            if (isset($services->response) && $services->response) {
                foreach ($services->data as $key => $value) {
                    $profit = getPriceWithProfitV2('Media Sosial', $value->price, $profits);
                    $price = $value->price + $profit;

                    if (!array_key_exists($value->id, $old_services)) {
                        $insert = array();
                        $insert['userid'] = $userid;
                        $insert['vendor'] = $vendor;
                        $insert['code'] = $value->id;
                        $insert['productname'] = $value->service_name;
                        $insert['description'] = $value->description;
                        $insert['status'] = 1;
                        $insert['price'] = $price;
                        $insert['vendorprice'] = $value->price;
                        $insert['profit'] = $profit;

                        if (!array_key_exists($value->category_name, $dont_updatecategory)) {
                            $insert['category'] = $value->category_name;
                            $insert['oldcategoryname'] = null;
                            $insert['dontupdatecategory'] = null;
                        } else {
                            $insert['category'] = $dont_updatecategory[$value->category_name]['category'];
                            $insert['oldcategoryname'] = $value->category_name;
                            $insert['dontupdatecategory'] = 1;
                        }

                        $insert['category_apikey'] = $category;
                        $insert['subcategory_apikey'] = 'SMM';
                        $insert['minorder'] = $value->min;
                        $insert['maxorder'] = $value->max;
                        $insert['type'] = $value->type;
                        $insert['createddate'] = getCurrentDate();

                        $insert_batch[] = $insert;
                    } else {
                        if (
                            (ceil($price) != $old_services[$value->id]['price'] && floor($price) != $old_services[$value->id]['price'])
                            || ($old_services[$value->id]['oldcategoryname'] == null && array_key_exists($value->category_name, $dont_updatecategory))
                        ) {
                            if ($price > $old_services[$value->id]['price']) {
                                $type = 'UP';
                            } else {
                                $type = 'DOWN';
                            }

                            if (!array_key_exists($old_services[$value->id]['productid'], $history_log)) {
                                $insert = array();
                                $insert['userid'] = $userid;
                                $insert['productid'] = $old_services[$value->id]['productid'];
                                $insert['type'] = $type;
                                $insert['oldprice'] = $old_services[$value->id]['price'];
                                $insert['newprice'] = $price;
                                $insert['createddate'] = getCurrentDate();

                                $insert_log[] = $insert;
                            } else {
                                $update = array();
                                $update['id'] = $history_log[$old_services[$value->id]['productid']]['logid'];
                                $update['type'] = $type;
                                $update['oldprice'] = $old_services[$value->id]['price'];
                                $update['newprice'] = $price;
                                $update['updateddate'] = getCurrentDate();

                                $update_log[] = $update;
                            }

                            $update = array();
                            $update['id'] = $old_services[$value->id]['productid'];
                            if ($old_services[$value->id]['dontupdate'] == null || $old_services[$value->id]['dontupdate'] == 0) {
                                $update['productname'] = $value->service_name;
                                $update['description'] = $value->description;
                                $update['status'] = 1;
                            }
                            $update['price'] = $price;
                            $update['vendorprice'] = $value->price;
                            $update['profit'] = $profit;

                            if (!array_key_exists($value->category_name, $dont_updatecategory)) {
                                $update['category'] = $value->category_name;
                                $update['oldcategoryname'] = null;
                                $update['dontupdatecategory'] = 0;
                            } else {
                                $update['category'] = $dont_updatecategory[$value->category_name]['category'];
                                $update['oldcategoryname'] = $value->category_name;
                                $update['dontupdatecategory'] = 1;
                            }

                            $update['minorder'] = $value->min;
                            $update['maxorder'] = $value->max;
                            $update['type'] = $value->type;
                            $update['updateddate'] = getCurrentDate();

                            $update_batch[] = $update;
                        }
                    }

                    $updated_services[$value->id] = $value;
                }
            } else {
                $do_checkproduct = false;
            }
        }
    }

    $insert_batch_chunk = array_chunk($insert_batch, 25);
    $insert_log_batch_chunk = array_chunk($insert_log, 25);
    $update_log_batch_chunk = array_chunk($update_log, 25);
    $update_batch_chunk = array_chunk($update_batch, 25);

    if ($dontadd_product != 1) {
        if (count($updated_services) > 0 && $do_checkproduct == true) {
            foreach ($old_services as $key => $value) {
                if (!array_key_exists($key, $updated_services)) {
                    $CI->msproduct->delete(array(
                        'userid' => $userid,
                        'vendor' => $vendor,
                        'code' => $key,
                        'category_apikey' => $category
                    ));
                }
            }
        }

        foreach ($insert_batch_chunk as $num => $insert) {
            $CI->msproduct->insert_batch($insert);
        }
    } else {
        $CI->mstempproduct->delete(array(
            'userid' => $userid,
            'vendor' => $vendor,
            'vendorid' => null
        ));

        foreach ($insert_batch_chunk as $num => $data) {
            $insert = array();
            $insert['userid'] = $userid;
            $insert['vendor'] = $vendor;
            $insert['vendorid'] = null;
            $insert['data'] = json_encode($data);
            $insert['createddate'] = getCurrentDate();
            $insert['createdby'] = $userid;
            $insert['updateddate'] = getCurrentDate();
            $insert['updatedby'] = $userid;

            $CI->mstempproduct->insert($insert);
        }
    }

    foreach ($insert_log_batch_chunk as $num => $insert) {
        $CI->productpricelog->insert_batch($insert);
    }

    foreach ($update_log_batch_chunk as $num => $update) {
        $CI->productpricelog->update_batch($update, 'id');
    }

    foreach ($update_batch_chunk as $num => $update) {
        $CI->msproduct->update_batch($update, 'id');
    }

    return true;
}

function getTypeUser()
{
    $CI = &get_instance();
    $currentuser = getCurrentUser();

    $get = $CI->db->get_where('packagelicense', array(
        'id' => $currentuser->licenseid
    ))->row();

    return $get->name;
}

function sendMail($host, $username, $password, $port, $email, $companyname, $subject, $message)
{
    $CI = &get_instance();

    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        return false;
    }

    $config = array(
        'mailtype' => 'html',
        'charset' => 'UTF-8',
        'protocol' => 'smtp',
        'smtp_host' => $host,
        'smtp_user' => $username,
        'smtp_pass' => $password,
        'smtp_crypto' => 'ssl',
        'smtp_port' => $port,
        'crlf' => "\r\n",
        'newline' => "\r\n"
    );

    // Initialize
    $CI->email->initialize($config);
    $CI->email->from($username, $companyname);
    $CI->email->to($email);
    $CI->email->subject($subject);
    $CI->email->message($message);

    return $CI->email->send();
}

function getCurrentThemeConfiguration()
{
    $CI = &get_instance();

    $get = $CI->db->get_where('themeconfiguration', array(
        'userid' => getCurrentIdUser(),
        'isused' => 1
    ))->row();

    return $get != null ? $get->themename : null;
}

function thousandsCurrencyNumber($num)
{
    for ($i = 0; $num >= 1000; $i++) {
        $num /= 1000;
    }

    return $num;
}

function thousandsCurrencyName($num)
{
    $units = ['', 'K', 'M', 'B', 'T'];

    for ($i = 0; $num >= 1000; $i++) {
        $num /= 1000;
    }

    return $units[$i];
}

function requestSocket($uri, $data)
{
    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, "https://socket.server-ppobsmm.com/$uri");
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

    $result = curl_exec($ch);
    curl_close($ch);

    return json_decode($result);
}

function log_message_user($type, $message, $userid = null)
{
    $CI = &get_instance();

    $insert = array();
    $insert['userid'] = $userid ?? getCurrentIdUser();
    $insert['type'] = $type;
    $insert['message'] = $message;
    $insert['createddate'] = getCurrentDate();

    return $CI->db->insert('errorlogger', $insert);
}

function get_script_captcha()
{
    $CI = &get_instance();

    return $CI->captcha->getScriptTag();
}

function get_recaptcha_response()
{
    $CI = &get_instance();

    $g_captcha = getPost('g-recaptcha-response');
    $g_response = $CI->captcha->verifyResponse($g_captcha);

    if (!isset($g_response['success']) || $g_response['success'] <> true) {
        return false;
    } else {
        return true;
    }
}

function get_widget_captcha()
{
    $CI = &get_instance();

    return $CI->captcha->getWidget();
}

function getPercentageUpDownProfit($month, $year)
{
    $CI = &get_instance();

    $currentIdUser = getCurrentIdUser();

    $profit_this_month = $CI->db->select('SUM(a.profit) AS profit')
        ->from('trorder a')
        ->join('msusers b', 'b.id = a.userid', 'LEFT')
        ->where(array(
            "(b.merchantid = '$currentIdUser' OR a.merchantid_order = '$currentIdUser') =" => true,
            "(LOWER(a.status) = 'success' OR LOWER(a.status) = 'sukses' OR LOWER(a.status) = 'completed') =" => true,
        ))
        ->where('MONTH(a.createddate)', $month)
        ->where('YEAR(a.createddate)', $year)
        ->get()->row()->profit;

    $profit_last_month = $CI->db->select('SUM(a.profit) AS profit')
        ->from('trorder a')
        ->join('msusers b', 'b.id = a.userid', 'LEFT')
        ->where(array(
            "(b.merchantid = '$currentIdUser' OR a.merchantid_order = '$currentIdUser') =" => true,
            "(LOWER(a.status) = 'success' OR LOWER(a.status) = 'sukses' OR LOWER(a.status) = 'completed') =" => true,
        ))
        ->where('MONTH(a.createddate)', date('m', strtotime(date("$year-$month-01") . ' -1 month')))
        ->where('YEAR(a.createddate)', date('Y', strtotime(date("$year-$month-01") . ' -1 month')))
        ->get()->row()->profit;

    if ($profit_last_month == 0) {
        return 0;
    }

    $percentage = round(($profit_this_month - $profit_last_month) / $profit_last_month * 100);

    return $percentage;
}

function getPercentageUpDownUnrealizedProfit($month, $year)
{
    $CI = &get_instance();

    $currentIdUser = getCurrentIdUser();

    $unrealized_profit_this_month = $CI->db->select('SUM(a.profit) AS profit')
        ->from('trorder a')
        ->join('msusers b', 'b.id = a.userid', 'LEFT')
        ->where(array(
            "(b.merchantid = '$currentIdUser' OR a.merchantid_order = '$currentIdUser') =" => true,
            "(LOWER(a.status) = 'pending' OR LOWER(a.status) = 'in progress' OR LOWER(a.status) = 'processing') =" => true,
        ))
        ->where('MONTH(a.createddate)', $month)
        ->where('YEAR(a.createddate)', $year)
        ->get()->row()->profit;

    $unrealized_profit_last_month = $CI->db->select('SUM(a.profit) AS profit')
        ->from('trorder a')
        ->join('msusers b', 'b.id = a.userid', 'LEFT')
        ->where(array(
            "(b.merchantid = '$currentIdUser' OR a.merchantid_order = '$currentIdUser') =" => true,
            "(LOWER(a.status) = 'pending' OR LOWER(a.status) = 'in progress' OR LOWER(a.status) = 'processing') =" => true,
        ))
        ->where('MONTH(a.createddate)', date('m', strtotime(date("$year-$month-01") . ' -1 month')))
        ->where('YEAR(a.createddate)', date('Y', strtotime(date("$year-$month-01") . ' -1 month')))
        ->get()->row()->profit;

    if ($unrealized_profit_last_month == 0) {
        return 0;
    }

    $percentage = round(($unrealized_profit_this_month - $unrealized_profit_last_month) / $unrealized_profit_last_month * 100);

    return $percentage;
}

#get percentage up down omset smm
function getPercentageUpDownOmset($month, $year)
{
    $CI = &get_instance();

    $currentIdUser = getCurrentIdUser();

    $omset_this_month = $CI->db->select('SUM(a.price) AS price')
        ->from('trorder a')
        ->join('msusers b', 'b.id = a.userid', 'LEFT')
        ->where(array(
            "(b.merchantid = '$currentIdUser' OR a.merchantid_order = '$currentIdUser') =" => true,
        ))
        ->where('a.type', 'SMM')
        ->where(array(
            "(LOWER(a.status) = 'success' OR LOWER(a.status) = 'sukses' OR LOWER(a.status) = 'completed') =" => true,
        ))
        ->where('MONTH(a.createddate)', $month)
        ->where('YEAR(a.createddate)', $year)
        ->get()
        ->row()
        ->price;

    $omset_last_month = $CI->db->select('SUM(a.price) AS price')
        ->from('trorder a')
        ->join('msusers b', 'b.id = a.userid', 'LEFT')
        ->where(array(
            "(b.merchantid = '$currentIdUser' OR a.merchantid_order = '$currentIdUser') =" => true,
        ))
        ->where('a.type', 'SMM')
        ->where(array(
            "(LOWER(a.status) = 'success' OR LOWER(a.status) = 'sukses' OR LOWER(a.status) = 'completed') =" => true,
        ))
        ->where('MONTH(a.createddate)', date('m', strtotime(date("$year-$month-01") . ' -1 month')))
        ->where('YEAR(a.createddate)', date('Y', strtotime(date("$year-$month-01") . ' -1 month')))
        ->get()
        ->row()
        ->price;

    if ($omset_last_month == 0) {
        return 0;
    }

    $percentage = round(($omset_this_month - $omset_last_month) / $omset_last_month * 100);

    return $percentage;
}

#get percentage up down omset ppob
function getPercentageUpDownOmsetPpob($month, $year)
{
    $CI = &get_instance();

    $currentIdUser = getCurrentIdUser();

    $omset_this_month = $CI->db->select('SUM(a.price) AS price')
        ->from('trorder a')
        ->join('msusers b', 'b.id = a.userid', 'LEFT')
        ->where(array(
            "(b.merchantid = '$currentIdUser' OR a.merchantid_order = '$currentIdUser') =" => true,
        ))
        ->where('a.type', 'PPOB')
        ->where(array(
            "(LOWER(a.status) = 'success' OR LOWER(a.status) = 'sukses' OR LOWER(a.status) = 'completed') =" => true,
        ))
        ->where('MONTH(a.createddate)', $month)
        ->where('YEAR(a.createddate)', $year)
        ->get()
        ->row()
        ->price;

    $omset_last_month = $CI->db->select('SUM(a.price) AS price')
        ->from('trorder a')
        ->join('msusers b', 'b.id = a.userid', 'LEFT')
        ->where(array(
            "(b.merchantid = '$currentIdUser' OR a.merchantid_order = '$currentIdUser') =" => true,
        ))
        ->where('a.type', 'PPOB')
        ->where(array(
            "(LOWER(a.status) = 'success' OR LOWER(a.status) = 'sukses' OR LOWER(a.status) = 'completed') =" => true,
        ))
        ->where('MONTH(a.createddate)', date('m', strtotime(date("$year-$month-01") . ' -1 month')))
        ->where('YEAR(a.createddate)', date('Y', strtotime(date("$year-$month-01") . ' -1 month')))
        ->get()
        ->row()
        ->price;

    if ($omset_last_month == 0) {
        return 0;
    }

    $percentage = round(($omset_this_month - $omset_last_month) / $omset_last_month * 100);

    return $percentage;
}

function isLocalhost()
{
    return $_SERVER['HTTP_HOST'] === 'localhost' || $_SERVER['HTTP_HOST'] === '127.0.0.1';
}

function listVendor($category = null)
{
    $vendor = array(
        'PPOB' => array(
            'Digiflazz' => array(
                'free' => false
            ),
            'VIPayment' => array(
                'free' => false
            ),
        ),
        'SMM' => array(
            'BuzzerPanel' => array(
                'free' => false
            ),
            'MedanPedia' => array(
                'free' => false
            ),
            'IrvanKede' => array(
                'free' => false
            ),
            'DailyPanel' => array(
                'free' => false
            ),
            'WStore' => array(
                'free' => false
            ),
            'UNDRCTRL' => array(
                'free' => false
            ),
            'SosmedOnline' => array(
                'free' => false
            ),
            'SosmedOnlineVIP' => array(
                'free' => false
            ),
            'DjuraganSosmed' => array(
                'free' => false
            ),
            'SMMRaja' => array(
                'free' => false
            ),
            // 'Snow' => array(
            //     'free' => false
            // ),
            'SMMIllusion' => array(
                'free' => false
            ),
            'V1Pedia' => array(
                'free' => false
            )
        )
    );

    if ($category != null && isset($vendor[$category])) {
        return $vendor[$category];
    } else {
        return $vendor;
    }
}

function readConfig($filename, $key = null)
{
    $config = array();
    $lines = file($filename);

    foreach ($lines as $line) {
        $line = trim($line);
        if (empty($line) || $line[0] == '#') {
            continue; // skip empty or comment lines
        }

        $pos = strpos($line, '=');
        if ($pos === false) {
            continue; // skip lines without "=" separator
        }

        $key = trim(substr($line, 0, $pos));
        $value = trim(substr($line, $pos + 1));

        $config[$key] = $value;
    }

    if ($key != null && isset($config[$key])) {
        return $config[$key];
    } else {
        return $config;
    }
}

function changePrefixPhone($phone)
{
    $phone = str_replace(' ', '', $phone);
    $phone = str_replace('-', '', $phone);
    $phone = str_replace('+', '', $phone);

    if (substr($phone, 0, 1) == '0') {
        $phone = '62' . substr($phone, 1);
    }

    return $phone;
}

function searchCloudflare($string)
{
    if (strpos($string, 'ns.cloudflare.com') !== false) {
        return true;
    } else {
        return false;
    }
}

function sanitize_meta_description($str)
{
    // Remove any HTML tags
    $str = strip_tags($str);

    // Remove any line breaks or extra white space
    $str = preg_replace('/\s+/', ' ', $str);

    // Replace any non-alphanumeric characters with a space
    $str = preg_replace('/[^a-zA-Z0-9]+/', ' ', $str);

    // Trim any remaining whitespace
    $str = trim($str);

    // Return the sanitized string
    return $str;
}

function validate_schema($jsonld)
{
    // Make sure the JSON-LD object has the correct context
    if (!isset($jsonld['@context'])) {
        return false;
    }

    // Make sure the JSON-LD object has the required type
    if (!isset($jsonld['@type'])) {
        return false;
    }

    // If all validation checks pass, return true
    return true;
}

function strukContent()
{
    return array(
        'clientcode' => 'Kode Transaksi',
        'productname' => 'Nama Produk',
        'target' => 'Tujuan',
        'price' => 'Harga',
        'currentsaldo' => 'Saldo Sebelum',
        'aftercutsaldo' => 'Saldo Sesudah',
        'status' => 'Status',
        'sn' => 'SN',
        'startcount' => 'Start Count',
        'remain' => 'Remain',
        'qty' => 'Jumlah',
        'createddate' => 'Tanggal di Buat',
        'updateddate' => 'Tanggal di Perbarui',
    );
}

function parameterListNotification()
{
    return array(
        '${CODE}' => 'clientcode',
        '${TARGET}' => 'target',
        '${PRICE}' => 'price',
        '${BALANCE_BEFORE}' => 'currentsaldo',
        '${BALANCE_AFTER}' => 'aftercutsaldo',
        '${STATUS}' => 'status',
        '${SN}' => 'sn',
        '${PRODUCT}' => 'productname',
        '${QTY}' => 'qty',
    );
}

function parameterListNotificationDeposit()
{
    return array(
        '${PAYMENT}' => 'payment',
        '${AMOUNT}' => 'nominal',
        '${NOTE}' => 'note',
        '${CODE}' => 'code',
        '${PAYMENTTYPE}' => 'paymenttype',
        '${BONUS}' => 'nominalbonus',
        '${FEE}' => 'fee',
        '${STATUS}' => 'status'
    );
}

function parameterListEmailMarketing()
{
    return array(
        '${NAME}' => 'name',
        '${EMAIL}' => 'email',
        '${BALANCE}' => 'balance',
        '${USERNAME}' => 'username',
        '${PHONENUMBER}' => 'phonenumber',
        '${COMPANYNAME}' => 'companyname',
        '${COMPANYADDRESS}' => 'companyaddress',
        '${UNSUBSCRIBE_LINK}' => 'unsubscribe_link',
        '${HOOK_URL}' => 'hook_url'
    );
}

function replaceParameterNotificationDeposit($depositid, $userid, $notificationType = 'whatsapp')
{
    $CI = &get_instance();

    $deposit = $CI->db->select('a.*')
        ->get_where('deposit a', array(
            'a.id' => $depositid
        ))->row();

    if ($deposit != null) {
        $get = getCurrentUser($userid);

        $type = null;
        if (strtolower($deposit->status) == 'cancel' || strtolower($deposit->status) == 'failed' || strtolower($deposit->status) == 'expired') {
            $type = 'deposit_failed';
        } else if (strtolower($deposit->status) == 'pending') {
            $type = 'deposit_pending';
        } else if (strtolower($deposit->status) == 'success' || strtolower($deposit->status) == 'settlement' || strtolower($deposit->status) == 'sukses') {
            $type = 'deposit_success';
        }

        // Pilih kolom notifikasi berdasarkan tipe
        $notificationColumn = $notificationType == 'firebase' ? 'firebasenotification' : 'whatsappnotification';

        if ($get->$notificationColumn != null) {
            $notification_data = json_decode($get->$notificationColumn);

            if ($type != null && isset($notification_data->$type)) {
                $notification = $notification_data->$type;

                foreach (parameterListNotificationDeposit() as $key => $value) {
                    $notification = str_replace($key, strtoupper($deposit->$value ?? ''), $notification ?? '');
                }

                return $notification;
            }
        }
    }

    return null;
}

function replaceParameterEmailMarketing($content, $userid, $recipientid, $bypass_unsubscribe = false, $broadcast_id = null)
{
    $CI = &get_instance();
    $CI->load->model('MsUsers', 'msusers');
    $CI->load->model('MsEmailUnsubscribe', 'emailunsubscribe');

    // Get recipient data
    $recipient = $CI->msusers->get(array('id' => $recipientid))->row();

    if (!$recipient) {
        return $content;
    }

    // Get company data from merchant (user who sends the email)
    $merchant = $CI->msusers->get(array('id' => $userid))->row();

    if (!$merchant) {
        return $content;
    }

    // Generate unsubscribe token
    $unsubscribe_data = array(
        'userid' => $recipientid,
        'merchantid' => $userid,
        'timestamp' => time()
    );

    // Add broadcast_id if provided
    if ($broadcast_id !== null) {
        $unsubscribe_data['broadcast_id'] = $broadcast_id;
    }

    $unsubscribe_token = stringEncryption('encrypt', json_encode($unsubscribe_data));

    // Get merchant domain for unsubscribe link
    $merchant_domain = getMerchantDomain($userid);
    $unsubscribe_link = $merchant_domain . 'email-unsubscribe?token=' . urlencode($unsubscribe_token) . '&userid=' . stringEncryption('encrypt', $merchant->id);

    // Generate hook URL for click tracking
    $hook_data = array(
        'userid' => $recipientid,
        'merchantid' => $userid,
        'timestamp' => time()
    );

    // Add broadcast_id if provided
    if ($broadcast_id !== null) {
        $hook_data['broadcast_id'] = $broadcast_id;
    }

    $hook_token = stringEncryption('encrypt', json_encode($hook_data));
    $hook_url = $merchant_domain . 'email-hook/' . urlencode($hook_token) . '?userid=' . stringEncryption('encrypt', $merchant->id);

    // Replace parameters
    foreach (parameterListEmailMarketing() as $parameter => $field) {
        $value = '';

        switch ($field) {
            case 'name':
                $value = $recipient->name ?? '';
                break;
            case 'email':
                $value = $recipient->email ?? '';
                break;
            case 'balance':
                $value = 'Rp ' . number_format($recipient->balance ?? 0, 0, ',', '.');
                break;
            case 'username':
                $value = $recipient->username ?? '';
                break;
            case 'phonenumber':
                $value = $recipient->phonenumber ?? '';
                break;
            case 'companyname':
                $value = $merchant->name ?? '';
                break;
            case 'companyaddress':
                $value = $merchant->address ?? '';
                break;
            case 'unsubscribe_link':
                $value = $unsubscribe_link;
                break;
            case 'hook_url':
                $value = $hook_url;
                break;
        }

        $content = str_replace($parameter, $value, $content);
    }

    // Add automatic unsubscribe footer if not already present
    if (strpos($content, '${UNSUBSCRIBE_LINK}') === false && strpos(strtolower($content), 'unsubscribe') === false && $bypass_unsubscribe == false) {
        $unsubscribe_footer = '<hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">';
        $unsubscribe_footer .= '<div style="text-align: center; font-size: 12px; color: #666; margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">';
        $unsubscribe_footer .= '<p style="margin: 0 0 10px 0;"><strong>Informasi Email Marketing</strong></p>';
        $unsubscribe_footer .= '<p style="margin: 0 0 10px 0;">Email ini dikirim oleh <strong>' . ($merchant->companyname ?? 'Server PPOB & SMM') . '</strong> kepada <strong>' . ($recipient->email ?? '') . '</strong></p>';
        $unsubscribe_footer .= '<p style="margin: 0 0 10px 0;">Jika Anda tidak ingin menerima email marketing dari kami lagi, ';
        $unsubscribe_footer .= '<a href="' . $unsubscribe_link . '" style="color: #dc3545; text-decoration: underline; font-weight: bold;">klik di sini untuk berhenti berlangganan</a>.</p>';
        $unsubscribe_footer .= '<p style="margin: 0; font-size: 11px; color: #999;">Dengan berhenti berlangganan, Anda tidak akan menerima email promosi, penawaran, atau informasi produk dari kami. Anda masih dapat berlangganan kembali kapan saja.</p>';
        $unsubscribe_footer .= '</div>';

        $content .= $unsubscribe_footer;
    }

    return $content;
}

function replaceParameterNotification($orderid, $userid, $notificationType = 'whatsapp')
{
    $CI = &get_instance();

    $order = $CI->db->select('a.clientcode, a.target, a.price, a.currentsaldo, a.status, a.sn, b.productname, a.qty')
        ->join('msproduct b', 'a.serviceid = b.id')
        ->get_where('trorder a', array(
            'a.id' => $orderid
        ))->row();

    if ($order != null) {
        $get = getCurrentUser($userid);

        $type = null;
        if (strtolower($order->status) == 'error' || strtolower($order->status) == 'failed' || strtolower($order->status) == 'gagal') {
            $type = $notificationType == 'firebase' ? 'transaction_failed' : 'failed';
        } else if (strtolower($order->status) == 'pending') {
            $type = $notificationType == 'firebase' ? 'transaction_pending' : 'pending';
        } else if (strtolower($order->status) == 'completed' || strtolower($order->status) == 'success' || strtolower($order->status) == 'sukses') {
            $type = $notificationType == 'firebase' ? 'transaction_success' : 'success';
        }

        // Pilih kolom notifikasi berdasarkan tipe
        $notificationColumn = $notificationType == 'firebase' ? 'firebasenotification' : 'whatsappnotification';

        if ($get->$notificationColumn != null) {
            $notification_data = json_decode($get->$notificationColumn);

            if ($type != null && isset($notification_data->$type)) {
                $notification = $notification_data->$type;

                foreach (parameterListNotification() as $key => $value) {
                    if ($value != 'aftercutsaldo' && $value != 'currentsaldo' && $value != 'price' && $value != 'qty') {
                        $notification = str_replace($key, strtoupper($order->$value ?? ''), $notification ?? '');
                    } else {
                        if ($value == 'aftercutsaldo') {
                            $notification = str_replace($key, "Rp " . IDR($order->currentsaldo - $order->price), $notification);
                        } else if ($value == 'currentsaldo' || $value == 'price') {
                            $notification = str_replace($key, "Rp " . IDR($order->$value), $notification);
                        } else if ($value == 'qty') {
                            $notification = str_replace($key, number_format($order->$value ?? 0), $notification);
                        }
                    }
                }

                return $notification;
            }
        }
    }

    return null;
}

function getRoleid($where = array())
{
    $CI = &get_instance();

    $CI->db->select('a.roleid')
        ->from('msusers a')
        ->where('a.id', getCurrentIdUser());

    if (is_array($where) && count($where) > 0) {
        $CI->db->where($where);
    }

    return $CI->db->get()->row()->roleid;
}

// create function to generate random color
function generateRandomColor()
{
    // rgb or hex
    $type = 1;

    // set to random
    $red = rand(0, 255);
    $green = rand(0, 255);
    $blue = rand(0, 255);

    // convert to hex
    $red = dechex($red);
    $green = dechex($green);
    $blue = dechex($blue);

    // check if red is one character
    if (strlen($red) == 1) {
        $red = "0" . $red;
    }

    // check if green is one character
    if (strlen($green) == 1) {
        $green = "0" . $green;
    }

    // check if blue is one character
    if (strlen($blue) == 1) {
        $blue = "0" . $blue;
    }

    // check if user wants rgb
    if ($type == 0) {
        return "$red, $green, $blue";
    } else {
        return "#$red$green$blue";
    }
}

function buildApp($img_path, $img_url, $company_primary_color, $company_name, $company_email, $company_phone, $company_address, $company_base_address, $keystore_path, $keystore_alias, $app_package_name, $customappid, $applicationtype)
{
    $curl = curl_init();

    $params = array(
        'img_path' => $img_path,
        'img_url' => $img_url,
        'company_primary_color' => $company_primary_color,
        'company_name' => $company_name,
        'company_email' => $company_email,
        'company_phone' => $company_phone,
        'company_address' => $company_address,
        'company_base_address' => $company_base_address,
        'keystore_path' => $keystore_path,
        'keystore_alias' => $keystore_alias,
        'app_package_name' => $app_package_name,
        'customappid' => $customappid,
        'applicationtype' => $applicationtype
    );

    curl_setopt_array($curl, array(
        CURLOPT_URL => 'https://2b3e-114-5-223-65.ngrok-free.app/build-app',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_ENCODING => '',
        CURLOPT_MAXREDIRS => 10,
        CURLOPT_TIMEOUT => 0,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => json_encode($params),
        CURLOPT_HTTPHEADER => array(
            'Content-Type: application/json'
        ),
    ));

    $response = curl_exec($curl);
    curl_close($curl);

    return json_decode($response);
}

function pullTransaction($target)
{
    try {
        $ch = curl_init();

        curl_setopt($ch, CURLOPT_URL, "https://socket.server-ppobsmm.com/realtime/transaction/refresh");
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query(array(
            'targetuserid' => stringEncryption('encrypt', $target)
        )));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);

        $result = curl_exec($ch);
        curl_close($ch);

        return json_decode($result);
    } catch (Exception $ex) {
        return null;
    }
}

function tgl_indo($tanggal)
{
    $bulan = array(
        'Januari',
        'Februari',
        'Maret',
        'April',
        'Mei',
        'Juni',
        'Juli',
        'Agustus',
        'September',
        'Oktober',
        'November',
        'Desember'
    );

    $pecahkan = explode('-', $tanggal);

    if (isset($pecahkan[0]) && isset($pecahkan[1]) && isset($pecahkan[2])) {
        return $pecahkan[2] . ' ' . $bulan[(int)$pecahkan[1] - 1] . ' ' . $pecahkan[0];
    } else {
        return date('d F Y', strtotime($tanggal));
    }
}

function sendNotificationToApps($title, $body, $deviceid, $data = null)
{
    try {
        $factory = (new Factory)->withServiceAccount(APPPATH . 'config/firebase.json');
        $messaging = $factory->createMessaging();

        // Create the notification
        $notification = Notification::create($title, $body);

        // Create the message with target
        $message = CloudMessage::withTarget('token', $deviceid)
            ->withNotification($notification);

        // Add custom data if provided
        if ($data !== null) {
            $message = $message->withData($data);
        }

        // Send the message
        $send = $messaging->send($message);

        return $send;
    } catch (InvalidMessage $e) {
        return $e->getMessage();
    } catch (\Throwable $e) {
        return $e->getMessage();
    }
}

function sendFirebaseNotificationOrder($orderid, $userid)
{
    try {
        $CI = &get_instance();

        // 1. Cek transaksi tersebut dilakukan melalui web/apps (ambil dari trorder kolom orderplatform)
        $order = $CI->db->select('orderplatform, status')
            ->get_where('trorder', array('id' => $orderid))
            ->row();

        if ($order == null) {
            return false;
        }

        // 2. Jika transaksi tersebut dilakukan melalui apps
        if (strtolower($order->orderplatform) == 'apps') {
            // 3. Cari sesi mobile pembeli tersebut (ambil dari mobilesession)
            $mobileSession = $CI->db->select('fcm_token')
                ->get_where('mobilesession', array(
                    'userid' => $userid,
                ))
                ->row();

            // 4. Jika di mobile session terdapat sesi yang aktif dan ada fcm_token
            if ($mobileSession != null && !empty($mobileSession->fcm_token)) {
                // Tentukan title berdasarkan status
                $title = '';
                if (strtolower($order->status) == 'error' || strtolower($order->status) == 'failed' || strtolower($order->status) == 'gagal') {
                    $title = 'Transaksi Gagal';
                } else if (strtolower($order->status) == 'pending') {
                    $title = 'Transaksi Sedang di Proses';
                } else if (strtolower($order->status) == 'completed' || strtolower($order->status) == 'success' || strtolower($order->status) == 'sukses') {
                    $title = 'Transaksi Berhasil';
                }

                // Ambil template dari replaceParameterNotification dengan type firebase
                $messageBody = replaceParameterNotification($orderid, $userid, 'firebase');

                if (!empty($title) && !empty($messageBody)) {
                    // Panggil function sendNotificationToApps
                    $result = sendNotificationToApps($title, $messageBody, $mobileSession->fcm_token);
                    return $result;
                }
            }
        }

        return false;
    } catch (Exception $ex) {
        return false;
    }
}

function sendFirebaseNotificationDeposit($depositid, $userid)
{
    try {
        $CI = &get_instance();

        // 1. Cek deposit tersebut dilakukan melalui web/apps (ambil dari deposit kolom depositplatform)
        $deposit = $CI->db->select('depositplatform, status')
            ->get_where('deposit', array('id' => $depositid))
            ->row();

        if ($deposit == null) {
            return false;
        }

        // 2. Jika deposit tersebut dilakukan melalui apps
        if (strtolower($deposit->depositplatform) == 'apps') {
            // 3. Cari sesi mobile pembeli tersebut (ambil dari mobilesession)
            $mobileSession = $CI->db->select('fcm_token')
                ->get_where('mobilesession', array(
                    'userid' => $userid,
                ))
                ->row();

            // 4. Jika di mobile session terdapat sesi yang aktif dan ada fcm_token
            if ($mobileSession != null && !empty($mobileSession->fcm_token)) {
                // Tentukan title berdasarkan status
                $title = '';
                if (strtolower($deposit->status) == 'cancel' || strtolower($deposit->status) == 'failed' || strtolower($deposit->status) == 'expired') {
                    $title = 'Deposit Gagal';
                } else if (strtolower($deposit->status) == 'pending') {
                    $title = 'Deposit Sedang di Proses';
                } else if (strtolower($deposit->status) == 'success' || strtolower($deposit->status) == 'settlement' || strtolower($deposit->status) == 'sukses') {
                    $title = 'Deposit Berhasil';
                }

                // Ambil template dari replaceParameterNotificationDeposit dengan type firebase
                $messageBody = replaceParameterNotificationDeposit($depositid, $userid, 'firebase');

                if (!empty($title) && !empty($messageBody)) {
                    // Panggil function sendNotificationToApps
                    $result = sendNotificationToApps($title, $messageBody, $mobileSession->fcm_token);
                    return $result;
                }
            }
        }

        return false;
    } catch (Exception $ex) {
        return false;
    }
}
