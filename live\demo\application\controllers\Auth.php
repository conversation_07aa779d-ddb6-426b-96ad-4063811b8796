<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsUsers $msusers
 * @property EmailRequestVerification $emailrequestverification
 * @property ForgotPassword $forgotpassword
 * @property LoginActivity $loginactivity
 * @property CI_DB_mysqli_driver $db
 * @property HistoryLogin $historylogin
 */
class Auth extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsUsers', 'msusers');
        $this->load->model('EmailRequestVerification', 'emailrequestverification');
        $this->load->model('ForgotPassword', 'forgotpassword');
        $this->load->model('LoginActivity', 'loginactivity');
        $this->load->model('HistoryLogin', 'historylogin');
    }

    public function login()
    {
        if (isLogin()) {
            if (getCurrentThemeConfiguration($this->merchant->id) == 'Fin-App') {
                return redirect(base_url('?userid=' . stringEncryption('encrypt', $this->merchant->id)));
            } else {
                return redirect(base_url('dashboard?userid=' . stringEncryption('encrypt', $this->merchant->id)));
            }
        }

        $success = removeSymbol(getGet('success'));
        $failed = removeSymbol(getGet('failed'));

        $data = array();
        $data['success'] = $success;
        $data['failed'] = $failed;
        $data['back'] = base_url('?userid=' . stringEncryption('encrypt', $this->merchant->id));

        if (getCurrentThemeConfiguration($this->merchant->id) == 'Sobat-Serverppob') {
            $data['content'] = 'auth/login';
            $data['theme'] = (getThemeConfiguration('Sobat-Serverppob', $this->merchant->id) ?? null) != null ? json_decode(getThemeConfiguration('Sobat-Serverppob', $this->merchant->id)->themeconfig ?? null) : null;
            return viewTemplate($this->merchant->id, "master", $data);
        }

        return viewTemplate($this->merchant->id, "auth/login", $data);
    }

    public function process_login()
    {
        try {
            $this->db->trans_begin();

            if (isLogin()) {
                throw new Exception('Anda telah login');
            }

            if (!get_recaptcha_response()) {
                throw new Exception('Silahkan konfirmasi bahwa anda bukanlah Robot');
            }

            $emailusername = getPost('emailusername');
            $password = getPost('password');

            if ($emailusername == null) {
                throw new Exception('Email Atau Username wajib diisi');
            } else if ($password == null) {
                throw new Exception('Password wajib diisi');
            }

            $get = $this->msusers->get(array(
                "(email = '$emailusername' OR username = '$emailusername') =" => true,
                'merchantid' => $this->merchant->id
            ));

            $row = null;

            if ($get->num_rows() == 0) {
                throw new Exception('Email atau Username yang anda masukkan tidak terdaftar');
            } else {
                $row = $get->row();
            }

            if (!password_verify($password, $row->password)) {
                throw new Exception('Password yang anda masukkan salah');
            }

            if ($this->merchant->isregisterwithoutverification != 1) {
                if ($row->isemailverified != 1) {
                    throw new Exception('Email anda belum terverifikasi, Harap melakukan verifikasi email terlebih dahulu');
                }
            }

            if ($row->isdeleted == 1) {
                throw new Exception('Akun anda telah dihapus oleh admin');
            }

            $this->loginactivity->insert(array(
                'userid' => $row->id,
                'ipaddress' => getIPAddress(),
                'useragent' => getUserAgent(),
                'createddate' => getCurrentDate()
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Login gagal');
            }
            
            // langsung insert ke tabel historylogin
            $this->db->insert('historylogin', [
                'userid'      => $row->id,
                'ipaddress'   => getIPAddress(),
                'useragent'   => getUserAgent(),
                'createddate' => getCurrentDate(),
            ]);


            $this->db->trans_commit();

            setSessionValue(array(
                'ISLOGIN' => true,
                'USERID' => $row->id,
                'EMAIL' => $row->email,
                'NAME' => $row->name,
                'CREATEDDATE' => $row->createddate
            ));

            return JSONResponseDefault('OK', 'Login berhasil');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function register()
    {
        if (isLogin()) {
            return redirect(base_url('dashboard?userid=' . stringEncryption('encrypt', $this->merchant->id)));
        }

        $data = array();
        $data['back'] = base_url('auth/login?userid=' . stringEncryption('encrypt', $this->merchant->id));

        $privacypolicy = null;
        if (file_exists('./../../application/templates/privacypolicy_member.txt')) {
            $privacypolicy = file_get_contents('./../../application/templates/privacypolicy_member.txt');
        }

        $data['privacypolicy'] = str_replace('${companyname}', $this->merchant->companyname, $privacypolicy);

        if (getCurrentThemeConfiguration($this->merchant->id) == 'Sobat-Serverppob') {
            $data['content'] = 'auth/register';
            $data['theme'] = (getThemeConfiguration('Sobat-Serverppob', $this->merchant->id) ?? null) != null ? json_decode(getThemeConfiguration('Sobat-Serverppob', $this->merchant->id)->themeconfig ?? null) : null;
            return viewTemplate($this->merchant->id, "master", $data);
        }

        return viewTemplate($this->merchant->id, "auth/register", $data);
    }

    public function process_register()
    {
        try {
            $this->db->trans_begin();

            if (!get_recaptcha_response()) {
                throw new Exception('Silahkan konfirmasi bahwa anda bukanlah Robot');
            }

            $nama = getPost('nama');
            $username = getPost('username');
            $email = getPost('email');
            $password = getPost('password');
            $konfirmasipassword = getPost('konfirmasi_password');
            $pin = getPost('pin');
            $wa = getPost('wa');

            if ($nama == null) {
                throw new Exception('Nama wajib diisi');
            } else if ($username == null) {
                throw new Exception('Username wajib diisi');
            } else if (strlen($username) < 4) {
                throw new Exception('Username minimal 4 karakter');
            } else if (strlen($username) > 20) {
                throw new Exception('Username maksimal 20 karakter');
            } else if ($email == null) {
                throw new Exception('Email wajib diisi');
            } else if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Email yang anda masukkan tidak valid');
            } else if ($password == null) {
                throw new Exception('Password wajib diisi');
            } else if ($konfirmasipassword == null) {
                throw new Exception('Konfirmasi Password wajib diisi');
            } else if ($pin == null) {
                throw new Exception('PIN Transaksi wajib diisi');
            } else if (!is_numeric($pin)) {
                throw new Exception('PIN Transaksi harus berupa angka');
            } else if (strlen($pin) != 6) {
                throw new Exception('PIN Transaksi harus berjumlah 6 digit');
            } else {
                $nama = removeSymbol($nama);
                $username = removeSymbol($username);
            }

            if ($wa != null && !is_numeric($wa)) {
                throw new Exception('Nomor WhatsApp harus berupa angka');
            }

            if ($this->merchant->licenseid == null) {
                $get = $this->msusers->total(array(
                    'merchantid' => $this->merchant->id
                ));

                if ($get >= 5) {
                    throw new Exception('Maaf, Anda telah mencapai batas maksimal pengguna');
                }
            }

            $get = $this->msusers->total(array(
                'email' => $email,
                'merchantid' => $this->merchant->id,
            ));

            $getusername = $this->msusers->total(array(
                'username' => $username,
                'merchantid' => $this->merchant->id,
            ));

            if ($getusername > 0) {
                throw new Exception('Username yang anda masukkan telah terdaftar');
            }

            if ($get > 0) {
                throw new Exception('Email yang anda masukkan telah terdaftar');
            }

            if ($password != $konfirmasipassword) {
                throw new Exception('Password yang anda masukkan tidak sesuai');
            }

            $insert = array();
            $insert['name'] = $nama;
            $insert['username'] = $username;
            $insert['email'] = $email;
            $insert['password'] = password_hash($password, PASSWORD_DEFAULT);
            $insert['role'] = 'User';
            $insert['merchantid'] = $this->merchant->id;
            $insert['pin'] = stringEncryption('encrypt', $pin);
            $insert['phonenumber'] = $wa;
            $insert['is_kyc'] = 0;

            $this->msusers->insert($insert);
            $userid = $this->db->insert_id();

            $getverif = $this->msusers->get(array(
                'id' => $this->merchant->id
            ))->row();

            if ($getverif->isregisterwithoutverification != 1) {
                $smtpemailconfig = json_decode(stringEncryption('decrypt', $this->merchant->smtpemailconfig));

                if ($smtpemailconfig != null) {
                    $sendMail = sendMail($smtpemailconfig->host, $smtpemailconfig->username, $smtpemailconfig->password, $smtpemailconfig->port, $email, $this->merchant->companyname, 'Verifikasi Akun ' . $this->merchant->companyname, viewTemplate($this->merchant->id, 'auth/register/verification', array(
                        'hash' => stringEncryption('encrypt', $userid)
                    ), true));

                    if ($sendMail == false) {
                        log_message_user('error', '[SMTP EMAIL] Gagal mengirimkan kode verifikasi menggunakan akun SMTP anda, Trigger Back menggunakan akun SMTP bawaan Server PPOB & SMM', $this->merchant->id);

                        $sendMail = sendMail(String_Helper::SMTP_HOST, String_Helper::SMTP_USERNAME, String_Helper::SMTP_PASSWORD, String_Helper::SMTP_PORT, $email, $this->merchant->companyname, 'Verifikasi Akun ' . $this->merchant->companyname, viewTemplate($this->merchant->id, 'auth/register/verification', array(
                            'hash' => stringEncryption('encrypt', $userid)
                        ), true));
                    }
                } else {
                    $sendMail = sendMail(String_Helper::SMTP_HOST, String_Helper::SMTP_USERNAME, String_Helper::SMTP_PASSWORD, String_Helper::SMTP_PORT, $email, $this->merchant->companyname, 'Verifikasi Akun ' . $this->merchant->companyname, viewTemplate($this->merchant->id, 'auth/register/verification', array(
                        'hash' => stringEncryption('encrypt', $userid)
                    ), true));
                }

                if ($sendMail == false) {
                    throw new Exception('Pendaftaran gagal');
                }
            } else {
                $this->msusers->update(array(
                    'id' => $userid
                ), array(
                    'isemailverified' => '1'
                ));
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Pendaftaran gagal');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Pendaftaran berhasil');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function logout()
    {
        destroySession();

        return redirect(base_url('auth/login?userid=' . stringEncryption('encrypt', $this->merchant->id)));
    }

    public function verification()
    {
        $hash = getGet('hash');

        $get = $this->msusers->total(array(
            'id' => stringEncryption('decrypt', $hash),
            'merchantid' => $this->merchant->id,
            'isemailverified' => null
        ));

        if ($get == 0) {
            return redirect(base_url('auth/login?failed=Verifikasi tidak valid&userid=' . $this->userid));
        }

        $update = array();
        $update['isemailverified'] = 1;

        $this->msusers->update(array(
            'id' => stringEncryption('decrypt', $hash)
        ), $update);

        return redirect(base_url('auth/login?success=Verifikasi berhasil&userid=' . $this->userid));
    }

    public function request_verification()
    {
        if ($this->merchant->isregisterwithoutverification == 1) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        if (isLogin()) {
            return redirect(base_url('dashboard?userid=' . stringEncryption('encrypt', $this->merchant->id)));
        }

        $data = array();
        $data['back'] = base_url('auth/login?userid=' . $this->userid);

        if (getCurrentThemeConfiguration($this->merchant->id) == 'Sobat-Serverppob') {
            $data['content'] = 'auth/request_verification';
            $data['theme'] = (getThemeConfiguration('Sobat-Serverppob', $this->merchant->id) ?? null) != null ? json_decode(getThemeConfiguration('Sobat-Serverppob', $this->merchant->id)->themeconfig ?? null) : null;
            return viewTemplate($this->merchant->id, "master", $data);
        }

        return viewTemplate($this->merchant->id, "auth/request_verification", $data);
    }

    public function process_request_verification()
    {
        try {
            $this->db->trans_begin();

            if ($this->merchant->isregisterwithoutverification == 1) {
                throw new Exception('Verifikasi tidak diperlukan');
            }

            if (!get_recaptcha_response()) {
                throw new Exception('Silahkan konfirmasi bahwa anda bukanlah Robot');
            }

            $email = getPost('email');

            if ($email == null) {
                throw new Exception('Email wajib diisi');
            } else if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Email yang anda masukkan tidak valid');
            }

            $get = $this->msusers->get(array(
                'email' => $email,
                'merchantid' => $this->merchant->id,
                'isemailverified' => null
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Email yang anda masukkan tidak terdaftar / sudah terverifikasi');
            }

            $row = $get->row();

            if ($row->isdeleted == 1) {
                throw new Exception('Akun anda telah dihapus oleh admin');
            }

            $spam = $this->emailrequestverification->order_by('a.createddate', 'DESC')->get(array(
                'userid' => $row->id,
                'DATE(createddate) =' => getCurrentDate('Y-m-d')
            ));

            if ($spam->num_rows() > 0) {
                $spamRow = $spam->row();

                $createddate = $spamRow->createddate;
                $tenminutes = date('Y-m-d H:i:s', strtotime("$createddate +10 minutes"));

                if (getCurrentDate() < $tenminutes) {
                    throw new Exception('Anda tidak dapat meminta kode verifikasi hingga ' . date('d F Y H:i:s', strtotime($tenminutes)));
                }
            }

            $insert = array();
            $insert['userid'] = $row->id;

            $this->emailrequestverification->insert($insert);

            $smtpemailconfig = json_decode(stringEncryption('decrypt', $this->merchant->smtpemailconfig));

            if ($smtpemailconfig != null) {
                $sendMail = sendMail($smtpemailconfig->host, $smtpemailconfig->username, $smtpemailconfig->password, $smtpemailconfig->port, $email, $this->merchant->companyname, 'Verifikasi Akun ' . $this->merchant->companyname, viewTemplate($this->merchant->id, 'auth/register/verification', array(
                    'hash' => stringEncryption('encrypt', $row->id)
                ), true));

                if ($sendMail == false) {
                    log_message_user('error', '[SMTP EMAIL] Gagal mengirimkan kode verifikasi menggunakan akun SMTP anda, Trigger Back menggunakan akun SMTP bawaan Server PPOB & SMM', $this->merchant->id);

                    $sendMail = sendMail(String_Helper::SMTP_HOST, String_Helper::SMTP_USERNAME, String_Helper::SMTP_PASSWORD, String_Helper::SMTP_PORT, $email, $this->merchant->companyname, 'Verifikasi Akun ' . $this->merchant->companyname, viewTemplate($this->merchant->id, 'auth/register/verification', array(
                        'hash' => stringEncryption('encrypt', $row->id)
                    ), true));
                }
            } else {
                $sendMail = sendMail(String_Helper::SMTP_HOST, String_Helper::SMTP_USERNAME, String_Helper::SMTP_PASSWORD, String_Helper::SMTP_PORT, $email, $this->merchant->companyname, 'Verifikasi Akun ' . $this->merchant->companyname, viewTemplate($this->merchant->id, 'auth/register/verification', array(
                    'hash' => stringEncryption('encrypt', $row->id)
                ), true));
            }

            if ($sendMail == false) {
                throw new Exception('Permintaan gagal');
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Permintaan gagal');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Permintaan berhasil, Silahkan cek email anda');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function forgot_password()
    {
        if (isLogin()) {
            return redirect(base_url('dashboard?userid=' . $this->userid));
        }

        $data = array();
        $data['back'] = base_url('auth/login?userid=' . $this->userid);

        if (getCurrentThemeConfiguration($this->merchant->id) == 'Sobat-Serverppob') {
            $data['content'] = 'auth/forgot_password';
            $data['theme'] = (getThemeConfiguration('Sobat-Serverppob', $this->merchant->id) ?? null) != null ? json_decode(getThemeConfiguration('Sobat-Serverppob', $this->merchant->id)->themeconfig ?? null) : null;
            return viewTemplate($this->merchant->id, "master", $data);
        }

        return viewTemplate($this->merchant->id, "auth/forgot_password", $data);
    }

    public function process_forgot_password()
    {
        try {
            $this->db->trans_begin();

            if (!get_recaptcha_response()) {
                throw new Exception('Silahkan konfirmasi bahwa anda bukanlah Robot');
            }

            $email = getPost('email');

            if ($email == null) {
                throw new Exception('Email wajib diisi');
            } else if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Email yang anda masukkan tidak valid');
            }

            $get = $this->msusers->get(array(
                'email' => $email,
                'merchantid' => $this->merchant->id,
                'isdeleted' => null
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Email yang anda masukkan tidak terdaftar');
            }

            $row = $get->row();

            $get = $this->forgotpassword->get(array(
                'userid' => $row->id
            ));

            if ($get->num_rows() > 0) {
                $row = $get->row();

                $tenminutes = date('Y-m-d H:i:s', strtotime("$row->createddate +10 minutes"));

                if (getCurrentDate() < $tenminutes) {
                    throw new Exception('Anda tidak dapat meminta link reset password hingga ' . date('d F Y H:i:s', strtotime($tenminutes)));
                } else {
                    $this->forgotpassword->delete(array(
                        'id' => $row->id
                    ));
                }
            }

            $request = generateTransactionNumber('FORGOT');
            $hash = stringEncryption('encrypt', $request);

            $insert = array();
            $insert['userid'] = $row->id;
            $insert['hash'] = $hash;

            $this->forgotpassword->insert($insert);

            $smtpemailconfig = json_decode(stringEncryption('decrypt', $this->merchant->smtpemailconfig));

            if ($smtpemailconfig != null) {
                $sendMail = sendMail($smtpemailconfig->host, $smtpemailconfig->username, $smtpemailconfig->password, $smtpemailconfig->port, $email, $this->merchant->companyname, 'Reset Password ' . $this->merchant->companyname, viewTemplate($this->merchant->id, 'auth/forgot_password/verification', array(
                    'hash' => $hash
                ), true));

                if ($sendMail == false) {
                    log_message_user('error', '[SMTP EMAIL] Gagal mengirimkan kode verifikasi menggunakan akun SMTP anda, Trigger Back menggunakan akun SMTP bawaan Server PPOB & SMM', $this->merchant->id);

                    $sendMail = sendMail(String_Helper::SMTP_HOST, String_Helper::SMTP_USERNAME, String_Helper::SMTP_PASSWORD, String_Helper::SMTP_PORT, $email, $this->merchant->companyname, 'Reset Password ' . $this->merchant->companyname, viewTemplate($this->merchant->id, 'auth/forgot_password/verification', array(
                        'hash' => $hash
                    ), true));
                }
            } else {
                $sendMail = sendMail(String_Helper::SMTP_HOST, String_Helper::SMTP_USERNAME, String_Helper::SMTP_PASSWORD, String_Helper::SMTP_PORT, $email, $this->merchant->companyname, 'Reset Password ' . $this->merchant->companyname, viewTemplate($this->merchant->id, 'auth/forgot_password/verification', array(
                    'hash' => $hash
                ), true));
            }

            if ($sendMail == false) {
                throw new Exception('Permintaan gagal');
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Permintaan gagal');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Link Reset Password telah dikirim ke alamat email anda');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function check_email_forgot_password()
    {
        if (isLogin()) {
            return redirect(base_url('dashboard?userid=' . $this->userid));
        }

        $data = array();

        if (getCurrentThemeConfiguration($this->merchant->id) == 'Sobat-Serverppob') {
            $data['content'] = 'auth/forgot_password/checkemail';
            $data['theme'] = (getThemeConfiguration('Sobat-Serverppob', $this->merchant->id) ?? null) != null ? json_decode(getThemeConfiguration('Sobat-Serverppob', $this->merchant->id)->themeconfig ?? null) : null;
            return viewTemplate($this->merchant->id, "master", $data);
        }
    }

    public function reset()
    {
        $hash = getGet('hash');

        if ($hash == null) {
            return redirect(base_url('auth/login?failed=Verifikasi tidak valid&userid=' . $this->userid));
        }

        $get = $this->forgotpassword->get(array(
            'hash' => $hash
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('auth/login?failed=Verifikasi tidak valid&userid=' . $this->userid));
        }

        $row = $get->row();

        $data = array();
        $data['user'] = $row;
        $data['hash'] = $hash;

        if (getCurrentThemeConfiguration($this->merchant->id) == 'Sobat-Serverppob') {
            $data['content'] = 'auth/forgot_password/reset';
            $data['theme'] = (getThemeConfiguration('Sobat-Serverppob', $this->merchant->id) ?? null) != null ? json_decode(getThemeConfiguration('Sobat-Serverppob', $this->merchant->id)->themeconfig ?? null) : null;
            return viewTemplate($this->merchant->id, "master", $data);
        }

        return viewTemplate($this->merchant->id, "auth/forgot_password/reset", $data);
    }

    public function process_reset_password()
    {
        try {
            $this->db->trans_begin();

            $id = getPost('id');
            $hash = getPost('hash');
            $password = getPost('password');
            $confirmpassword = getPost('confirmpassword');

            if ($id == null || $hash == null) {
                throw new Exception('Verifikasi tidak valid');
            } else if ($password == null) {
                throw new Exception('Password Baru wajib diisi');
            } else if ($confirmpassword == null) {
                throw new Exception('Konfirmasi Password Baru wajib diisi');
            } else if ($password != $confirmpassword) {
                throw new Exception('Konfirmasi Password Baru tidak sesuai');
            } else {
                $id = stringEncryption('decrypt', $id);
            }

            $get = $this->forgotpassword->get(array(
                'id' => $id,
                'hash' => $hash
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Reset Password tidak valid');
            }

            $row = $get->row();

            $update = array();
            $update['password'] = password_hash($password, PASSWORD_DEFAULT);

            $this->msusers->update(array(
                'id' => $row->userid
            ), $update);

            $this->forgotpassword->delete(array(
                'id' => $id
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Password gagal direset');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Password berhasil direset');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}