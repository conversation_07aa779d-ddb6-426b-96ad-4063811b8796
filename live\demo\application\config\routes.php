<?php
defined('BASEPATH') or exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| URI ROUTING
| -------------------------------------------------------------------------
| This file lets you re-map URI requests to specific controller functions.
|
| Typically there is a one-to-one relationship between a URL string
| and its corresponding controller class/method. The segments in a
| URL normally follow this pattern:
|
|	example.com/class/method/id/
|
| In some instances, however, you may want to remap this relationship
| so that a different class/function is called than the one
| corresponding to the URL.
|
| Please see the user guide for complete details:
|
|	https://codeigniter.com/userguide3/general/routing.html
|
| -------------------------------------------------------------------------
| RESERVED ROUTES
| -------------------------------------------------------------------------
|
| There are three reserved routes:
|
|	$route['default_controller'] = 'welcome';
|
| This route indicates which controller class should be loaded if the
| URI contains no data. In the above example, the "welcome" class
| would be loaded.
|
|	$route['404_override'] = 'errors/page_missing';
|
| This route will tell the Router which controller/method to use if those
| provided in the URL cannot be matched to a valid route.
|
|	$route['translate_uri_dashes'] = FALSE;
|
| This is not exactly a route, but allows you to automatically route
| controller and method names that contain dashes. '-' isn't a valid
| class or method name character, so it requires translation.
| When you set this option to TRUE, it will replace ALL dashes in the
| controller and method URI segments.
|
| Examples:	my-controller/index	-> my_controller/index
|		my-controller/my-method	-> my_controller/my_method
*/
$route['default_controller'] = 'Landing';
$route['404_override'] = 'Page404';
$route['translate_uri_dashes'] = FALSE;

$route['dashboard'] = 'Dashboard';
$route['dashboard/news'] = 'Dashboard/news';
$route['dashboard/ppoborder'] = 'Dashboard/order_ppob';
$route['dashboard/ppoborder/fields'] = 'Dashboard/fields_ppob';
$route['dashboard/ppoborder/process'] = 'Dashboard/process_order_ppob';
$route['dashboard/smmorder'] = 'Dashboard/order_smm';
$route['dashboard/smmorder/process'] = 'Dashboard/process_order_smm';

$route['maintenance'] = 'Landing/maintenance';
$route['privacypolicy'] = 'Landing/privacypolicy';
$route['services/digital'] = 'Landing/digital';
$route['services/digital/datatables'] = 'Landing/datatables_digital';
$route['services/smm'] = 'Landing/smm';
$route['services/smm/datatables'] = 'Landing/datatables_smm';

// App Pages for Mobile WebView
$route['app/about'] = 'AppPages/about';
$route['app/terms'] = 'AppPages/terms';
$route['app/privacy'] = 'AppPages/privacy';

$route['cooperation/expired'] = 'Cooperation/expired';

$route['auth/login'] = 'Auth/login';
$route['auth/login/process'] = 'Auth/process_login';

$route['auth/register'] = 'Auth/register';
$route['auth/register/process'] = 'Auth/process_register';

$route['auth/verification'] = 'Auth/verification';

$route['auth/reset'] = 'Auth/reset';
$route['auth/reset/process'] = 'Auth/process_reset_password';

$route['auth/request/verification'] = 'Auth/request_verification';
$route['auth/request/verification/process'] = 'Auth/process_request_verification';

$route['auth/forgot/password'] = 'Auth/forgot_password';
$route['auth/forgot/password/process'] = 'Auth/process_forgot_password';
$route['auth/forgot/password/checkemail'] = 'Auth/check_email_forgot_password';

$route['auth/logout'] = 'Auth/logout';

$route['account/change'] = 'Account/change';
$route['account/change/process'] = 'Account/process_change';

$route['account/verification'] = 'Account/verification';
$route['account/verification/process'] = 'Account/process_verificationkyc';

$route['secure/kyc/image/(:any)/(:any)'] = 'Secure/kyc_image/$1/$2';

$route['account/password'] = 'Account/password';
$route['account/password/process'] = 'Account/process_password';

$route['account/pin'] = 'Account/pin';
$route['account/pin/process'] = 'Account/process_pin';

$route['account/history/login'] = 'Account/history_login';
$route['account/history/login/datatables'] = 'Account/datatables_history_login';

$route['account/history/balance'] = 'Account/history_balance';
$route['account/history/balance/datatables'] = 'Account/datatables_history_balance';

$route['account/contact'] = 'Contact';
$route['account/contact/datatables'] = 'Contact/datatables_contact';
$route['account/contact/add'] = 'Contact/add';
$route['account/contact/add/process'] = 'Contact/process_add';
$route['account/contact/edit/(:any)'] = 'Contact/edit/$1';
$route['account/contact/edit/(:any)/process'] = 'Contact/process_edit/$1';
$route['account/contact/delete'] = 'Contact/process_delete';

$route['ppob/prabayar'] = 'PPOB/prabayar';
$route['ppob/prabayar/fields'] = 'PPOB/fields_prabayar';
$route['ppob/prabayarmassal'] = 'PPOB/prabayar_massal';
$route['ppob/prabayarmassal/form'] = 'PPOB/form_prabayar';
$route['ppob/prabayarmassal/fields'] = 'PPOB/fields_prabayar_massal';
$route['ppob/prabayarmassal/process'] = 'PPOB/process_prabayar_massal';
$route['ppob/prabayar/modalcontact'] = 'PPOB/modalContact';
$route['ppob/prabayar/process'] = 'PPOB/process_prabayar';
$route['ppob/history'] = 'PPOB/history';
$route['ppob/history/datatables'] = 'PPOB/history_datatables_ppob';
$route['ppob/services'] = 'PPOB/services';
$route['ppob/services/datatables'] = 'PPOB/datatables_services_ppob';
$route['ppob/services/favourite/add'] = 'PPOB/add_favourite_services';
$route['ppob/services/favourite/remove'] = 'PPOB/remove_favourite_services';
$route['ppob/services/export'] = 'PPOB/export_services_ppob';
$route['ppob/pascabayar'] = 'PPOB/pascabayar';
$route['ppob/pascabayar/cek'] = 'PPOB/cek_tagihan';
$route['ppob/pascabayar/bayar'] = 'PPOB/bayar_tagihan';
$route['ppob/pascabayar/modalcontact'] = 'PPOB/modalContactPascabayar';
$route['ppob/report'] = 'PPOB/report';
$route['ppob/report/filter'] = 'PPOB/filter_report';
$route['ppob/print/(:any)'] = 'PPOB/print/$1';
$route['ppob/print/(:any)/process'] = 'PPOB/process_print/$1';
$route['ppob/favourite'] = 'PPOB/favourite';
$route['ppob/favourite/datatables'] = 'PPOB/datatables_favourite_ppob';
$route['ppob/favourite/delete'] = 'PPOB/delete_favourite_ppob';
$route['ppob/information'] = 'PPOB/information';
$route['ppob/information/datatables'] = 'PPOB/datatables_information_ppob';

$route['smm/order'] = 'SMM/order';
$route['smm/order/process'] = 'SMM/process_order';
$route['smm/order/modalcontact'] = 'SMM/modalContact';
$route['smm/ordermassal'] = 'SMM/order_massal';
$route['smm/ordermassal/form'] = 'SMM/form_order';
$route['smm/ordermassal/process'] = 'SMM/process_order_massal';
$route['smm/history'] = 'SMM/history';
$route['smm/history/datatables'] = 'SMM/datatables_history_smm';
$route['smm/services'] = 'SMM/services';
$route['smm/services/datatables'] = 'SMM/datatables_services_smm';
$route['smm/services/favourite/add'] = 'SMM/add_favourite_services';
$route['smm/services/favourite/remove'] = 'SMM/remove_favourite_services';
$route['smm/services/export'] = 'SMM/export_services_smm';
$route['smm/report'] = 'SMM/report';
$route['smm/report/filter'] = 'SMM/filter_report';
$route['smm/print/(:any)'] = 'SMM/print/$1';
$route['smm/print/(:any)/process'] = 'SMM/process_print/$1';
$route['smm/favourite'] = 'SMM/favourite';
$route['smm/favourite/datatables'] = 'SMM/datatables_favourite_smm';
$route['smm/favourite/delete'] = 'SMM/delete_favourite_smm';
$route['smm/information'] = 'SMM/information';
$route['smm/information/datatables'] = 'SMM/datatables_information_smm';

$route['deposit/topup'] = 'Deposit/topup';
$route['deposit/topup/process'] = 'Deposit/process_topup';
$route['deposit/topup/qr/(:any)'] = 'Deposit/qr/$1';

$route['deposit/history'] = 'Deposit/history';
$route['deposit/history/datatables'] = 'Deposit/datatables_history_deposit';
$route['deposit/history/cancel'] = 'Deposit/process_cancel';
$route['deposit/history/payment/check'] = 'Deposit/payment_check';
$route['deposit/history/bayarscan/(:any)'] = 'Deposit/bayar_scan/$1';
$route['deposit/report'] = 'Deposit/report';
$route['deposit/report/filter'] = 'Deposit/filter_report';

// begin template finapp
$route['account'] = 'Account';

$route['landing'] = 'Landing';
$route['landing/ppob/category'] = 'Landing/ppob_category';
$route['landing/ppob/subcategory'] = 'Landing/ppob_subcategory';
$route['landing/ppob/modalsubprovider'] = 'Landing/ppob_brand';
$route['landing/ppob/product/(:any)/(:any)'] = 'Landing/ppob_product/$1/$2';
$route['landing/ppob/product/(:any)/(:any)/confirm'] = 'Landing/confirm_order_ppob/$1/$2';
$route['landing/ppob/product/(:any)/(:any)/process'] = 'Landing/process_order_ppob/$1/$2';
$route['landing/smm/category'] = 'Landing/smm_category';
$route['landing/smm/subcategory'] = 'Landing/smm_subcategory';
$route['landing/smm/product'] = 'Landing/smm_product';
$route['landing/smm/product/confirm'] = 'Landing/confirm_order_smm/$1';
$route['landing/smm/product/process'] = 'Landing/process_order_smm';
$route['landing/pricelist'] = 'Landing/pricelist';
$route['landing/pricelist/datatables'] = 'Landing/datatables_pricelist';
$route['landing/pricelist/brand'] = 'Landing/pricelist_brand';

$route['deposit/detail/(:any)'] = 'Deposit/detail_topup/$1';

$route['history'] = 'HistoryTransaction';
$route['history/type'] = 'HistoryTransaction/history';
$route['history/cekdetail/(:any)'] = 'HistoryTransaction/cek_detail_history/$1';
$route['history/detail/(:any)'] = 'HistoryTransaction/detail_history/$1';
$route['history/filter'] = 'HistoryTransaction/filter_history';
$route['history/datatables'] = 'HistoryTransaction/datatables';

$route['account/aboutus'] = 'Account/aboutus';
$route['account/news'] = 'Account/news';
$route['account/news/detail'] = 'Account/news_detail';
$route['account/privacypolicy'] = 'Account/privacypolicy';
$route['account/faq'] = 'Account/faq';
// end template finapp

$route['pages/(:num)'] = 'Pages/index/$1';

$route['ticket/new'] = 'Ticket/new';
$route['ticket/new/process'] = 'Ticket/process_create_ticket';
$route['ticket/history'] = 'Ticket/history';
$route['ticket/history/datatables'] = 'Ticket/datatables_history';
$route['ticket/history/close'] = 'Ticket/process_close_ticket';
$route['ticket/conversation/(:any)'] = 'Ticket/conversation/$1';
$route['ticket/conversation/(:any)/send'] = 'Ticket/send_conversation/$1';

$route['api'] = 'ApiDocs/index';
$route['api/regenerate_api'] = 'ApiDocs/regenerate_api_key';

$route['news'] = 'News';
$route['news/datatables'] = 'News/datatables_news';

$route['select/payment'] = 'Select/payment';
$route['select/paymentmethod'] = 'Select/paymentmethod';

$route['select/ppob/category'] = 'Select/ppob_category';
$route['select/ppob/subcategory'] = 'Select/ppob_subcategory';
$route['select/ppob/brand'] = 'Select/ppob_brand';
$route['select/ppob/product'] = 'Select/ppob_product';
$route['select/ppob/product/detail'] = 'Select/ppob_product_detail';
$route['select/ppob/product/pascabayar'] = 'Select/ppob_product_pascabayar';

$route['top'] = 'Top';

$route['select/smm/category'] = 'Select/smm_category';
$route['select/smm/product'] = 'Select/smm_product';
$route['select/smm/product/detail'] = 'Select/smm_product_detail';

$route['callback/tripay'] = 'Callback/tripay';
$route['callback/digiflazz'] = 'Callback/digiflazz';
$route['callback/ipaymu'] = 'Callback/ipaymu';
$route['callback/duitku'] = 'Callback/duitku';
$route['callback/paydisini'] = 'Callback/paydisini';

$route['sitemap'] = 'Sitemap';

$route['api/profile'] = 'API/profile';
$route['api/services'] = 'API/services';
$route['api/status'] = 'API/status';
$route['api/order'] = 'API/order';

$route['api/mobile/auth/login'] = 'Mobile/Auth/login';
$route['api/mobile/auth/login/phonenumber'] = 'Mobile/Auth/request_otp';
$route['api/mobile/auth/login/verify'] = 'Mobile/Auth/verify_otp';
$route['api/mobile/auth/login/resend'] = 'Mobile/Auth/resend_otp';
$route['api/mobile/auth/profile'] = 'Mobile/Auth/profile';
$route['api/mobile/auth/logout'] = 'Mobile/Auth/logout';
$route['api/mobile/auth/register'] = 'Mobile/Auth/register';
$route['api/mobile/auth/forgot_password'] = 'Mobile/Auth/forgot_password';

$route['api/mobile/profile/change_profile'] = 'Mobile/Profile/change_profile';
$route['api/mobile/profile/change_password'] = 'Mobile/Profile/change_password';
$route['api/mobile/profile/change_pin'] = 'Mobile/Profile/change_pin';
$route['api/mobile/profile/upload_image'] = 'Mobile/Profile/upload_image';
$route['api/mobile/profile/account_verification'] = 'Mobile/Profile/account_verification';
$route['api/mobile/profile/check_verification_status'] = 'Mobile/Profile/check_verification_status';

$route['api/mobile/slider'] = 'Mobile/Slider/index';

$route['api/mobile/deposit/paymentmethod/manual'] = 'Mobile/Deposit/paymentmethod_manual';
$route['api/mobile/deposit/paymentmethod/otomatis'] = 'Mobile/Deposit/paymentmethod_otomatis';
$route['api/mobile/deposit/request'] = 'Mobile/Deposit/request';
$route['api/mobile/deposit/detail'] = 'Mobile/Deposit/detail';

$route['api/mobile/dashboard/category/prabayar'] = 'Mobile/Dashboard/category_prabayar';
$route['api/mobile/dashboard/category/pascabayar'] = 'Mobile/Dashboard/category_pascabayar';
$route['api/mobile/dashboard/category/smm'] = 'Mobile/Dashboard/category_smm';

$route['api/mobile/news'] = 'Mobile/News/index';

$route['api/mobile/customersupport'] = 'Mobile/CustomerSupport/index';

$route['api/mobile/order/provider'] = 'Mobile/Order/provider';
$route['api/mobile/order/subprovider'] = 'Mobile/Order/subprovider';
$route['api/mobile/order/product'] = 'Mobile/Order/product';
$route['api/mobile/order/process'] = 'Mobile/Order/process';

$route['api/mobile/pascabayar/inquiry'] = 'Mobile/Pascabayar/inquiry';
$route['api/mobile/pascabayar/product'] = 'Mobile/Pascabayar/product';
$route['api/mobile/pascabayar/payment'] = 'Mobile/Pascabayar/payment';

$route['api/mobile/smm/category'] = 'Mobile/SMM/category';
$route['api/mobile/smm/product'] = 'Mobile/SMM/product';
$route['api/mobile/smm/order'] = 'Mobile/SMM/order';

$route['api/mobile/history/deposit'] = 'Mobile/History/Deposit/index';

$route['api/mobile/history/transaction/prabayar'] = 'Mobile/History/Transaction/prabayar';
$route['api/mobile/history/transaction/pascabayar'] = 'Mobile/History/Transaction/pascabayar';
$route['api/mobile/history/transaction/medsos'] = 'Mobile/History/Transaction/medsos';
$route['api/mobile/history/transaction/detail'] = 'Mobile/History/Transaction/detail';
$route['api/mobile/history/transaction/struk'] = 'Mobile/History/Transaction/struk';
$route['api/mobile/history/transaction/struk/download'] = 'Mobile/History/Transaction/download_struk';

$route['email-unsubscribe'] = 'EmailUnsubscribe/index';
$route['email-unsubscribe/process'] = 'EmailUnsubscribe/process';
$route['email-unsubscribe/success'] = 'EmailUnsubscribe/success';

$route['email-hook'] = 'EmailHook/index';
$route['email-hook/(:any)'] = 'EmailHook/index/$1';
