<?php

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Dompdf\Dompdf;

defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsProduct $msproduct
 * @property TrOrder $trorder
 * @property MsUsers $msusers
 * @property ApiKeys $apikeys
 * @property DisabledCategory $disabledcategory
 * @property ProductFavourite $productfavourite
 * @property Datatables $datatables
 * @property CI_DB_mysqli_driver $db
 * @property MsRole $msrole
 * @property MsRoleDiscount $msrolediscount
 * @property MsRoleDiscountAdv $msrolediscountadv
 * @property HistoryBalance $historybalance
 * @property MsContact $mscontact
 * @property ApiKeys_WhatsApp $apikeys_whatsapp
 * @property MsVendorDetail $msvendordetail
 */
class SMM extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsProduct', 'msproduct');
        $this->load->model('TrOrder', 'trorder');
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('ApiKeys', 'apikeys');
        $this->load->model('DisabledCategory', 'disabledcategory');
        $this->load->model('ProductFavourite', 'productfavourite');
        $this->load->model('MsRole', 'msrole');
        $this->load->model('MsRoleDiscount', 'msrolediscount');
        $this->load->model('MsRoleDiscountAdv', 'msrolediscountadv');
        $this->load->model('MsContact', 'mscontact');
        $this->load->model('HistoryBalance', 'historybalance');
        $this->load->model('ApiKeys_WhatsApp', 'apikeys_whatsapp');
        $this->load->model('MsVendorDetail', 'msvendordetail');
    }

    public function services()
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $disabled = $this->disabledcategory->result(array(
            'userid' => $this->merchant->id,
            'category_apikey' => 'SMM'
        ));

        $disabled_category = array();
        foreach ($disabled as $key => $value) {
            $disabled_category[] = $value->categoryname;
        }

        $where = array(
            'a.userid' => $this->merchant->id,
            'a.category_apikey' => 'SMM',
        );

        if ($this->merchant->multivendor != 1) {
            $smm = getCurrentVendor('SMM', $this->merchant->id);
            $where["(a.vendor = '$smm' OR a.vendor IS NULL) ="] = true;
            $where['a.vendorid'] = null;
        } else {
            $where["((a.vendorid IS NOT NULL AND a.vendorenabled = 1) OR a.vendor IS NULL) ="] = true;
        }

        $category = $this->msproduct->select('a.category')
            ->where_not_in('a.category', $disabled_category)
            ->group_by('a.category')
            ->result($where);

        $data = array();
        $data['title'] = $this->merchant->companyname . ' | Daftar Harga';
        $data['content'] = 'smm/services';
        $data['category'] = $category;

        return viewTemplate($this->merchant->id, "master", $data);
    }

    public function datatables_services_smm()
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            if (isLogin()) {
                $data = array();

                $datatable = $this->datatables->make('MsProduct', 'QueryDatatables', 'SearchDatatables');

                $where = array(
                    'a.userid' => $this->merchant->id,
                    'a.category_apikey' => 'SMM',
                    'a.subcategory_apikey' => 'SMM',
                    'b.id' => null,
                    'a.status' => 1,
                    'a.category !=' => null
                );

                if ($this->merchant->multivendor != 1) {
                    $vendor = getCurrentVendor('SMM', $this->merchant->id);
                    $where['a.vendor'] = $vendor;
                    $where['a.vendorid'] = null;
                } else {
                    $where['a.vendorid !='] = null;
                    $where['a.vendorenabled'] = 1;
                }

                $category = getPost('category');

                if ($category != null) {
                    $where['a.category'] = $category;
                }

                $currenttheme = getCurrentThemeConfiguration($this->merchant->id);

                if ($currenttheme != 'HighAdmin-TopNav' && $currenttheme != 'Dason-TopNav' && $currenttheme != 'Able') {
                    $label = "label label";
                } else if ($currenttheme == 'Dason-TopNav' || $currenttheme == 'Able') {
                    $label = "badge bg";
                } else {
                    $label = "badge badge";
                }

                $currentuser = getCurrentUser();

                if ($currentuser->roleid == null) {
                    $getrole = $this->msrole->get(array(
                        'createdby' => $this->merchant->id,
                        'isdefault' => '1'
                    ))->row();
                } else {
                    $getrole = $this->msrole->get(array(
                        'createdby' => $this->merchant->id,
                        'id' => $currentuser->roleid
                    ))->row();
                }

                if ($getrole != null) {
                    if ($getrole->discounttype == 'Simple') {
                        $getrolediscount = $this->msrolediscount->get(array(
                            'userid' => $this->merchant->id,
                            'roleid' => $getrole->id
                        ))->row();

                        $discount = $getrolediscount->trxdiscount ?? 0;
                    } else {
                        $discount = $this->msrolediscountadv->get(array(
                            'createdby' => $this->merchant->id,
                            'roleid' => $getrole->id,
                            'servicetype' => 'SMM'
                        ))->result();
                    }
                } else {
                    $getrole = new stdClass();
                    $getrole->discounttype = 'Simple';
                    $discount = 0;
                }

                foreach ($datatable->getData($where) as $key => $value) {
                    if ($value->status == 1) {
                        $status = "<span class=\"$label-success\">Normal</span>";
                    } else {
                        $status = "<span class=\"$label-danger\">Gangguan</span>";
                    }

                    $favourite = "";
                    if ($value->favouriteid == null) {
                        if ($currenttheme != 'HighAdmin-TopNav' && $currenttheme != 'Dason-TopNav' && $currenttheme != 'Able') {
                            $favourite = "<a href=\"javascript:;\" onclick=\"addFavourite('" . stringEncryption('encrypt', $value->id) . "')\"><i class=\"fa fa-star-o\"></i></a>";
                        } else {
                            $favourite = "<a href=\"javascript:;\" onclick=\"addFavourite('" . stringEncryption('encrypt', $value->id) . "')\"><i class=\"far fa-star\"></i></a>";
                        }
                    } else {
                        $favourite = "<a href=\"javascript:;\" onclick=\"removeFavourite('" . stringEncryption('encrypt', $value->id) . "')\"><i class=\"fa fa-star\"></i></a>";
                    }

                    $detail = array();
                    $detail[] = $favourite;
                    $detail[] = $value->category;
                    $detail[] = $value->productname;
                    $detail[] = IDR($value->minorder);
                    $detail[] = IDR($value->maxorder);

                    if ($getrole->discounttype == 'Simple') {
                        $detail[] = IDR($value->price - $discount);
                    } else {
                        $found = false;

                        foreach ($discount as $val) {
                            if ($found) continue;

                            if ($val->startrange <= $value->price && $val->endrange >= $value->price) {
                                if ($val->discounttype == 'Persentase') {
                                    $detail[] = IDR($value->price - ($value->price * $val->nominal / 100));

                                    $found = true;
                                } else {
                                    $detail[] = IDR($value->price - $val->nominal);

                                    $found = true;
                                }
                            }
                        }

                        if ($found == false) {
                            $detail[] = IDR($value->price);
                        }
                    }
                    $detail[] = $status;

                    $data[] = $detail;
                }

                return $datatable->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function order()
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $disabled = $this->disabledcategory->result(array(
            'userid' => $this->merchant->id,
            'category_apikey' => 'SMM'
        ));

        $disabled_category = array();
        foreach ($disabled as $key => $value) {
            $disabled_category[] = $value->categoryname;
        }

        $where = array(
            'userid' => $this->merchant->id,
            'subcategory_apikey' => 'SMM',
            'category !=' => null
        );

        if ($this->merchant->multivendor != 1) {
            $vendor = getCurrentVendor('SMM', $this->merchant->id);
            $where["(vendor = '$vendor' OR vendor IS NULL) ="] = true;
            $where['vendorid'] = null;
        } else {
            $where["((vendorid IS NOT NULL AND vendorenabled = 1) OR vendor IS NULL) ="] = true;
        }

        $data = array();
        $data['title'] = $this->merchant->companyname . ' | Pesanan Baru';
        $data['content'] = 'smm/order';
        $data['category'] = $this->msproduct->select('category')
            ->where_not_in('category', $disabled_category)
            ->group_by('category')
            ->order_by('category', 'ASC')
            ->result($where);

        return viewTemplate($this->merchant->id, "master", $data);
    }

    public function process_order()
    {
        try {
            $this->db->trans_begin();

            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                $this->db->trans_rollback();

                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            }

            $product = getPost('product');
            $additional = getPost('additional');
            $target = getPost('target');
            $qty = getPost('qty');
            $pin = getPost('pin');

            if ($product == null) {
                throw new Exception('Produk wajib diisi');
            } else if ($target == null) {
                throw new Exception('Target wajib diisi');
            } else if ($qty == null) {
                throw new Exception('Jumlah wajib diisi');
            } else if (!is_numeric($qty)) {
                throw new Exception('Jumlah harus berupa angka');
            } else if ($pin == null) {
                throw new Exception('PIN Transaksi wajib diisi');
            }

            $currentuser = getCurrentUser(null, true);

            if (stringEncryption('encrypt', $pin) != $currentuser->pin) {
                throw new Exception('PIN Transaksi yang anda masukkan salah');
            }

            $disabled = $this->disabledcategory->result(array(
                'userid' => $this->merchant->id,
                'category_apikey' => 'SMM'
            ));

            $disabled_category = array();
            foreach ($disabled as $key => $value) {
                $disabled_category[] = $value->categoryname;
            }

            $where = array(
                'id' => $product,
                'userid' => $this->merchant->id,
                'subcategory_apikey' => 'SMM',
                'category !=' => null
            );

            if ($this->merchant->multivendor != 1) {
                $vendor = getCurrentVendor('SMM', $this->merchant->id);
                $where["(vendor = '$vendor' OR vendor IS NULL) ="] = true;
                $where['vendorid'] = null;
            } else {
                $where["((vendorid IS NOT NULL AND vendorenabled = 1) OR vendor IS NULL) ="] = true;
            }

            $getproduct = $this->msproduct->where_not_in('category', $disabled_category)
                ->get($where);

            if ($getproduct->num_rows() == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            $productRow = $getproduct->row();

            if ($productRow->status != 1) {
                throw new Exception('Produk sedang gangguan');
            }

            if ($productRow->minorder > $qty) {
                throw new Exception("Jumlah minimal order adalah " . IDR($productRow->minorder));
            }

            if ($productRow->maxorder < $qty) {
                throw new Exception("Jumlah maksimal order adalah " . IDR($productRow->maxorder));
            }

            if ($currentuser->roleid == null) {
                $getrole = $this->msrole->get(array(
                    'createdby' => $this->merchant->id,
                    'isdefault' => '1'
                ))->row();
            } else {
                $getrole = $this->msrole->get(array(
                    'createdby' => $this->merchant->id,
                    'id' => $currentuser->roleid
                ))->row();
            }

            if ($getrole != null) {
                if ($getrole->discounttype == 'Simple') {
                    $getrolediscount = $this->msrolediscount->get(array(
                        'userid' => $this->merchant->id,
                        'roleid' => $getrole->id
                    ))->row();

                    $discount = $getrolediscount->trxdiscount ?? 0;
                } else {
                    $discount = $this->msrolediscountadv->get(array(
                        'createdby' => $this->merchant->id,
                        'roleid' => $getrole->id,
                        'servicetype' => 'SMM'
                    ))->result();
                }
            } else {
                $getrole = new stdClass();
                $getrole->discounttype = 'Simple';
                $discount = 0;
            }

            $fixproductprice = 0;

            if ($getrole->discounttype == 'Simple') {
                $fixproductprice = $productRow->price - $discount;
            } else {
                $found = false;

                foreach ($discount as $val) {
                    if ($found) continue;

                    if ($val->startrange <= $productRow->price && $val->endrange >= $productRow->price) {
                        if ($val->discounttype == 'Persentase') {
                            $fixproductprice = $productRow->price - ($productRow->price * $val->nominal / 100);

                            $found = true;
                        } else {
                            $fixproductprice = $productRow->price - $val->nominal;

                            $found = true;
                        }
                    }
                }

                if ($found == false) {
                    $fixproductprice = $productRow->price;
                }
            }

            $totalharga = ($fixproductprice / 1000) * $qty;
            $totalhargaorigin = ($productRow->price / 1000) * $qty;

            $potonganprofit = $totalhargaorigin - $totalharga;

            $totalhargabyvendor = ($productRow->vendorprice / 1000) * $qty;
            $profit = (($productRow->profit / 1000) * $qty) - $potonganprofit;

            if ($currentuser->balance < $totalharga) {
                throw new Exception('Saldo anda tidak mencukupi');
            }

            $getpending = $this->trorder->total(array(
                'userid' => getCurrentIdUser(),
                'serviceid' => $productRow->id,
                'status' => 'pending',
                'target' => $target,
            ));

            if ($getpending > 0) {
                throw new Exception('Anda memiliki transaksi yang masih dalam proses');
            }

            $queuetransaction = false;

            if ($this->merchant->multivendor != 1) {
                $apikeys = getCurrentAPIKeys('SMM', $this->merchant->id);
                if (ENVIRONMENT == 'production' && $apikeys->balance < $totalhargabyvendor) {
                    $queuetransaction = true;
                }
            } else {
                if (ENVIRONMENT == 'production' && getCurrentBalanceVendor($this->merchant->id, $productRow->vendorid) < $totalhargabyvendor) {
                    $queuetransaction = true;
                }
            }

            $insert = array();
            $insert['userid'] = getCurrentIdUser();
            $insert['clientcode'] = generateTransactionNumber('SMM');
            $insert['serviceid'] = $productRow->id;
            $insert['target'] = $target;
            $insert['qty'] = $qty;
            $insert['price'] = $totalharga;
            $insert['profit'] = $profit;
            $insert['currentsaldo'] = $currentuser->balance;
            $insert['status'] = 'pending';
            $insert['type'] = 'SMM';
            $insert['queuetransaction'] = $queuetransaction ? 1 : 0;
            $insert['category_apikey'] = $productRow->category_apikey;
            $insert['subcategory_apikey'] = $productRow->subcategory_apikey;
            $insert['orderplatform'] = 'web';
            $insert['productcode'] = $productRow->code;
            $insert['productname_order'] = $productRow->productname;
            $insert['vendor'] = $productRow->vendor;
            $insert['merchantid_order'] = $this->merchant->id;
            $insert['status_payment'] = 'sukses';
            $insert['vendorid'] = $productRow->vendorid;

            if ($productRow->iscustom == 1) {
                $insert['additional'] = $additional;
            }

            $this->trorder->insert($insert);
            $insert_id = $this->db->insert_id();

            $contact = getPost('contact');

            if ($contact != null) {
                $insertcontact = array();
                $insertcontact['contactname'] = $contact;
                $insertcontact['content'] = $target;
                $insertcontact['userid'] = getCurrentIdUser();
                $insertcontact['contacttype'] = 'SMM';

                $this->mscontact->insert($insertcontact);
            }

            $inserthistorybalance = array();
            $inserthistorybalance['userid'] = getCurrentIdUser();
            $inserthistorybalance['type'] = 'OUT';
            $inserthistorybalance['nominal'] = $totalharga;
            $inserthistorybalance['currentbalance'] = $currentuser->balance;
            $inserthistorybalance['orderid'] = $insert_id;
            $inserthistorybalance['createdby'] = getCurrentIdUser();
            $inserthistorybalance['createddate'] = getCurrentDate();

            $this->historybalance->insert($inserthistorybalance);

            $update = array();
            $update['balance'] = $currentuser->balance - $totalharga;

            $this->msusers->update(array(
                'id' => getCurrentIdUser()
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Transaksi gagal');
            }

            pullTransaction($this->merchant->id);

            if ($productRow->code != null) {
                if ($queuetransaction == false && ENVIRONMENT == 'production') {
                    if ($this->merchant->multivendor != 1) {
                        if ($vendor == 'BuzzerPanel') {
                            $buzzerpanel = new BuzzerPanel(stringEncryption('decrypt', $apikeys->apikey), stringEncryption('decrypt', $apikeys->secretkey));
                            $order = $buzzerpanel->order($productRow->code, $target, $qty);

                            if (isset($order->status) && $order->status) {
                                $update = array();
                                $update['servercode'] = $order->data->id;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Pembelian berhasil');
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[BUZZERPANEL ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                if (isset($order->msg)) {
                                    throw new Exception($order->msg);
                                } else {
                                    throw new Exception('Gagal melakukan pembelian');
                                }
                            }
                        } else if ($vendor == 'MedanPedia') {
                            $medanpedia = new MedanPedia(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));
                            $order = $medanpedia->order($productRow->code, $target, $qty, $productRow->iscustom == 1 ? $additional : '');

                            if (isset($order->status) && $order->status) {
                                $update = array();
                                $update['servercode'] = $order->data->id;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Pembelian berhasil');
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[MEDANPEDIA ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                if (isset($order->data)) {
                                    throw new Exception($order->data);
                                } else {
                                    throw new Exception('Gagal melakukan pembelian');
                                }
                            }
                        } else if ($vendor == 'IrvanKede') {
                            if ($productRow->iscustom == null) {
                                $additional = '';
                            }

                            $irvankede = new IrvanKede(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));
                            $order = $irvankede->order(array(
                                'service' => $productRow->code,
                                'target' => $target,
                                'quantity' => $qty,
                                'custom_comments' => $additional,
                                'custom_link' => $additional
                            ));

                            if ($order->status) {
                                $update = array();
                                $update['servercode'] = $order->data->id;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Pembelian berhasil');
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[IRVANKEDE ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                if (isset($order->data)) {
                                    throw new Exception($order->data);
                                } else {
                                    throw new Exception('Gagal melakukan pembelian');
                                }
                            }
                        } else if ($vendor == 'DailyPanel') {
                            $dailypanel = new DailyPanel(stringEncryption('decrypt', $apikeys->apikey), stringEncryption('decrypt', $apikeys->secretkey));
                            $order = $dailypanel->order($productRow->code, $target, $qty, $productRow->iscustom == 1 ? $additional : '');

                            if (isset($order->success) && $order->success) {
                                $update = array();
                                $update['servercode'] = $order->msg->order_id;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Pembelian berhasil');
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[DAILYPANEL ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception($order->msg->error);
                            }
                        } else if ($vendor == 'WStore') {
                            $wstore = new WStore(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey), stringEncryption('decrypt', $apikeys->secretkey));
                            $order = $wstore->order($productRow->code, $target, $qty);

                            if (isset($order->response) && $order->response) {
                                $update = array();
                                $update['servercode'] = $order->data->id;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Pembelian berhasil');
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[WSTORE ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception($order->data->msg);
                            }
                        } else if ($vendor == 'UNDRCTRL') {
                            $undrctrl = new UNDRCTRL(stringEncryption('decrypt', $apikeys->apikey));
                            $order = $undrctrl->order($productRow->code, $target, $qty);

                            if (isset($order->order)) {
                                $update = array();
                                $update['servercode'] = $order->order;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Pembelian berhasil');
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[UNDRCTRL ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception('Gagal melakukan pembelian');
                            }
                        } else if ($vendor == 'SosmedOnline') {
                            $sosmedonline = new SosmedOnline(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));
                            $order = $sosmedonline->order($productRow->code, $target, $qty, $productRow->iscustom == 1 ? $additional : '');

                            if (isset($order->status) && $order->status) {
                                $update = array();
                                $update['servercode'] = $order->data->id;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Pembelian berhasil');
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[SOSMEDONLINE ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception('Gagal melakukan pembelian');
                            }
                        } else if ($vendor == 'SosmedOnlineVIP') {
                            $sosmedonlinevip = new SosmedOnlineVIP(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));
                            $order = $sosmedonlinevip->order($productRow->code, $target, $qty, $productRow->iscustom == 1 ? $additional : '');

                            if (isset($order->status) && $order->status) {
                                $update = array();
                                $update['servercode'] = $order->data->id;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Pembelian berhasil');
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[SOSMEDONLINEVIP ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception('Gagal melakukan pembelian');
                            }
                        } else if ($vendor == 'DjuraganSosmed') {
                            $djuragansosmed = new DjuraganSosmed(stringEncryption('decrypt', $apikeys->apikey));
                            $order = $djuragansosmed->order($productRow->code, $target, $qty);

                            if (isset($order->order)) {
                                $update = array();
                                $update['servercode'] = $order->order;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Pembelian berhasil');
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[DJURAGANSOSMED ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception('Gagal melakukan pembelian');
                            }
                        } else if ($vendor == 'SMMRaja') {
                            $smmraja = new SMMRaja(stringEncryption('decrypt', $apikeys->apikey));
                            $order = $smmraja->order($productRow->code, $target, $qty);

                            if (isset($order->order)) {
                                $update = array();
                                $update['servercode'] = $order->order;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Pembelian berhasil');
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[SMMRAJA ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception('Gagal melakukan pembelian');
                            }
                        } else if ($vendor == 'SMMIllusion') {
                            $smmillusion = new SMMIllusion(stringEncryption('decrypt', $apikeys->apikey));
                            $order = $smmillusion->order($productRow->code, $target, $qty);

                            if (isset($order->order)) {
                                $update = array();
                                $update['servercode'] = $order->order;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Pembelian berhasil');
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[SMMILLUSION ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception('Gagal melakukan pembelian');
                            }
                        } else if ($vendor == 'V1Pedia') {
                            $v1pedia = new V1Pedia(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey), stringEncryption('decrypt', $apikeys->secretkey));
                            $order = $v1pedia->order($productRow->code, $target, $qty);

                            if (isset($order->response) && $order->response) {
                                $update = array();
                                $update['servercode'] = $order->data->id;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $insert_id
                                ), $update);

                                $this->db->trans_commit();

                                $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                                return JSONResponseDefault('OK', 'Pembelian berhasil');
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[V1PEDIA ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception($order->data->msg);
                            }
                        }
                    } else {
                        $order = $this->msvendordetail->select('a.*, b.default_config')
                            ->join('msvendor b', 'b.id = a.vendorid')
                            ->get(array(
                                'a.vendorid' => $productRow->vendorid,
                                'a.apitype' => 'Order'
                            ));

                        if ($order->num_rows() == 0) {
                            throw new Exception('Konfigurasi vendor tidak ditemukan');
                        }

                        $vendor_order = $order->row();

                        if ($vendor_order->default_config == null) {
                            throw new Exception('Konfigurasi vendor tidak ditemukan');
                        }

                        $response_indicator = json_decode($vendor_order->response_indicator);
                        $response_setting = json_decode($vendor_order->response_setting);

                        $dynamicvendor = new DynamicVendor($productRow->vendorid, json_decode($vendor_order->default_config, true));
                        $order = $dynamicvendor->order($insert_id);

                        if (
                            ($response_indicator->index == null && isset($order[$response_indicator->key]) && (
                                $response_indicator->datatype == 'string' && $order[$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'number' && $order[$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'boolean' && $order[$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'exists' && isset($order[$response_indicator->key])
                            ))
                            || ($response_indicator->index != null && isset($order[$response_indicator->index][$response_indicator->key]) && (
                                $response_indicator->datatype == 'string' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'number' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'boolean' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'exists' && isset($order[$response_indicator->index][$response_indicator->key])
                            ))
                        ) {
                            $var_referenceid = $response_setting->referenceid ?? null;
                            $var_price = $response_setting->price ?? null;
                            $var_status = $response_setting->status ?? null;
                            $var_note = $response_setting->note ?? null;
                            $var_sn = $response_setting->sn ?? null;
                            $var_errorrefund = $response_setting->errorrefund;

                            $exploding_errorefund = explode('[#]', strtolower($var_errorrefund ?? ''));

                            if ($response_setting->index != null) {
                                $order = $order[$response_setting->index] ?? null;
                            }

                            $referenceid = $var_referenceid != null ? ($order[$var_referenceid] ?? null) : null;
                            $price = $var_price != null ? ($order[$var_price] ?? null) : null;
                            $status = $var_status != null ? ($order[$var_status] ?? null) : null;
                            $note = $var_note != null ? ($order[$var_note] ?? null) : null;
                            $sn = $var_sn != null ? ($order[$var_sn] ?? null) : null;

                            if ($status != null) {
                                if (in_array($status, $exploding_errorefund)) {
                                    log_message_user('error', '[MULTIVENDOR ORDER] Failed to parsing error refund, response: ' . json_encode($order), $this->merchant->id);

                                    throw new Exception('Gagal melakukan transaksi');
                                }
                            } else {
                                if ($var_status == null) {
                                    $status = 'pending';
                                } else if ($var_status != null && $status == null) {
                                    log_message_user('error', '[MULTIVENDOR ORDER] Failed to parsing status, response: ' . json_encode($order), $this->merchant->id);

                                    throw new Exception('Gagal melakukan transaksi');
                                }
                            }

                            $update = array();
                            $update['jsonresponse'] = json_encode($order);

                            if ($referenceid != null) {
                                $update['servercode'] = $referenceid;
                            }

                            if ($price != null) {
                                $update['price'] = $price;
                            }

                            if ($status != null) {
                                $update['status'] = $status;
                            }

                            if ($note != null) {
                                $update['note'] = $note;
                            }

                            if ($sn != null) {
                                $update['sn'] = $sn;
                            }

                            $this->trorder->update(array(
                                'id' => $insert_id
                            ), $update);

                            $this->db->trans_commit();

                            $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                            return JSONResponseDefault('OK', 'Transaksi berhasil');
                        } else {
                            log_message_user('error', '[MULTIVENDOR ORDER] Failed to parse response, response: ' . json_encode($order), $this->merchant->id);

                            throw new Exception('Gagal melakukan transaksi');
                        }
                    }
                }
            }

            $this->db->trans_commit();

            $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

            return JSONResponseDefault('OK', 'Pembelian berhasil');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function order_massal()
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $disabled = $this->disabledcategory->result(array(
            'userid' => $this->merchant->id,
            'category_apikey' => 'SMM'
        ));

        $disabled_category = array();
        foreach ($disabled as $key => $value) {
            $disabled_category[] = $value->categoryname;
        }

        $where = array(
            'userid' => $this->merchant->id,
            'subcategory_apikey' => 'SMM',
            'category !=' => null
        );

        if ($this->merchant->multivendor != 1) {
            $vendor = getCurrentVendor('SMM', $this->merchant->id);
            $where["(vendor = '$vendor' OR vendor IS NULL) ="] = true;
            $where['vendorid'] = null;
        } else {
            $where["((vendorid IS NOT NULL AND vendorenabled = 1) OR vendor IS NULL) ="] = true;
        }

        $data = array();
        $data['title'] = $this->merchant->companyname . ' | Pesanan Baru';
        $data['content'] = 'smm/order_massal';
        $data['category'] = $this->msproduct->select('category')
            ->where_not_in('category', $disabled_category)
            ->group_by('category')
            ->order_by('category', 'ASC')
            ->result($where);

        return viewTemplate($this->merchant->id, "master", $data);
    }


    public function form_order()
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            }

            $disabled = $this->disabledcategory->result(array(
                'userid' => $this->merchant->id,
                'category_apikey' => 'SMM'
            ));

            $disabled_category = array();
            foreach ($disabled as $key => $value) {
                $disabled_category[] = $value->categoryname;
            }

            $where = array(
                'userid' => $this->merchant->id,
                'subcategory_apikey' => 'SMM',
                'category !=' => null
            );

            if ($this->merchant->multivendor != 1) {
                $vendor = getCurrentVendor('SMM', $this->merchant->id);
                $where["(vendor = '$vendor' OR vendor IS NULL) ="] = true;
                $where['vendorid'] = null;
            } else {
                $where["((vendorid IS NOT NULL AND vendorenabled = 1) OR vendor IS NULL) ="] = true;
            }

            $category = $this->msproduct->select('category')
                ->where_not_in('category', $disabled_category)
                ->group_by('category')
                ->order_by('category', 'ASC')
                ->result($where);

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => viewTemplate($this->merchant->id, "smm/order_form", array(
                    'category' => $category,
                ), true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_order_massal()
    {
        try {
            $this->db->trans_begin();

            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                $this->db->trans_rollback();

                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            }

            $product = getPost('product', array());
            $additional = getPost('additional', array());
            $target = getPost('target', array());
            $qty = getPost('qty', array());
            $pin = getPost('pin');
            $contact = getPost('contact', array());

            if (count($product) == 0) {
                throw new Exception('Produk wajib diisi');
            } else {
                foreach ($product as $key => $value) {
                    if ($value == null) {
                        throw new Exception('Produk ke ' . ($key + 1) . ' wajib diisi');
                    } else if ($target[$key] == null) {
                        throw new Exception('Target ke ' . ($key + 1) . ' wajib diisi');
                    } else if ($qty[$key] == null) {
                        throw new Exception('Qty ke ' . ($key + 1) . ' wajib diisi');
                    } else if ($qty[$key] <= 0) {
                        throw new Exception('Qty ke ' . ($key + 1) . ' tidak boleh kurang dari 1');
                    }
                }
            }

            if ($pin == null) {
                throw new Exception('PIN Transaksi wajib diisi');
            }

            $currentuser = getCurrentUser();

            if (stringEncryption('encrypt', $pin) != $currentuser->pin) {
                throw new Exception('PIN Transaksi yang anda masukkan salah');
            }

            if ($currentuser->roleid == null) {
                $getrole = $this->msrole->get(array(
                    'createdby' => $this->merchant->id,
                    'isdefault' => '1'
                ))->row();
            } else {
                $getrole = $this->msrole->get(array(
                    'createdby' => $this->merchant->id,
                    'id' => $currentuser->roleid
                ))->row();
            }

            if ($getrole != null) {
                if ($getrole->discounttype == 'Simple') {
                    $getrolediscount = $this->msrolediscount->get(array(
                        'userid' => $this->merchant->id,
                        'roleid' => $getrole->id
                    ))->row();

                    $discount = $getrolediscount->trxdiscount ?? 0;
                } else {
                    $discount = $this->msrolediscountadv->get(array(
                        'createdby' => $this->merchant->id,
                        'roleid' => $getrole->id,
                        'servicetype' => 'SMM'
                    ))->result();
                }
            } else {
                $getrole = new stdClass();
                $getrole->discounttype = 'Simple';
                $discount = 0;
            }

            $disabled = $this->disabledcategory->result(array(
                'userid' => $this->merchant->id,
                'category_apikey' => 'SMM'
            ));

            $disabled_category = array();
            foreach ($disabled as $key => $value) {
                $disabled_category[] = $value->categoryname;
            }

            if ($this->merchant->multivendor != 1) {
                $vendor = getCurrentVendor('SMM', $this->merchant->id);
            }

            $grandtotal = 0;

            $arrayproduct = array();
            foreach ($product as $key => $value) {
                $where = array(
                    'id' => $value,
                    'userid' => $this->merchant->id,
                    'subcategory_apikey' => 'SMM',
                    'category !=' => null
                );

                if ($this->merchant->multivendor != 1) {
                    $where["(vendor = '$vendor' OR vendor IS NULL) ="] = true;
                    $where['vendorid'] = null;
                } else {
                    $where["((vendorid IS NOT NULL AND vendorenabled = 1) OR vendor IS NULL) ="] = true;
                }

                $getproduct = $this->msproduct->where_not_in('category', $disabled_category)
                    ->get($where);

                if ($getproduct->num_rows() == 0) {
                    throw new Exception('Produk tidak ditemukan');
                }

                $productRow = $getproduct->row();

                if ($productRow->status != 1) {
                    throw new Exception('Produk sedang gangguan');
                }

                if ($productRow->minorder > $qty[$key]) {
                    throw new Exception('Jumlah minimal order produk ke ' . ($key + 1) . ' adalah ' . IDR($productRow->minorder));
                }

                if ($productRow->maxorder < $qty[$key]) {
                    throw new Exception('Jumlah maksimal order produk ke ' . ($key + 1) . ' adalah ' . IDR($productRow->maxorder));
                }

                $fixproductprice = $productRow->price;

                if ($getrole->discounttype == 'Simple') {
                    $fixproductprice = $productRow->price - $discount;
                } else {
                    $found = false;

                    foreach ($discount as $val) {
                        if ($found) continue;

                        if ($val->startrange <= $productRow->price && $val->endrange >= $productRow->price) {
                            if ($val->discounttype == 'Persentase') {
                                $fixproductprice = $productRow->price - ($productRow->price * $val->nominal / 100);

                                $found = true;
                            } else {
                                $fixproductprice = $productRow->price - $val->nominal;

                                $found = true;
                            }
                        }
                    }

                    if ($found == false) {
                        $fixproductprice = $productRow->price;
                    }
                }

                $totalharga = ($fixproductprice / 1000) * $qty[$key];
                $totalhargaorigin = ($productRow->price / 1000) * $qty[$key];

                $potonganprofit = $totalhargaorigin - $totalharga;

                $totalhargabyvendor = ($productRow->vendorprice / 1000) * $qty[$key];
                $profit = (($productRow->profit / 1000) * $qty[$key]) - $potonganprofit;

                $grandtotal += $totalharga;

                $arrayproduct[] = array(
                    'product' => $productRow,
                    'totalharga' => $totalharga,
                    'profit' => $profit,
                    'totalhargabyvendor' => $totalhargabyvendor
                );
            }

            $currentbalance = getCurrentBalance(getCurrentIdUser(), true);
            $simulatedBalance = $currentbalance;
            if ($currentbalance < $grandtotal) {
                throw new Exception('Saldo anda tidak mencukupi');
            }

            $realGrandTotal = 0;
            $transactionFailed = array();
            $transactionSuccess = array();
            foreach ($arrayproduct as $key => $value) {
                $productRow = $value['product'];
                $totalharga = $value['totalharga'];
                $profit = $value['profit'];
                $totalhargabyvendor = $value['totalhargabyvendor'];

                $getpending = $this->trorder->total(array(
                    'userid' => getCurrentIdUser(),
                    'serviceid' => $productRow->id,
                    'status' => 'pending',
                    'target' => $target[$key]
                ));

                if ($getpending > 0) {
                    $transactionFailed[] = array(
                        'message' => 'Transaksi ' . $productRow->productname . ' ke ' . $target[$key] . ' sedang dalam proses transaksi',
                    );
                    continue;
                }

                $insert = array();
                $insert['userid'] = getCurrentIdUser();
                $insert['clientcode'] = generateTransactionNumber('SMM');
                $insert['serviceid'] = $productRow->id;
                $insert['target'] = $target[$key];
                $insert['qty'] = $qty[$key];
                $insert['price'] = $totalharga;
                $insert['profit'] = $profit;
                $insert['currentsaldo'] = getCurrentBalance();
                $insert['status'] = 'pending';
                $insert['type'] = 'SMM';
                $insert['queuetransaction'] = 1;
                $insert['category_apikey'] = $productRow->category_apikey;
                $insert['subcategory_apikey'] = $productRow->subcategory_apikey;
                $insert['orderplatform'] = 'web';
                $insert['productcode'] = $productRow->code;
                $insert['productname_order'] = $productRow->productname;
                $insert['vendor'] = $productRow->vendor;
                $insert['merchantid_order'] = $this->merchant->id;
                $insert['status_payment'] = 'sukses';
                $insert['vendorid'] = $productRow->vendorid;

                if ($productRow->iscustom == 1) {
                    $insert['additional'] = $additional[$key];
                }

                $this->trorder->insert($insert);
                $insert_id = $this->db->insert_id();

                if ($contact[$key] != null) {
                    $insertcontact = array();
                    $insertcontact['contactname'] = $contact[$key];
                    $insertcontact['content'] = $target[$key];
                    $insertcontact['userid'] = getCurrentIdUser();
                    $insertcontact['contacttype'] = 'SMM';

                    $this->mscontact->insert($insertcontact);
                }

                $inserthistorybalance = array();
                $inserthistorybalance['userid'] = getCurrentIdUser();
                $inserthistorybalance['type'] = 'OUT';
                $inserthistorybalance['nominal'] = $totalharga;
                $inserthistorybalance['currentbalance'] = $simulatedBalance;
                $inserthistorybalance['orderid'] = $insert_id;
                $inserthistorybalance['createdby'] = getCurrentIdUser();
                $inserthistorybalance['createddate'] = getCurrentDate();

                $this->historybalance->insert($inserthistorybalance);

                $transactionSuccess[] = array(
                    'message' => 'Transaksi ' . $productRow->productname . ' ke ' . $target[$key] . ' berhasil',
                );
                $realGrandTotal += $totalharga;
                $simulatedBalance -= $totalharga;
            }

            $update = array();
            $update['balance'] = $currentbalance - $realGrandTotal;

            $this->msusers->update(array(
                'id' => getCurrentIdUser()
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Transaksi gagal');
            }

            $this->db->trans_commit();

            $message = '';
            foreach ($transactionSuccess as $key => $value) {
                if (count($transactionFailed) > 0) {
                    $message .= $value['message'] . ', ';
                } else {
                    $message .= $value['message'];
                }
            }

            foreach ($transactionFailed as $key => $value) {
                if ($key == count($transactionFailed) - 1) {
                    $message .= $value['message'];
                } else {
                    $message .= $value['message'] . ', ';
                }
            }

            return JSONResponseDefault('OK', $message);
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    private function send_notification($orderid, $userid, $phonenumber)
    {
        // Kirim Firebase notification untuk order
        sendFirebaseNotificationOrder($orderid, $userid);

        $apikeys_whatsapp = $this->apikeys_whatsapp->get(array(
            'userid' => $userid,
        ));

        if ($apikeys_whatsapp->num_rows() == 0) {
            return false;
        }

        $row = $apikeys_whatsapp->row();

        $wablasingateway = new WABlasinGateway(stringEncryption('decrypt', $row->apikey), stringEncryption('decrypt', $row->devicecode));

        $messagenotification = replaceParameterNotification($orderid, $userid);
        if ($messagenotification != null && $phonenumber != null) {
            $phonenumber = changePrefixPhone($phonenumber);

            $send = $wablasingateway->sendMessage($phonenumber, $messagenotification);

            if (!isset($send->RESULT) || (isset($send->RESULT) && $send->RESULT != 'OK')) {
                log_message_user('error', '[WHATSAPP SEND MESSAGE] Send notification failed: ' . json_encode($send), $userid);
            }

            return true;
        }

        return false;
    }

    public function history()
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $data = array();
        $data['title'] = $this->merchant->companyname . ' | Riwayat';
        $data['content'] = 'smm/history';

        return viewTemplate($this->merchant->id, "master", $data);
    }

    public function datatables_history_smm()
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            if (isLogin()) {
                $data = array();

                $datatables = $this->datatables->make('TrOrder', 'QueryDatatables', 'SearchDatatables');

                $where = array(
                    'a.userid' => getCurrentIdUser(),
                    'a.type' => 'SMM',
                );

                $currenttheme = getCurrentThemeConfiguration($this->merchant->id);

                if ($currenttheme != 'HighAdmin-TopNav' && $currenttheme != 'Dason-TopNav' && $currenttheme != 'Able') {
                    $label = "label label";
                } else if ($currenttheme == 'Dason-TopNav' || $currenttheme == 'Able') {
                    $label = "badge bg";
                } else {
                    $label = "badge badge";
                }

                foreach ($datatables->getData($where) as $key => $value) {
                    $createddate = new DateTime($value->createddate);
                    $updateddate = new DateTime($value->updateddate);

                    $waktu_proses = $createddate->diff($updateddate);

                    if (strtolower($value->status) == 'pending') {
                        $status = "<span class=\"$label-warning\">Pending</span>";
                    } else if (strtolower($value->status) == 'success' || strtolower($value->status) == 'completed') {
                        $status = "<span class=\"$label-success\">Success</span>";
                    } else if (strtolower($value->status) == 'in progress') {
                        $status = "<span class=\"$label-info\">In progress</span>";
                    } else if (strtolower($value->status) == 'processing') {
                        $status = "<span class=\"$label-info\">Processing</span>";
                    } else {
                        $status = "<span class=\"$label-danger\">" . ucfirst($value->status) . "</span>";
                    }

                    $timeprocess = "-";
                    if (strtolower($value->status) == 'success' || strtolower($value->status) == 'sukses') {
                        $timeprocess = $waktu_proses->format("%a Hari %h Jam %i Menit %s Detik");
                    }

                    $orderplatform = "-";
                    if ($value->orderplatform == 'web' || $value->orderplatform == null) {
                        $orderplatform = "Website";
                    } else if ($value->orderplatform == 'apps') {
                        $orderplatform = "Aplikasi";
                    } else if ($value->orderplatform == 'api') {
                        $orderplatform = "Website";
                    }

                    $detail = array();
                    $detail[] = "<a href=\"javascript:;\" onclick=\"modalStruk('" . stringEncryption('encrypt', $value->id) . "')\">$value->clientcode</a>";
                    $detail[] = DateFormat($value->createddate, 'd F Y H:i:s');
                    $detail[] = $value->productname ?? '- Layanan telah dihapus -';
                    $detail[] = $value->target;
                    $detail[] = IDR($value->qty);
                    $detail[] = IDR($value->price);
                    $detail[] = IDR($value->startcount);
                    $detail[] = IDR($value->remain);
                    $detail[] = $orderplatform;
                    $detail[] = IDR($value->currentsaldo);
                    $detail[] = IDR($value->currentsaldo - $value->price);
                    $detail[] = $status;
                    $detail[] = $timeprocess;

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function report()
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $data = array();
        $data['title'] = $this->merchant->companyname . ' | Laporan SMM';
        $data['content'] = 'smm/report/index';
        $data['pending'] = $this->trorder->select('status, count(*) as total, sum(price) as nominal')
            ->get(array(
                'userid' => getCurrentIdUser(),
                'LOWER(status) =' => 'pending',
                'type' => 'SMM',
                'MONTH(createddate) =' => getCurrentDate('m'),
                'YEAR(createddate) =' => getCurrentDate('Y')
            ))->row();
        $data['processing'] = $this->trorder->select('status, count(*) as total, sum(price) as nominal')
            ->get(array(
                'userid' => getCurrentIdUser(),
                "(LOWER(status) = 'in progress' or LOWER(status) = 'processing') =" => true,
                'type' => 'SMM',
                'MONTH(createddate) =' => getCurrentDate('m'),
                'YEAR(createddate) =' => getCurrentDate('Y')
            ))->row();
        $data['success'] = $this->trorder->select('status, count(*) as total, sum(price) as nominal')
            ->get(array(
                'userid' => getCurrentIdUser(),
                "(LOWER(status) = 'success' or LOWER(status) = 'sukses' or LOWER(status) = 'completed') =" => true,
                'type' => 'SMM',
                'MONTH(createddate) =' => getCurrentDate('m'),
                'YEAR(createddate) =' => getCurrentDate('Y')
            ))->row();
        $data['failed'] = $this->trorder->select('status, count(*) as total, sum(price) as nominal')
            ->get(array(
                'userid' => getCurrentIdUser(),
                "(LOWER(status) = 'gagal' or LOWER(status) = 'failed' or LOWER(status) = 'error' or LOWER(status) = 'partial') =" => true,
                'type' => 'SMM',
                'MONTH(createddate) =' => getCurrentDate('m'),
                'YEAR(createddate) =' => getCurrentDate('Y')
            ))->row();
        $data['transaction'] = $this->trorder->select('date(createddate) as date, count(*) as total, sum(price) as nominal')
            ->group_by('date(createddate)')
            ->order_by('date(createddate)', 'asc')
            ->result(array(
                'userid' => getCurrentIdUser(),
                'type' => 'SMM',
                'MONTH(createddate) =' => getCurrentDate('m'),
                'YEAR(createddate) =' => getCurrentDate('Y')
            ));
        $data['startperiode'] = getCurrentDate('1 F Y');
        $data['endperiode'] = getCurrentDate('t F Y');

        return viewTemplate($this->merchant->id, "master", $data);
    }

    public function filter_report()
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Anda belum login');
            }

            $startdate = getPost('startdate');
            $enddate = getPost('enddate');

            if (empty($startdate) || empty($enddate)) {
                throw new Exception('Tanggal tidak boleh kosong');
            } else if ($enddate < $startdate) {
                throw new Exception('Tanggal akhir tidak boleh kurang dari tanggal awal');
            }

            $data = array();
            $data['pending'] = $this->trorder->select('status, count(*) as total, sum(price) as nominal')
                ->get(array(
                    'userid' => getCurrentIdUser(),
                    'LOWER(status) =' => 'pending',
                    'type' => 'SMM',
                    'DATE(createddate) >=' => $startdate,
                    'DATE(createddate) <=' => $enddate
                ))->row();
            $data['processing'] = $this->trorder->select('status, count(*) as total, sum(price) as nominal')
                ->get(array(
                    'userid' => getCurrentIdUser(),
                    "(LOWER(status) = 'in progress' or LOWER(status) = 'processing') =" => true,
                    'type' => 'SMM',
                    'DATE(createddate) >=' => $startdate,
                    'DATE(createddate) <=' => $enddate
                ))->row();
            $data['success'] = $this->trorder->select('status, count(*) as total, sum(price) as nominal')
                ->get(array(
                    'userid' => getCurrentIdUser(),
                    "(LOWER(status) = 'success' or LOWER(status) = 'sukses' or LOWER(status) = 'completed') =" => true,
                    'type' => 'SMM',
                    'DATE(createddate) >=' => $startdate,
                    'DATE(createddate) <=' => $enddate
                ))->row();
            $data['failed'] = $this->trorder->select('status, count(*) as total, sum(price) as nominal')
                ->get(array(
                    'userid' => getCurrentIdUser(),
                    "(LOWER(status) = 'gagal' or LOWER(status) = 'failed' or LOWER(status) = 'error' or LOWER(status) = 'partial') =" => true,
                    'type' => 'SMM',
                    'DATE(createddate) >=' => $startdate,
                    'DATE(createddate) <=' => $enddate
                ))->row();
            $data['transaction'] = $this->trorder->select('date(createddate) as date, count(*) as total, sum(price) as nominal')
                ->group_by('date(createddate)')
                ->order_by('date(createddate)', 'asc')
                ->result(array(
                    'userid' => getCurrentIdUser(),
                    'type' => 'SMM',
                    'DATE(createddate) >=' => $startdate,
                    'DATE(createddate) <=' => $enddate
                ));
            $data['startperiode'] = DateFormat($startdate, 'd F Y');
            $data['endperiode'] = DateFormat($enddate, 'd F Y');

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => viewTemplate($this->merchant->id, 'smm/report/content', $data, true),
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function print($id)
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            } else if ($this->merchant->licenseid == null) {
                throw new Exception('Anda belum memiliki lisensi');
            }

            $id = stringEncryption('decrypt', $id);
            $merchantid = $this->merchant->id;

            $get = $this->trorder->select('a.clientcode, c.productname, a.price, a.updateddate, a.target, c.category_apikey, a.sn, d.domain, d.companyaddress, d.companyicon, d.companyname, d.strukcontent')
                ->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->join('msproduct c', 'c.id = a.serviceid', 'LEFT')
                ->join('msusers d', 'd.id = b.merchantid')
                ->get(array(
                    'a.id' => $id,
                    "(b.merchantid = '$merchantid' OR a.merchantid_order = '$merchantid') =" => true,
                    'a.userid' => getCurrentIdUser(),
                    'c.category_apikey' => 'SMM'
                ));

            if ($get->num_rows() == 0) {
                return redirect(base_url('dashboard?userid=' . $this->userid));
            }

            $content = viewTemplate($this->merchant->id, "smm/modalprint", array('transaction' => $get->row()), true);

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => $content,
            ), JSON_INVALID_UTF8_IGNORE);
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_print($id)
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $id = stringEncryption('decrypt', $id);
        $price = getGet('price');
        $merchantid = $this->merchant->id;

        $get = $this->trorder->select('a.clientcode, c.productname, a.price, a.updateddate, a.target, c.category_apikey, a.sn, d.domain, d.companyaddress, d.companyicon, d.companyname, d.strukcontent')
            ->join('msusers b', 'b.id = a.userid', 'LEFT')
            ->join('msproduct c', 'c.id = a.serviceid', 'LEFT')
            ->join('msusers d', 'd.id = b.merchantid')
            ->get(array(
                'a.id' => $id,
                "(b.merchantid = '$merchantid' OR a.merchantid_order = '$merchantid') =" => true,
                'a.userid' => getCurrentIdUser(),
                'c.category_apikey' => 'SMM'
            ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('dashboard?userid=' . $this->userid));
        }

        $row = $get->row();

        $data = array();
        $data['title'] = 'Cetak Struk - ' . $row->clientcode;
        $data['transaction'] = $row;
        $data['price'] = $price;

        $dompdf = new Dompdf();
        $dompdf->loadHtml($this->load->view('transaction/print', $data, true));

        $dompdf->setPaper('A5', 'P');

        $dompdf->render();

        $dompdf->stream("Struk Transaksi", array("Attachment" => false));

        exit(0);
    }

    public function add_favourite_services()
    {
        try {
            $this->db->trans_begin();

            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                $this->db->trans_rollback();

                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Anda belum login');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);

            $get = $this->msproduct->get(array(
                'id' => $id,
                'userid' => $this->merchant->id,
                'category_apikey' => 'SMM',
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            $row = $get->row();

            $favourite = $this->productfavourite->total(array(
                'userid' => getCurrentIdUser(),
                'productid' => $row->id
            ));

            if ($favourite > 0) {
                throw new Exception('Produk sudah ada di daftar favorit');
            }

            $insert = array();
            $insert['userid'] = getCurrentIdUser();
            $insert['productid'] = $row->id;
            $insert['createddate'] = getCurrentDate('Y-m-d H:i:s');
            $insert['createdby'] = getCurrentIdUser();
            $insert['updateddate'] = getCurrentDate('Y-m-d H:i:s');
            $insert['updatedby'] = getCurrentIdUser();

            $this->productfavourite->insert($insert);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menambahkan produk ke daftar favorit');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Produk berhasil ditambahkan ke daftar favorit');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function remove_favourite_services()
    {
        try {
            $this->db->trans_begin();

            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                $this->db->trans_rollback();

                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Anda belum login');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);

            $get = $this->msproduct->get(array(
                'id' => $id,
                'userid' => $this->merchant->id,
                'category_apikey' => 'SMM',
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            $row = $get->row();

            $favourite = $this->productfavourite->total(array(
                'userid' => getCurrentIdUser(),
                'productid' => $row->id
            ));

            if ($favourite == 0) {
                throw new Exception('Produk tidak ada di daftar favorit');
            }

            $this->productfavourite->delete(array(
                'userid' => getCurrentIdUser(),
                'productid' => $row->id
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menghapus produk dari daftar favorit');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Produk berhasil dihapus dari daftar favorit');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function favourite()
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $data = array();
        $data['title'] = 'Favorit';
        $data['content'] = 'smm/favourite';

        return viewTemplate($this->merchant->id, 'master', $data);
    }

    public function datatables_favourite_smm()
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            if (isLogin()) {
                $data = array();

                $datatables = $this->datatables->make('ProductFavourite', 'QueryDatatables', 'SearchDatatables');

                $currenttheme = getCurrentThemeConfiguration($this->merchant->id);

                if ($currenttheme != 'HighAdmin-TopNav' && $currenttheme != 'Dason-TopNav' && $currenttheme != 'Able') {
                    $label = "label label";
                } else if ($currenttheme == 'Dason-TopNav' || $currenttheme == 'Able') {
                    $label = "badge bg";
                } else {
                    $label = "badge badge";
                }

                $where = array(
                    'a.userid' => getCurrentIdUser(),
                    'b.category_apikey' => 'SMM',
                );

                if ($this->merchant->multivendor != 1) {
                    $vendor = getCurrentVendor('SMM', $this->merchant->id);
                    $where["(b.vendor = '$vendor' OR b.vendor IS NULL) ="] = true;
                    $where['b.vendorid'] = null;
                } else {
                    $where["((b.vendorid IS NOT NULL AND b.vendorenabled = 1) OR b.vendor IS NULL) ="] = true;
                }

                foreach ($datatables->getData($where) as $key => $value) {
                    if ($value->status == 1) {
                        $status = "<span class=\"$label-success\">Normal</span>";
                    } else {
                        $status = "<span class=\"$label-danger\">Gangguan</span>";
                    }

                    $detail = array();
                    $detail[] = $value->category;
                    $detail[] = $value->productname;
                    $detail[] = IDR($value->minorder);
                    $detail[] = IDR($value->maxorder);
                    $detail[] = IDR($value->price);
                    $detail[] = $status;
                    $detail[] = "<button type=\"button\" class=\"btn btn-icon btn-danger btn-sm mb-1\" onclick=\"deleteFavourite('" . stringEncryption('encrypt', $value->id) . "')\">
                    <i class=\"fa fa-trash  me-1\"></i>
                </button>";

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Anda belum login');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function delete_favourite_smm()
    {
        try {
            $this->db->trans_begin();

            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                $this->db->trans_rollback();

                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Anda belum login');
            }

            $id = getPost('id');
            $id = stringEncryption('decrypt', $id);

            $get = $this->productfavourite->total(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ));

            if ($get == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            $this->productfavourite->delete(array(
                'id' => $id,
                'userid' => getCurrentIdUser()
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal menghapus produk dari daftar favorit');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Produk berhasil dihapus dari daftar favorit');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function information()
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $currentuserid = $this->merchant->id;

        $where_vendor = "";
        if ($this->merchant->multivendor != 1) {
            $vendorsmm = getCurrentVendor('SMM', $this->merchant->id);
            $where_vendor = "vendor = '$vendorsmm' AND vendorid IS NULL";
        } else {
            $where_vendor = "((vendorid IS NOT NULL AND vendorenabled = 1) OR vendor IS NULL)";
        }

        $data = array();
        $data['title'] = 'Informasi Layanan';
        $data['content'] = 'smm/information';
        $data['category'] = $this->db->query("SELECT * FROM ( SELECT category FROM msproduct WHERE userid = $currentuserid AND $where_vendor GROUP BY category UNION ALL SELECT NAME AS category FROM mscategory WHERE userid = $currentuserid GROUP BY NAME ) a WHERE a.category IS NOT NULL ORDER BY a.category ASC")->result();

        return viewTemplate($this->merchant->id, 'master', $data);
    }

    public function datatables_information_smm()
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            if (isLogin()) {
                $data = array();

                $datatables = $this->datatables->make('ProductPriceLog', 'QueryDatatables', 'SearchDatatables');

                $category = getPost('category');

                $where = array(
                    'a.userid' => $this->merchant->id,
                    'b.category_apikey' => 'SMM',
                );

                if ($category != null) {
                    $where['b.category'] = $category;
                }

                $currentuser = getCurrentUser();

                if ($currentuser->roleid == null) {
                    $getrole = $this->msrole->get(array(
                        'createdby' => $this->merchant->id,
                        'isdefault' => '1'
                    ))->row();
                } else {
                    $getrole = $this->msrole->get(array(
                        'createdby' => $this->merchant->id,
                        'id' => $currentuser->roleid
                    ))->row();
                }

                if ($getrole != null) {
                    if ($getrole->discounttype == 'Simple') {
                        $getrolediscount = $this->msrolediscount->get(array(
                            'userid' => $this->merchant->id,
                            'roleid' => $getrole->id
                        ))->row();

                        $discount = $getrolediscount->trxdiscount ?? 0;
                    } else {
                        $discount = $this->msrolediscountadv->get(array(
                            'createdby' => $this->merchant->id,
                            'roleid' => $getrole->id,
                            'servicetype' => 'SMM'
                        ))->result();
                    }
                } else {
                    $getrole = new stdClass();
                    $getrole->discounttype = 'Simple';
                    $discount = 0;
                }

                foreach ($datatables->getData($where) as $key => $value) {
                    if ($value->type == 'UP') {
                        $status = "<span class=\"badge badge-success label label-success bg-success\">Harga Naik</span>";
                    } else {
                        $status = "<span class=\"badge badge-danger label label-danger bg-danger\">Harga Turun</span>";
                    }

                    $detail = array();
                    $detail[] = $value->productname;
                    $detail[] = $status;
                    if ($getrole->discounttype == 'Simple') {
                        $detail[] = "Rp " . IDR($value->oldprice - $discount);
                    } else {
                        $found = false;

                        foreach ($discount as $val) {
                            if ($found) continue;

                            if ($val->startrange <= $value->oldprice && $val->endrange >= $value->oldprice) {
                                if ($val->discounttype == 'Persentase') {
                                    $detail[] = "Rp " . IDR($value->oldprice - ($value->oldprice * $val->nominal / 100));

                                    $found = true;
                                } else {
                                    $detail[] = "Rp " . IDR($value->oldprice - $val->nominal);

                                    $found = true;
                                }
                            }
                        }

                        if ($found == false) {
                            $detail[] = "Rp " . IDR($value->oldprice);
                        }
                    }

                    if ($getrole->discounttype == 'Simple') {
                        $detail[] = "Rp " . IDR($value->newprice - $discount);
                    } else {
                        $found = false;

                        foreach ($discount as $val) {
                            if ($found) continue;

                            if ($val->startrange <= $value->newprice && $val->endrange >= $value->newprice) {
                                if ($val->discounttype == 'Persentase') {
                                    $detail[] = "Rp " . IDR($value->newprice - ($value->newprice * $val->nominal / 100));

                                    $found = true;
                                } else {
                                    $detail[] = "Rp " . IDR($value->newprice - $val->nominal);

                                    $found = true;
                                }
                            }
                        }

                        if ($found == false) {
                            $detail[] = "Rp " . IDR($value->newprice);
                        }
                    }
                    $detail[] = DateFormat($value->createddate, 'd F Y H:i:s');

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Anda belum login');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function modalContact()
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $getContact = $this->mscontact->get(array('userid' => getCurrentIdUser(), 'contacttype' => 'SMM'))->result();

        return JSONResponse(array(
            'RESULT' => 'OK',
            'CONTENT' => viewTemplate($this->merchant->id, "smm/modalcontact", array(
                'data' => $getContact
            ), true)
        ));
    }

    public function export_services_smm()
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            if (isLogin()) {

                $start = 'A1';

                $letter = (string) preg_replace('/[0-9]+/', '', $start);
                $rowstart = (int) preg_replace('/[A-z]+/', '', $start);

                $spreadsheet = new Spreadsheet();
                $sheet = $spreadsheet->getActiveSheet();

                $letters = $letter;
                $sheet->setCellValue($letters++ . $rowstart, "Nama Produk")
                    ->setCellValue($letters++ . $rowstart, "Kategori")
                    ->setCellValue($letters++ . $rowstart, "Min Order")
                    ->setCellValue($letters++ . $rowstart, "Max Order")
                    ->setCellValue($letters++ . $rowstart, "Harga")
                    ->setCellValue($letters . $rowstart++, "Status");

                $where = array(
                    'a.userid' => $this->merchant->id,
                    'a.category_apikey' => 'SMM',
                    'a.subcategory_apikey' => 'SMM',
                    'b.id' => null,
                    'a.status' => 1,
                    'a.category !=' => null
                );

                if ($this->merchant->multivendor != 1) {
                    $vendor = getCurrentVendor('SMM', $this->merchant->id);
                    $where['a.vendor'] = $vendor;
                    $where['a.vendorid'] = null;
                } else {
                    $where['a.vendorid !='] = null;
                    $where['a.vendorenabled'] = 1;
                }

                $category = getGet('category');

                if ($category != null) {
                    $where['a.category'] = $category;
                }

                $currentuser = getCurrentUser();

                if ($currentuser->roleid == null) {
                    $getrole = $this->msrole->get(array(
                        'createdby' => $this->merchant->id,
                        'isdefault' => '1'
                    ))->row();
                } else {
                    $getrole = $this->msrole->get(array(
                        'createdby' => $this->merchant->id,
                        'id' => $currentuser->roleid
                    ))->row();
                }

                $discounttype = $getrole->discounttype;

                if ($getrole->discounttype == 'Simple') {
                    $getrolediscount = $this->msrolediscount->get(array(
                        'userid' => $this->merchant->id,
                        'roleid' => $currentuser->roleid
                    ))->row();

                    $discount = $getrolediscount->trxdiscount ?? 0;
                } else {
                    $discount = $this->msrolediscountadv->get(array(
                        'createdby' => $this->merchant->id,
                        'roleid' => $getrole->id,
                        'servicetype' => 'SMM'
                    ))->result();
                }

                $dataprduct = $this->msproduct->select('a.*, b.id AS disabledid, c.id AS favouriteid')
                    ->join('disabledcategory b', 'b.categoryname = a.category AND b.category_apikey = a.category_apikey AND b.userid = a.userid', 'LEFT')
                    ->join('productfavourite c', 'c.productid = a.id AND c.userid = ' . (getCurrentIdUser() ?? '0'), 'LEFT')
                    ->order_by('a.price, a.category, a.brand')->result($where);

                foreach ($dataprduct as $key => $value) {
                    if ($value->status == 1) {
                        $status = "Normal";
                    } else {
                        $status = "Gangguan";
                    }

                    $harga = '';
                    if ($discounttype == 'Simple') {
                        $harga = IDR($value->price - $discount);
                    } else {
                        $found = false;

                        foreach ($discount as $val) {
                            if ($found) continue;

                            if ($val->startrange < $value->price && $val->endrange > $value->price && strtoupper($val->servicetype) == 'SMM') {
                                if ($val->discounttype == 'Persentase') {
                                    $harga = IDR($value->price - ($value->price * $val->nominal / 100));

                                    $found = true;
                                } else {
                                    $harga = IDR($value->price - $val->nominal);

                                    $found = true;
                                }
                            }
                        }

                        if ($found == false) {
                            $harga = IDR($value->price);
                        }
                    }

                    $letters = $letter;

                    $sheet->setCellValue($letters++ . $rowstart, $value->productname)
                        ->setCellValue($letters++ . $rowstart, $value->category)
                        ->setCellValue($letters++ . $rowstart, IDR($value->minorder))
                        ->setCellValue($letters++ . $rowstart, IDR($value->maxorder))
                        ->setCellValue($letters++ . $rowstart, $harga)
                        ->setCellValue($letters . $rowstart, $status);

                    $rowstart++;
                }


                foreach (range('A', $sheet->getHighestDataColumn()) as $col) {
                    $sheet->getColumnDimension($col)
                        ->setAutoSize(true);
                }

                $sheet->getStyle("A1:$letters" . --$rowstart)->applyFromArray(array(
                    'borders' => array(
                        'allBorders' => array(
                            'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN
                        )
                    )
                ));

                $writer = new Xlsx($spreadsheet);
                $filename = "Daftar Harga SMM";

                header('Content-Type: application/vnd.ms-excel');
                header('Content-Disposition: attachment;filename="' . $filename . '.xlsx"');
                header('Cache-Control: max-age=0');

                $writer->save('php://output');
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}
