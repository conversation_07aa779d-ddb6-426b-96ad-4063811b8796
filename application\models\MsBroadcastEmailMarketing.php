<?php
defined('BASEPATH') or die('No direct script access allowed!');

class MsBroadcastEmailMarketing extends MY_Model
{
    protected $table = 'msbroadcastemailmarketing';
    public $SearchDatatables = array('title', 'subject');

    public function QueryDatatables()
    {
        $this->db->select('a.*')
            ->from($this->table . ' a')
            ->order_by('a.createddate', 'DESC');

        return $this;
    }
}
