<?php
defined('BASEPATH') or die('No direct script access allowed!');

class MsEmailUnsubscribe extends MY_Model
{
    protected $table = 'msemailunsubscribe';
    public $SearchDatatables = array('email', 'reason');

    public function QueryDatatables()
    {
        $this->db->select('a.*, b.name as user_name, c.name as merchant_name')
            ->from($this->table . ' a')
            ->join('msusers b', 'a.userid = b.id', 'LEFT')
            ->join('msusers c', 'a.merchantid = c.id', 'LEFT')
            ->order_by('a.createddate', 'DESC');

        return $this;
    }
}
