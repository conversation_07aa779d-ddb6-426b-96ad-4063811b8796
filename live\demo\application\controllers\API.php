<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property MsUsers $msusers
 * @property MsProduct $msproduct
 * @property DisabledCategory $disabledcategory
 * @property DisabledBrand $disabledbrand
 * @property TrOrder $trorder
 * @property CI_Output $output
 * @property CI_DB_mysqli_driver $db
 * @property ApiKeys $apikeys
 * @property MsRole $msrole
 * @property MsRoleDiscount $msrolediscount
 * @property MsRoleDiscountAdv $msrolediscountadv
 * @property HistoryBalance $historybalance
 * @property ApiKeys_WhatsApp $apikeys_whatsapp
 * @property MsVendorDetail $msvendordetail
 * @property MsStockproduct $msstockproduct
 * @property MsStockproductDetail $msstockproductdetail
 */
class API extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsUsers', 'msusers');
        $this->load->model('MsProduct', 'msproduct');
        $this->load->model('DisabledCategory', 'disabledcategory');
        $this->load->model('TrOrder', 'trorder');
        $this->load->model('ApiKeys', 'apikeys');
        $this->load->model('DisabledBrand', 'disabledbrand');
        $this->load->model('MsRole', 'msrole');
        $this->load->model('MsRoleDiscount', 'msrolediscount');
        $this->load->model('MsRoleDiscountAdv', 'msrolediscountadv');
        $this->load->model('ApiKeys_WhatsApp', 'apikeys_whatsapp');
        $this->load->model('HistoryBalance', 'historybalance');
        $this->load->model('MsVendorDetail', 'msvendordetail');
        $this->load->model('MsStockproduct', 'msstockproduct');
        $this->load->model('MsStockproductDetail', 'msstockproductdetail');
    }

    public function profile()
    {
        $api_id = getPost('api_id');
        $api_key = getPost('api_key');

        if ($api_id == null) {
            $this->output->set_status_header(400);
            return JSONResponse(array(
                'status' => false,
                'message' => 'Bad Request'
            ));
        }

        if ($api_key == null) {
            $this->output->set_status_header(400);
            return JSONResponse(array(
                'status' => false,
                'message' => 'Bad Request'
            ));
        }

        $get = $this->msusers->get(array(
            'id' => $api_id,
            'apikey' => $api_key,
            'merchantid' => $this->merchant->id,
        ));

        if ($get->num_rows() == 0) {
            $this->output->set_status_header(401);
            return JSONResponse(array(
                'status' => false,
                'message' => 'Unauthorized'
            ));
        }

        $user = $get->row();

        if ($user->isdeleted == 1) {
            $this->output->set_status_header(401);
            return JSONResponse(array(
                'status' => false,
                'message' => 'Unauthorized'
            ));
        }

        $this->output->set_status_header(200);
        return JSONResponse(array(
            'status' => true,
            'data' => array(
                'email' => $user->email,
                'name' => $user->name,
                'balance' => (int) $user->balance,
            )
        ));
    }

    public function services()
    {
        $api_id = getPost('api_id');
        $api_key = getPost('api_key');
        $service_type = getPost('service_type');

        if ($api_id == null) {
            $this->output->set_status_header(400);
            return JSONResponse(array(
                'status' => false,
                'message' => 'Bad Request'
            ));
        }

        if ($api_key == null) {
            $this->output->set_status_header(400);
            return JSONResponse(array(
                'status' => false,
                'message' => 'Bad Request'
            ));
        }

        if ($service_type != 'SMM' && $service_type != 'PPOB') {
            $this->output->set_status_header(400);
            return JSONResponse(array(
                'status' => false,
                'message' => 'Bad Request'
            ));
        }

        $get = $this->msusers->get(array(
            'id' => $api_id,
            'apikey' => $api_key,
            'merchantid' => $this->merchant->id,
        ));

        if ($get->num_rows() == 0) {
            $this->output->set_status_header(401);
            return JSONResponse(array(
                'status' => false,
                'message' => 'Unauthorized'
            ));
        }

        $user = $get->row();

        if ($user->isdeleted == 1) {
            $this->output->set_status_header(401);
            return JSONResponse(array(
                'status' => false,
                'message' => 'Unauthorized'
            ));
        }

        if ($user->roleid == null) {
            $getrole = $this->msrole->get(array(
                'createdby' => $this->merchant->id,
                'isdefault' => '1'
            ))->row();
        } else {
            $getrole = $this->msrole->get(array(
                'createdby' => $this->merchant->id,
                'id' => $user->roleid
            ))->row();
        }

        if ($getrole != null) {
            if ($getrole->discounttype == 'Simple') {
                $getrolediscount = $this->msrolediscount->get(array(
                    'userid' => $this->merchant->id,
                    'roleid' => $getrole->id
                ))->row();

                $discount = $getrolediscount->trxdiscount ?? 0;
            } else {
                if ($service_type == 'PPOB') {
                    $discount = $this->msrolediscountadv->get(array(
                        'createdby' => $this->merchant->id,
                        'roleid' => $getrole->id,
                        '(servicetype = "Prabayar" OR servicetype = "Pascabayar") =' => true
                    ))->result();
                } else {
                    $discount = $this->msrolediscountadv->get(array(
                        'createdby' => $this->merchant->id,
                        'roleid' => $getrole->id,
                        'servicetype' => 'SMM'
                    ))->result();
                }
            }
        } else {
            $getrole = new stdClass();
            $getrole->discounttype = 'Simple';
            $getrole->id = 0;
        }

        $disabledcategory = $this->disabledcategory->select('categoryname')
            ->result(array(
                'userid' => $this->userid,
                'category_apikey' => $service_type
            ));

        $disabled = array();
        foreach ($disabledcategory as $key => $value) {
            $disabled[] = $value->categoryname;
        }

        $disabledbrand = $this->disabledbrand->select('brandname')
            ->result(array(
                'userid' => $this->userid,
            ));

        $disabled_brand = array();
        foreach ($disabledbrand as $key => $value) {
            $disabled_brand[] = $value->brandname;
        }

        $where = array(
            'userid' => $this->merchant->id,
            'category_apikey' => $service_type
        );

        if ($this->merchant->multivendor != 1) {
            $vendor = getCurrentVendor($service_type, $this->merchant->id);
            $where['vendor'] = $vendor;
            $where['vendorid'] = null;
        } else {
            $where['vendorid !='] = null;
            $where['vendorenabled'] = 1;
        }

        $product = $this->msproduct->select('id, productname, description, status, price, category, brand, minorder, maxorder, type, subcategory_apikey, admin, profit')
            ->where_not_in('category', $disabled)
            ->where_not_in('brand', $disabled_brand)
            ->result($where);

        $services = array();
        foreach ($product as $key => $value) {
            $price = $value->price;

            if ($value->subcategory_apikey == 'PRABAYAR' || $value->subcategory_apikey == 'SMM') {
                if ($getrole->discounttype == 'Simple') {
                    $price = $value->price - $discount;
                } else {
                    $found = false;

                    foreach ($discount as $val) {
                        if ($found) continue;

                        if ($val->startrange <= $value->price && $val->endrange >= $value->price && strtoupper($val->servicetype) == 'PRABAYAR') {
                            if ($val->discounttype == 'Persentase') {
                                $price = $value->price - ($value->price * $val->nominal / 100);

                                $found = true;
                            } else {
                                $price = $value->price - $val->nominal;

                                $found = true;
                            }
                        }
                    }
                }
            } else if ($value->subcategory_apikey == 'PASCABAYAR') {
                if ($getrole->discounttype == 'Simple') {
                    $price = $value->admin + $value->profit - $discount;
                } else {
                    $found = false;

                    foreach ($discount as $val) {
                        if ($found) continue;

                        if ($val->startrange <= $value->admin + $value->profit && $val->endrange >= $value->admin + $value->profit && strtoupper($val->servicetype) == 'PASCABAYAR') {
                            if ($val->discounttype == 'Persentase') {
                                $price = $value->admin + $value->profit - ($value->admin + $value->profit * $val->nominal / 100);

                                $found = true;
                            } else {
                                $price = $value->admin + $value->profit - $val->nominal;

                                $found = true;
                            }
                        }
                    }
                }
            }

            $detail = array();
            $detail['id'] = (int) $value->id;
            $detail['productname'] = $value->productname;
            $detail['description'] = $value->description;
            $detail['status'] = (int) $value->status;
            $detail['price'] = (int) $price;
            $detail['category'] = $value->category;

            if ($service_type == 'PPOB') {
                $detail['brand'] = $value->brand;
            }

            if ($service_type == 'SMM') {
                $detail['minorder'] = $value->minorder;
                $detail['maxorder'] = $value->maxorder;
                $detail['type'] = $value->type;
            }

            $services[] = $detail;
        }

        $this->output->set_status_header(200);

        return JSONResponse(array(
            'status' => true,
            'data' => $services
        ));
    }

    public function status()
    {
        $api_id = getPost('api_id');
        $api_key = getPost('api_key');
        $id = getPost('id');

        if ($api_id == null) {
            $this->output->set_status_header(400);
            return JSONResponse(array(
                'status' => false,
                'message' => 'Bad Request'
            ));
        }

        if ($api_key == null) {
            $this->output->set_status_header(400);
            return JSONResponse(array(
                'status' => false,
                'message' => 'Bad Request'
            ));
        }

        if ($id == null) {
            $this->output->set_status_header(400);
            return JSONResponse(array(
                'status' => false,
                'message' => 'Bad Request'
            ));
        }

        $get = $this->msusers->get(array(
            'id' => $api_id,
            'apikey' => $api_key,
            'merchantid' => $this->merchant->id,
        ));

        if ($get->num_rows() == 0) {
            $this->output->set_status_header(401);
            return JSONResponse(array(
                'status' => false,
                'message' => 'Unauthorized'
            ));
        }

        $user = $get->row();

        if ($user->isdeleted == 1) {
            $this->output->set_status_header(401);
            return JSONResponse(array(
                'status' => false,
                'message' => 'Unauthorized'
            ));
        }

        $order = $this->trorder->get(array(
            "(id = '$id' OR clientcode = '$id') =" => true,
            'userid' => $user->id
        ));

        if ($order->num_rows() == 0) {
            $this->output->set_status_header(404);
            return JSONResponse(array(
                'status' => false,
                'message' => 'Not Found'
            ));
        }

        $order = $order->row();

        $this->output->set_status_header(200);

        $data = array();
        $data['id'] = (int) $order->id;
        $data['target'] = $order->target . $order->zoneid;
        $data['price'] = (int) $order->price;
        $data['status'] = $order->status;
        $data['note'] = $order->note;

        if ($order->category_apikey == 'PPOB') {
            $data['sn'] = $order->sn;
        }

        if ($order->category_apikey == 'SMM') {
            $data['startcount'] = (int) $order->startcount;
            $data['remain'] = (int) $order->remain;
            $data['qty'] = (int) $order->qty;
        }

        return JSONResponse(array(
            'status' => true,
            'data' => $data
        ));
    }

    private function send_notification($orderid, $userid, $phonenumber)
    {
        // Kirim Firebase notification untuk order
        sendFirebaseNotificationOrder($orderid, $userid);

        $apikeys_whatsapp = $this->apikeys_whatsapp->get(array(
            'userid' => $userid,
        ));

        if ($apikeys_whatsapp->num_rows() == 0) {
            return false;
        }

        $row = $apikeys_whatsapp->row();

        $wablasingateway = new WABlasinGateway(stringEncryption('decrypt', $row->apikey), stringEncryption('decrypt', $row->devicecode));

        $messagenotification = replaceParameterNotification($orderid, $userid);
        if ($messagenotification != null && $phonenumber != null) {
            $phonenumber = changePrefixPhone($phonenumber);

            $send = $wablasingateway->sendMessage($phonenumber, $messagenotification);

            if (!isset($send->RESULT) || (isset($send->RESULT) && $send->RESULT != 'OK')) {
                log_message_user('error', '[WHATSAPP SEND MESSAGE] Send notification failed: ' . json_encode($send), $userid);
            }

            return true;
        }

        return false;
    }

    public function order()
    {
        try {
            $this->db->trans_begin();

            $api_id = getPost('api_id');
            $api_key = getPost('api_key');
            $service = getPost('service');
            $service_type = getPost('service_type');

            // PPOB & SMM
            $target = getPost('target');

            // PPOB
            $zoneid = getPost('zoneid');

            // SMM
            $additional = getPost('additional');
            $qty = getPost('qty');

            if ($api_id == null) {
                throw new Exception('Bad Request');
            }

            if ($api_key == null) {
                throw new Exception('Bad Request');
            }

            if ($service == null) {
                throw new Exception('Bad Request');
            }

            if ($service_type != 'SMM' && $service_type != 'PPOB') {
                throw new Exception('Bad Request');
            } else if ($service_type == 'PPOB') {
                if ($target == null) {
                    throw new Exception('Bad Request');
                }
            } else if ($service_type == 'SMM') {
                if ($target == null) {
                    throw new Exception('Bad Request');
                } else if ($qty == null) {
                    throw new Exception('Bad Request');
                } else if (!is_numeric($qty)) {
                    throw new Exception('Bad Request');
                }
            }

            $get = $this->msusers->get(array(
                'id' => $api_id,
                'apikey' => $api_key,
                'merchantid' => $this->merchant->id,
            ));

            if ($get->num_rows() == 0) {
                throw new Exception('Unauthorized');
            }

            $user = $get->row();

            if ($user->isdeleted == 1) {
                throw new Exception('Unauthorized');
            }

            $currentbalance = getCurrentBalance($user->id, true);

            $disabledcategory = $this->disabledcategory->select('categoryname')
                ->result(array(
                    'userid' => $this->userid,
                    'category_apikey' => $service_type
                ));

            $disabled = array();
            foreach ($disabledcategory as $key => $value) {
                $disabled[] = $value->categoryname;
            }

            $disabledbrand = $this->disabledbrand->select('brandname')
                ->result(array(
                    'userid' => $this->userid,
                ));

            $disabled_brand = array();
            foreach ($disabledbrand as $key => $value) {
                $disabled_brand[] = $value->brandname;
            }

            $where = array(
                'id' => $service,
                'userid' => $this->merchant->id,
                'category_apikey' => $service_type,
            );

            if ($this->merchant->multivendor != 1) {
                $vendor = getCurrentVendor($service_type, $this->merchant->id);
                $where['vendor'] = $vendor;
                $where['vendorid'] = null;
            } else {
                $where['vendorid !='] = null;
                $where['vendorenabled'] = 1;
            }

            $product = $this->msproduct->where_not_in('category', $disabled)
                ->where_not_in('brand', $disabled_brand)
                ->get($where);

            if ($product->num_rows() == 0) {
                throw new Exception('Not Found');
            }

            $product = $product->row();

            if ($product->status == 0) {
                throw new Exception('Service is disabled');
            }

            if ($product->category_apikey == 'SMM') {
                if ($product->minorder > $qty) {
                    throw new Exception('Minimum order is ' . $product->minorder);
                }

                if ($product->maxorder < $qty) {
                    throw new Exception('Maximum order is ' . $product->maxorder);
                }
            }

            if ($product->category_apikey == 'PPOB') {
                if ($product->isstock == 1) {
                    $stock = $product->stock;

                    if ($stock == 0) {
                        throw new Exception('Stock is empty');
                    }
                }

                if ($product->isdatabase == 1 && $product->databasecategory == 'Stock Product') {
                    $databaseid = $product->databaseid;

                    $stockproduct = $this->msstockproduct->get(array(
                        'id' => $databaseid
                    ))->row();

                    if ($stockproduct == null) {
                        throw new Exception('Product not found');
                    } else {
                        $available_stock = $this->msstockproductdetail->total(array(
                            'stockproductid' => $databaseid,
                            'status' => 'Tersedia'
                        ));

                        if ($available_stock == 0) {
                            throw new Exception('Stock is empty');
                        }
                    }
                }

                $price = $product->price;

                $currentuser = $user;

                if ($currentuser->roleid == null) {
                    $getrole = $this->msrole->get(array(
                        'createdby' => $this->merchant->id,
                        'isdefault' => '1'
                    ))->row();
                } else {
                    $getrole = $this->msrole->get(array(
                        'createdby' => $this->merchant->id,
                        'id' => $currentuser->roleid
                    ))->row();
                }

                if ($getrole != null) {
                    if ($getrole->discounttype == "Simple") {
                        $getrolediscount = $this->msrolediscount->get(array(
                            'userid' => $this->merchant->id,
                            'roleid' => $getrole->id,
                        ))->row();

                        $discount = $getrolediscount->trxdiscount ?? 0;
                    } else {
                        $discount = $this->msrolediscountadv->result(array(
                            'createdby' => $this->merchant->id,
                            'roleid' => $getrole->id,
                            'servicetype' => 'Prabayar'
                        ));
                    }
                } else {
                    $getrole = new stdClass();
                    $getrole->discounttype = "Simple";
                    $discount = 0;
                }

                if ($getrole->discounttype == "Simple") {
                    $fixprice = $price - $discount;
                } else {
                    $found = false;
                    foreach ($discount as $key => $value) {
                        if ($found) continue;

                        if ($value->startrange <= $price && $value->endrange >= $price) {
                            if ($value->discounttype == "Persentase") {
                                $fixprice = $price - ($price * $value->nominal / 100);

                                $found = true;
                            } else {
                                $fixprice = $price - $value->nominal;

                                $found = true;
                            }
                        }
                    }

                    if ($found == false) {
                        $fixprice = $price;
                    }
                }

                $vendorprice = $product->vendorprice;
                $potonganprofit = $price - $fixprice;
                $profit = $product->profit - $potonganprofit;
                $price = $fixprice;
            } else {
                $price = $product->price;

                $currentuser = $user;

                if ($currentuser->roleid == null) {
                    $getrole = $this->msrole->get(array(
                        'createdby' => $this->merchant->id,
                        'isdefault' => '1'
                    ))->row();
                } else {
                    $getrole = $this->msrole->get(array(
                        'createdby' => $this->merchant->id,
                        'id' => $currentuser->roleid
                    ))->row();
                }

                if ($getrole != null) {
                    if ($getrole->discounttype == "Simple") {
                        $getrolediscount = $this->msrolediscount->get(array(
                            'userid' => $this->merchant->id,
                            'roleid' => $getrole->id,
                        ))->row();

                        $discount = $getrolediscount->trxdiscount ?? 0;
                    } else {
                        $discount = $this->msrolediscountadv->result(array(
                            'createdby' => $this->merchant->id,
                            'roleid' => $getrole->id,
                            'servicetype' => 'SMM'
                        ));
                    }
                } else {
                    $getrole = new stdClass();
                    $getrole->discounttype = "Simple";
                    $discount = 0;
                }

                if ($getrole->discounttype == "Simple") {
                    $fixprice = $price - $discount;
                } else {
                    $found = false;
                    foreach ($discount as $key => $value) {
                        if ($found) continue;

                        if ($value->startrange <= $price && $value->endrange >= $price) {
                            if ($value->discounttype == "Persentase") {
                                $fixprice = $price - ($price * $value->nominal / 100);

                                $found = true;
                            } else {
                                $fixprice = $price - $value->nominal;

                                $found = true;
                            }
                        }
                    }

                    if ($found == false) {
                        $fixprice = $price;
                    }
                }

                $price = ($fixprice / 1000) * $qty;
                $priceorigin = ($product->price / 1000) * $qty;

                $potonganprofit = $priceorigin - $price;

                $vendorprice = ($product->vendorprice / 1000) * $qty;
                $profit = (($product->profit / 1000) * $qty) - $potonganprofit;
            }

            if ($currentbalance < $price) {
                throw new Exception('Insufficient balance');
            }

            $transaction = $this->trorder->total(array(
                'userid' => $user->id,
                'serviceid' => $service,
                'target' => $target,
                'status' => 'pending'
            ));

            if ($transaction > 0) {
                throw new Exception('You have pending transaction');
            }

            $queuetransaction = false;

            if ($this->merchant->multivendor != 1) {
                $apikey = getCurrentAPIKeys($service_type, $this->merchant->id);
                if ($apikey->balance < $vendorprice) {
                    $queuetransaction = true;
                }
            } else {
                if (getCurrentBalanceVendor($this->merchant->id, $product->vendorid) < $vendorprice) {
                    $queuetransaction = true;
                }
            }

            if ($product->isstock == 1) {
                $this->msproduct->update(array(
                    'id' => $product->id
                ), array(
                    'stock' => $product->stock - 1
                ));
            }

            $insert = array();
            $insert['userid'] = $user->id;
            $insert['clientcode'] = generateTransactionNumber('API');
            $insert['serviceid'] = $service;
            $insert['target'] = $target . $zoneid;
            $insert['qty'] = $qty;
            $insert['price'] = $price;
            $insert['profit'] = $profit;
            $insert['currentsaldo'] = $currentbalance;
            $insert['status'] = 'pending';
            $insert['type'] = $service_type;
            $insert['queuetransaction'] = $queuetransaction ? 1 : 0;
            $insert['category_apikey'] = $product->category_apikey;
            $insert['subcategory_apikey'] = $product->subcategory_apikey;
            $insert['orderplatform'] = 'api';
            $insert['productcode'] = $product->code;
            $insert['vendor'] = $product->vendor;
            $insert['productname_order'] = $product->productname;
            $insert['merchantid_order'] = $this->merchant->id;
            $insert['status_payment'] = 'sukses';
            $insert['vendorid'] = $product->vendorid;

            if ($product->iscustom == 1) {
                $insert['additional'] = $additional;
            }

            $this->trorder->insert($insert);
            $orderid = $this->db->insert_id();

            $inserthistorybalance = array();
            $inserthistorybalance['userid'] = $user->id;
            $inserthistorybalance['type'] = 'OUT';
            $inserthistorybalance['nominal'] = $price;
            $inserthistorybalance['currentbalance'] = $currentbalance;
            $inserthistorybalance['orderid'] = $orderid;
            $inserthistorybalance['createdby'] = $user->id;
            $inserthistorybalance['createddate'] = getCurrentDate();

            $this->historybalance->insert($inserthistorybalance);

            $update = array();
            $update['balance'] = $currentbalance - $price;

            $this->msusers->update(array(
                'id' => $user->id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Internal Server Error');
            }

            pullTransaction($this->merchant->id);

            if ($queuetransaction == false && $product->vendor != null && ENVIRONMENT == 'production') {
                if ($this->merchant->multivendor != 1) {
                    if ($service_type == 'PPOB') {
                        if ($vendor == 'Digiflazz') {
                            $digiflazz = new Digiflazz(stringEncryption('decrypt', $apikey->usercode), stringEncryption('decrypt', $apikey->apikey));

                            $servercode = generateTransactionNumber('ORDER');
                            $topup = $digiflazz->topup($product->code, $target, $servercode);

                            $result = json_decode($topup);

                            if (isset($result->data->status)) {
                                if ($result->data->status != 'Gagal') {
                                    $update = array();
                                    $update['servercode'] = $servercode;
                                    $update['note'] = $result->data->message;
                                    $update['sn'] = $result->data->sn;
                                    $update['status'] = strtolower($result->data->status);
                                    $update['jsonresponse'] = json_encode($result);

                                    $this->trorder->update(array(
                                        'id' => $orderid
                                    ), $update);

                                    $this->db->trans_commit();
                                } else {
                                    if ($result != null) {
                                        log_message_user('error', '[DIGIFLAZZ PRABAYAR] Response: ' . json_encode($result), $this->merchant->id);
                                    }

                                    $responsecode = getResponseCodeDigiflazz($result->data->rc);

                                    throw new Exception($responsecode);
                                }
                            } else {
                                throw new Exception('Transaksi gagal');
                            }
                        } else if ($vendor == 'VIPayment') {
                            $vipayment = new VIPayment(stringEncryption('decrypt', $apikey->usercode), stringEncryption('decrypt', $apikey->apikey));

                            if ($product->vendor == 'VIPayment' && $product->type == 'game') {
                                if (preg_match('/(mobile legends)|(mobilelegends)|(mobile legend)|(mobilelegend)/', strtolower($product->brand))) {
                                    if ($zoneid == null) {
                                        throw new Exception('Zone ID harus diisi');
                                    }

                                    $order = $vipayment->order_game($product->code, $target, $zoneid);
                                } else {
                                    $order = $vipayment->order_game($product->code, $target, null);
                                }
                            } else {
                                $order = $vipayment->order_prepaid($product->code, $target);
                            }

                            if ($order->result) {
                                $update = array();
                                $update['servercode'] = $order->data->trxid;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $orderid
                                ), $update);

                                $this->db->trans_commit();
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[VIPAYMENT PRABAYAR] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception($order->message);
                            }
                        }
                    } else if ($service_type == 'SMM') {
                        if ($vendor == 'BuzzerPanel') {
                            $buzzerpanel = new BuzzerPanel(stringEncryption('decrypt', $apikey->apikey), stringEncryption('decrypt', $apikey->secretkey));
                            $order = $buzzerpanel->order($product->code, $target, $qty);

                            if ($order->status) {
                                $update = array();
                                $update['servercode'] = $order->data->id;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $orderid
                                ), $update);

                                $this->db->trans_commit();
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[BUZZERPANEL ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception($order->data);
                            }
                        } else if ($vendor == 'MedanPedia') {
                            $medanpedia = new MedanPedia(stringEncryption('decrypt', $apikey->usercode), stringEncryption('decrypt', $apikey->apikey));
                            $order = $medanpedia->order($product->code, $target, $qty, $product->iscustom == 1 ? $additional : '');

                            if ($order->status) {
                                $update = array();
                                $update['servercode'] = $order->data->id;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $orderid
                                ), $update);

                                $this->db->trans_commit();
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[MEDANPEDIA ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception($order->data);
                            }
                        } else if ($vendor == 'IrvanKede') {
                            if ($product->iscustom == null) {
                                $additional = '';
                            }

                            $irvankede = new IrvanKede(stringEncryption('decrypt', $apikey->usercode), stringEncryption('decrypt', $apikey->apikey));
                            $order = $irvankede->order(array(
                                'service' => $product->code,
                                'target' => $target,
                                'quantity' => $qty,
                                'custom_comments' => $additional,
                                'custom_link' => $additional
                            ));

                            if ($order->status) {
                                $update = array();
                                $update['servercode'] = $order->data->id;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $orderid
                                ), $update);

                                $this->db->trans_commit();
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[IRVANKEDE ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception($order->data);
                            }
                        } else if ($vendor == 'DailyPanel') {
                            $dailypanel = new DailyPanel(stringEncryption('decrypt', $apikey->apikey), stringEncryption('decrypt', $apikey->secretkey));
                            $order = $dailypanel->order($product->code, $target, $qty, $product->iscustom == 1 ? $additional : '');

                            if (isset($order->success) && $order->success) {
                                $update = array();
                                $update['servercode'] = $order->msg->order_id;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $orderid
                                ), $update);

                                $this->db->trans_commit();
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[DAILYPANEL ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception($order->msg->error);
                            }
                        } else if ($vendor == 'WStore') {
                            $wstore = new WStore(stringEncryption('decrypt', $apikey->usercode), stringEncryption('decrypt', $apikey->apikey), stringEncryption('decrypt', $apikey->secretkey));
                            $order = $wstore->order($product->code, $target, $qty);

                            if (isset($order->response) && $order->response) {
                                $update = array();
                                $update['servercode'] = $order->data->id;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $orderid
                                ), $update);

                                $this->db->trans_commit();
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[WSTORE ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception($order->data->msg);
                            }
                        } else if ($vendor == 'UNDRCTRL') {
                            $undrctrl = new UNDRCTRL(stringEncryption('decrypt', $apikey->apikey));
                            $order = $undrctrl->order($product->code, $target, $qty);

                            if (isset($order->order)) {
                                $update = array();
                                $update['servercode'] = $order->order;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $orderid
                                ), $update);

                                $this->db->trans_commit();
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[UNDRCTRL ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception('Gagal melakukan pembelian');
                            }
                        } else if ($vendor == 'SosmedOnline') {
                            $sosmedonline = new SosmedOnline(stringEncryption('decrypt', $apikey->usercode), stringEncryption('decrypt', $apikey->apikey));
                            $order = $sosmedonline->order($product->code, $target, $qty, $product->iscustom == 1 ? $additional : '');

                            if (isset($order->status) && $order->status) {
                                $update = array();
                                $update['servercode'] = $order->data->id;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $orderid
                                ), $update);

                                $this->db->trans_commit();
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[SOSMEDONLINE ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception('Gagal melakukan pembelian');
                            }
                        } else if ($vendor == 'SosmedOnlineVIP') {
                            $sosmedonlinevip = new SosmedOnlineVIP(stringEncryption('decrypt', $apikey->usercode), stringEncryption('decrypt', $apikey->apikey));
                            $order = $sosmedonlinevip->order($product->code, $target, $qty, $product->iscustom == 1 ? $additional : '');

                            if (isset($order->status) && $order->status) {
                                $update = array();
                                $update['servercode'] = $order->data->id;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $orderid
                                ), $update);

                                $this->db->trans_commit();
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[SOSMEDONLINEVIP ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception('Gagal melakukan pembelian');
                            }
                        } else if ($vendor == 'DjuraganSosmed') {
                            $djuragansosmed = new DjuraganSosmed(stringEncryption('decrypt', $apikey->apikey));
                            $order = $djuragansosmed->order($product->code, $target, $qty);

                            if (isset($order->order)) {
                                $update = array();
                                $update['servercode'] = $order->order;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $orderid
                                ), $update);

                                $this->db->trans_commit();
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[DJURAGANSOSMED ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception('Gagal melakukan pembelian');
                            }
                        } else if ($vendor == 'SMMRaja') {
                            $smmraja = new SMMRaja(stringEncryption('decrypt', $apikey->apikey));
                            $order = $smmraja->order($product->code, $target, $qty);

                            if (isset($order->order)) {
                                $update = array();
                                $update['servercode'] = $order->order;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $orderid
                                ), $update);

                                $this->db->trans_commit();
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[SMMRAJA ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception('Gagal melakukan pembelian');
                            }
                        } else if ($vendor == 'SMMIllusion') {
                            $smmillusion = new SMMIllusion(stringEncryption('decrypt', $apikey->apikey));
                            $order = $smmillusion->order($product->code, $target, $qty);

                            if (isset($order->order)) {
                                $update = array();
                                $update['servercode'] = $order->order;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $orderid
                                ), $update);

                                $this->db->trans_commit();
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[SMMILLUSION ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception('Gagal melakukan pembelian');
                            }
                        } else if ($vendor == 'V1Pedia') {
                            $v1pedia = new V1Pedia(stringEncryption('decrypt', $apikey->usercode), stringEncryption('decrypt', $apikey->apikey), stringEncryption('decrypt', $apikey->secretkey));
                            $order = $v1pedia->order($product->code, $target, $qty);

                            if (isset($order->response) && $order->response) {
                                $update = array();
                                $update['servercode'] = $order->data->id;
                                $update['jsonresponse'] = json_encode($order);

                                $this->trorder->update(array(
                                    'id' => $orderid
                                ), $update);

                                $this->db->trans_commit();
                            } else {
                                if ($order != null) {
                                    log_message_user('error', '[V1PEDIA ORDER] Response: ' . json_encode($order), $this->merchant->id);
                                }

                                throw new Exception('Gagal melakukan pembelian');
                            }
                        }
                    }
                } else {
                    $order = $this->msvendordetail->select('a.*, b.default_config')
                        ->join('msvendor b', 'b.id = a.vendorid')
                        ->get(array(
                            'a.vendorid' => $product->vendorid,
                            'a.apitype' => 'Order'
                        ));

                    if ($order->num_rows() == 0) {
                        throw new Exception('Vendor tidak ditemukan');
                    }

                    $vendor_order = $order->row();

                    if ($vendor_order->default_config == null) {
                        throw new Exception('Konfigurasi vendor tidak ditemukan');
                    }

                    $response_indicator = json_decode($vendor_order->response_indicator);
                    $response_setting = json_decode($vendor_order->response_setting);

                    $dynamicvendor = new DynamicVendor($product->vendorid, json_decode($vendor_order->default_config, true));
                    $order = $dynamicvendor->order($orderid);

                    if (
                        ($response_indicator->index == null && isset($order[$response_indicator->key]) && (
                            $response_indicator->datatype == 'string' && $order[$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'number' && $order[$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'boolean' && $order[$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'exists' && isset($order[$response_indicator->key])
                        ))
                        || ($response_indicator->index != null && isset($order[$response_indicator->index][$response_indicator->key]) && (
                            $response_indicator->datatype == 'string' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'number' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'boolean' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'exists' && isset($order[$response_indicator->index][$response_indicator->key])
                        ))
                    ) {
                        $var_referenceid = $response_setting->referenceid ?? null;
                        $var_price = $response_setting->price ?? null;
                        $var_status = $response_setting->status ?? null;
                        $var_note = $response_setting->note ?? null;
                        $var_sn = $response_setting->sn ?? null;
                        $var_errorrefund = $response_setting->errorrefund;

                        $exploding_errorefund = explode('[#]', strtolower($var_errorrefund ?? ''));

                        if ($response_setting->index != null) {
                            $order = $order[$response_setting->index] ?? null;
                        }

                        $referenceid = $var_referenceid != null ? ($order[$var_referenceid] ?? null) : null;
                        $price = $var_price != null ? ($order[$var_price] ?? null) : null;
                        $status = $var_status != null ? ($order[$var_status] ?? null) : null;
                        $note = $var_note != null ? ($order[$var_note] ?? null) : null;
                        $sn = $var_sn != null ? ($order[$var_sn] ?? null) : null;

                        if ($status != null) {
                            if (in_array($status, $exploding_errorefund)) {
                                log_message_user('error', '[MULTIVENDOR ORDER] Failed to parsing error refund, response: ' . json_encode($order), $this->merchant->id);

                                throw new Exception('Gagal melakukan transaksi');
                            }
                        } else {
                            if ($var_status == null) {
                                $status = 'pending';
                            } else if ($var_status != null && $status == null) {
                                log_message_user('error', '[MULTIVENDOR ORDER] Failed to parsing status, response: ' . json_encode($order), $this->merchant->id);

                                throw new Exception('Gagal melakukan transaksi');
                            }
                        }

                        $update = array();
                        $update['jsonresponse'] = json_encode($order);

                        if ($referenceid != null) {
                            $update['servercode'] = $referenceid;
                        }

                        if ($price != null) {
                            $update['price'] = $price;
                        }

                        if ($status != null) {
                            $update['status'] = $status;
                        }

                        if ($note != null) {
                            $update['note'] = $note;
                        }

                        if ($sn != null) {
                            $update['sn'] = $sn;
                        }

                        $this->trorder->update(array(
                            'id' => $orderid
                        ), $update);

                        $this->db->trans_commit();
                    } else {
                        log_message_user('error', '[MULTIVENDOR ORDER] Failed to parse response: ' . json_encode($order), $this->merchant->id);

                        throw new Exception('Gagal melakukan transaksi');
                    }
                }
            } else {
                if ($product->isdatabase == 1 && $product->databasecategory == 'Stock Product') {
                    $stockproductdetail = $this->msstockproductdetail->get(array(
                        'stockproductid' => $databaseid,
                        'status' => 'Tersedia'
                    ))->row();

                    $updatestockproductdetail = array();
                    $updatestockproductdetail['status'] = 'Tidak Tersedia';
                    $updatestockproductdetail['orderid'] = $orderid;

                    $this->msstockproductdetail->update(array(
                        'id' => $stockproductdetail->id
                    ), $updatestockproductdetail);

                    $update = array();
                    $update['status'] = 'success';
                    $update['sn'] = stringEncryption('decrypt', $stockproductdetail->data);

                    $this->trorder->update(array(
                        'id' => $orderid
                    ), $update);
                }

                $this->db->trans_commit();
            }

            $this->send_notification($orderid, $this->merchant->id, $user->phonenumber);

            $this->output->set_status_header(201);

            return JSONResponse(array(
                'status' => true,
                'data' => array(
                    'orderid' => $orderid,
                    'price' => $price,
                    'status' => 'pending',
                )
            ));
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            $this->output->set_status_header(500);

            return JSONResponse(array(
                'status' => false,
                'message' => $ex->getMessage()
            ));
        }
    }
}
