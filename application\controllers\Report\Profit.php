<?php

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property Datatables $datatables
 * @property TrOrder $trorder
 */
class Profit extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('TrOrder', 'trorder');
    }

    public function monthly()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        } else if (getCurrentUser()->licenseid == null) {
            return redirect(base_url('dashboard'));
        }

        $data = array();
        $data['title'] = 'Server PPOB & SMM - Keuntungan Bulanan';
        $data['content'] = 'report/profit/monthly';

        return $this->load->view('master', $data);
    }

    public function datatables_monthly()
    {
        try {
            if (isLogin() && isUser() && getCurrentUser()->licenseid != null) {

                $datatables = $this->datatables->make(
                    'ReportProfitMonthly',
                    'QueryDatatables',
                    'SearchDatatables'
                );


                $rows = $datatables->getData([
                    'b.merchantid' => getCurrentIdUser(),
                    "(LOWER(a.status) = 'success' OR LOWER(a.status) = 'sukses' OR LOWER(a.status) = 'completed') =" => true
                ]);


                $data = [];
                foreach ($rows as $value) {
                    $unrealized_row = $this->trorder->select('SUM(a.profit) AS unrealized')
                        ->join('msusers b', 'b.id = a.userid', 'LEFT')
                        ->where("MONTH(a.createddate)", $value->month)
                        ->where("YEAR(a.createddate)", $value->year)
                        ->where("(LOWER(a.status) = 'pending' OR LOWER(a.status) = 'in progress' OR LOWER(a.status) = 'processing')")
                        ->where("(b.merchantid = '" . getCurrentIdUser() . "' OR a.merchantid_order = '" . getCurrentIdUser() . "')")
                        ->row();

                    $unrealized = $unrealized_row->unrealized ?? 0;



                    $url = base_url("report/profit/monthly/export?month={$value->month}&year={$value->year}");
                    $actions = '<a href="' . $url . '" class="btn btn-icon btn-danger btn-sm mb-1">'
                        . '<i class="fa fa-file"></i>'
                        . '</a>';

                    // 3) Bangun array baris (5 kolom sesuai header)
                    $data[] = [
                        date('F', mktime(0, 0, 0, $value->month, 10)) . ' ' . $value->year,
                        'Rp ' . IDR($value->omset),
                        'Rp ' . IDR($value->profit),
                        'Rp ' . IDR($unrealized),
                        $actions
                    ];
                }

                // Kirim JSON ke DataTables
                return $datatables->json($data);
            }

            throw new Exception('Access denied');
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function process_export_excel()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login'));
        } else if (!isUser()) {
            return redirect(base_url('dashboard'));
        }

        $month = getGet('month');
        $year = getGet('year');
        $currentiduser = getCurrentIdUser();



        $start = 'A1';

        $letter = (string) preg_replace('/[0-9]+/', '', $start);
        $rowstart = (int) preg_replace('/[A-z]+/', '', $start);
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $letters = $letter;
        $sheet->setCellValue($letters++ . $rowstart, "Tanggal")
            ->setCellValue($letters++ . $rowstart, "Omset PPOB")
            ->setCellValue($letters++ . $rowstart, "Omset SMM")
            ->setCellValue($letters++ . $rowstart, "Keuntungan PPOB Terealisasi")
            ->setCellValue($letters++ . $rowstart, "Keuntungan PPOB Belum Terealisasi")
            ->setCellValue($letters++ . $rowstart, "Keuntungan SMM Terealisasi")
            ->setCellValue($letters++ . $rowstart, "Keuntungan SMM Belum Terealisasi")
            ->setCellValue($letters++ . $rowstart, "Total Omset")
            ->setCellValue($letters++ . $rowstart, "Total Keuntungan Terealisasi")
            ->setCellValue($letters   . $rowstart, "Total Keuntungan Belum Terealisasi");


        $rowstart++;

        $number = date('t', strtotime("$year-$month-01"));

        for ($i = 1; $i <= $number; $i++) {
            $num = date('Y-m-d', strtotime("$year-$month-$i"));
            $date = date('d F Y', strtotime("$year-$month-$i"));

            $getdatappob = $this->trorder->select('sum(a.price) AS omset, sum(a.profit) as profit')
                ->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->where('type', 'PPOB')
                ->where('date(a.createddate)', $num)
                ->row(array(
                    "(lower(a.status) = 'success' OR lower(a.status) = 'sukses' OR lower(a.status) = 'completed') =" => true,
                    "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true,
                ));

            $getdatasmm = $this->trorder->select('sum(a.price) AS omset, sum(a.profit) as profit')
                ->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->where('type', 'SMM')
                ->where('date(a.createddate)', $num)
                ->row(array(
                    "(lower(a.status) = 'success' OR lower(a.status) = 'sukses' OR lower(a.status) = 'completed') =" => true,
                    "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true,
                ));

            $getdataall = $this->trorder->select('sum(a.price) AS omset, sum(a.profit) as profit')
                ->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->where('date(a.createddate)', $num)
                ->row(array(
                    "(lower(a.status) = 'success' OR lower(a.status) = 'sukses' OR lower(a.status) = 'completed') =" => true,
                    "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true,
                ));
            $getunrealppob = $this->trorder->select('sum(a.profit) as profit')
                ->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->where('type', 'PPOB')
                ->where('date(a.createddate)', $num)
                ->row(array(
                    "(lower(a.status) = 'pending' OR lower(a.status) = 'in progress' OR lower(a.status) = 'processing') =" => true,
                    "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true,
                ));

            $getunrealsmm = $this->trorder->select('sum(a.profit) as profit')
                ->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->where('type', 'SMM')
                ->where('date(a.createddate)', $num)
                ->row(array(
                    "(lower(a.status) = 'pending' OR lower(a.status) = 'in progress' OR lower(a.status) = 'processing') =" => true,
                    "(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser') =" => true,
                ));

            $letters = $letter;
            $sheet->setCellValue($letters++ . $rowstart, $date)
                ->setCellValue($letters++ . $rowstart, "Rp " . IDR($getdatappob->omset  ?? 0))
                ->setCellValue($letters++ . $rowstart, "Rp " . IDR($getdatasmm->omset   ?? 0))
                ->setCellValue($letters++ . $rowstart, "Rp " . IDR($getdatappob->profit ?? 0))
                ->setCellValue($letters++ . $rowstart, "Rp " . IDR($getunrealppob->profit ?? 0))
                ->setCellValue($letters++ . $rowstart, "Rp " . IDR($getdatasmm->profit ?? 0))
                ->setCellValue($letters++ . $rowstart, "Rp " . IDR($getunrealsmm->profit ?? 0))
                ->setCellValue($letters++ . $rowstart, "Rp " . IDR($getdataall->omset  ?? 0))
                ->setCellValue($letters++ . $rowstart, "Rp " . IDR($getdataall->profit ?? 0))
                ->setCellValue($letters   . $rowstart, "Rp " . IDR(($getunrealppob->profit ?? 0) + ($getunrealsmm->profit ?? 0)));

            $rowstart++;
        }

        foreach (range('A', $sheet->getHighestDataColumn()) as $col) {
            $sheet->getColumnDimension($col)
                ->setAutoSize(true);
        }

        $sheet->getStyle("A1:$letters" . --$rowstart)->applyFromArray(array(
            'borders' => array(
                'allBorders' => array(
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN
                )
            )
        ));

        $writer = new Xlsx($spreadsheet);
        $filename = "Laporan Keuntungan Bulan " . date('F', mktime(0, 0, 0, $month, 10)) . ' ' . $year;

        header('Content-Type: application/vnd.ms-excel');
        header('Content-Disposition: attachment;filename="' . $filename . '.xlsx"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
    }
    public function chart_data_monthly()
    {
        try {
            if (!isLogin() || !isUser() || getCurrentUser()->licenseid == null) {
                throw new Exception('Unauthorized');
            }

            $month = getGet('month');
            $year = getGet('year');
            $currentiduser = getCurrentIdUser();
            $number = date('t', strtotime("$year-$month-01"));

            $data = [];

            for ($i = 1; $i <= $number; $i++) {
                $date = date('Y-m-d', strtotime("$year-$month-$i"));
                $label = date('d', strtotime($date)); // contoh: 01, 02, 03

                $realized = $this->trorder->select('SUM(a.profit) as profit')
                    ->join('msusers b', 'b.id = a.userid', 'LEFT')
                    ->where('DATE(a.createddate)', $date)
                    ->where("(LOWER(a.status) = 'success' OR LOWER(a.status) = 'sukses' OR LOWER(a.status) = 'completed')")
                    ->where("(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser')")
                    ->row();

                $unrealized = $this->trorder->select('SUM(a.profit) as profit')
                    ->join('msusers b', 'b.id = a.userid', 'LEFT')
                    ->where('DATE(a.createddate)', $date)
                    ->where("(LOWER(a.status) = 'pending' OR LOWER(a.status) = 'processing' OR LOWER(a.status) = 'in progress')")
                    ->where("(b.merchantid = '$currentiduser' OR a.merchantid_order = '$currentiduser')")
                    ->row();

                $data[] = [
                    'label' => $label,
                    'realized' => (float) ($realized->profit ?? 0),
                    'unrealized' => (float) ($unrealized->profit ?? 0)
                ];
            }

            return JSONResponse([
                'status' => 'SUCCESS',
                'message' => 'Data loaded',
                'data' => $data
            ]);
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }
}