<?php

use Duit<PERSON>\Api;
use iPaymu\iPaymu;
use Midtrans\Config;
use Midtrans\Snap;

defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsUsers $msusers
 * @property Datatables $datatables
 * @property MsPaymentMethod $mspaymentmethod
 * @property MsPaymentGateway $mspaymentgateway
 * @property CI_DB_mysqli_driver $db
 * @property Deposits $deposits
 * @property FeePaymentGateway $feepaymentgateway
 * @property UserBuyNotificationHandler $userbuynotificationhandler
 * @property HistoryBalance $historybalance
 * @property ApiKeys_WhatsApp
 */
class Deposit extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Deposits', 'deposits');
        $this->load->model('MsPaymentMethod', 'mspaymentmethod');
        $this->load->model('MsPaymentGateway', 'mspaymentgateway');
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('FeePaymentGateway', 'feepaymentgateway');
        $this->load->model('UserBuyNotificationHandler', 'userbuynotificationhandler');
        $this->load->model('HistoryBalance', 'historybalance');
        $this->load->model('ApiKeys_WhatsApp', 'apikeys_whatsapp');
    }

    public function topup()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $where = array(
            'a.userid' => getCurrentIdUser(),
            'a.merchantid' => $this->merchant->id
        );

        $data = array();
        $data['title'] = $this->merchant->companyname . ' | Topup';
        $data['content'] = 'deposit/topup';

        if (getCurrentThemeConfiguration($this->merchant->id) == 'Fin-App') {
            $data['history'] = $this->deposits->QueryDatatables()->selfGet($where)->result();
            $data['back'] = base_url('history?userid=' . $this->userid);
        }

        return viewTemplate($this->merchant->id, "master", $data);
    }

    public function process_topup()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            }

            $paymentmethod = getPost('paymentmethod');
            $payment = getPost('payment');
            $nominal = getPost('nominal');
            $nominal = str_replace('.', '', $nominal);
            $nominal = str_replace(',', '', $nominal);
            $pin = getPost('pin');
            $phone = getPost('phone');

            if ($paymentmethod == null) {
                throw new Exception('Metode Pembayaran wajib diisi');
            } else if ($payment == null) {
                throw new Exception('Pembayaran wajib diisi');
            } else if ($nominal == null) {
                throw new Exception('Nominal wajib diisi');
            } else if (!is_numeric($nominal)) {
                throw new Exception('Nominal harus berupa angka');
            } else if ($pin == null) {
                throw new Exception('PIN Transaksi wajib diisi');
            }

            $currentuser = getCurrentUser();

            if (stringEncryption('encrypt', $pin) != $currentuser->pin) {
                throw new Exception('PIN Transaksi yang anda masukkan salah');
            }

            // Cek apakah user sudah KYC
            if ($currentuser->is_kyc != 1 && $nominal > 500000) {
                throw new Exception('Maksimal deposit untuk akun yang belum KYC adalah Rp 500.000');
            }

            $spamCheck = $this->deposits->total(array(
                'userid' => getCurrentIdUser(),
                'status' => 'Pending'
            ));

            if ($spamCheck >= 2) {
                throw new Exception('Terdapat 2 permintaan topup yang masih pending, Silahkan selesaikan pembayaran terlebih dahulu');
            }

            $limit = $this->msusers->get(array(
                'id' => $this->merchant->id
            ))->row();

            if ($limit->balancelimit != null && $limit->balancelimit > 0) {
                if ($nominal + $currentuser->balance > $limit->balancelimit) {
                    throw new Exception('Anda melebihi batas Maksimal Penyimpanan Saldo, Jumlah saldo Maksimal yang dapat disimpan ' . IDR($limit->balancelimit));
                }
            }

            $insert = array();
            $insert['userid'] = getCurrentIdUser();
            $insert['code'] = generateTransactionNumber('DEPOSIT');
            $insert['status'] = 'Pending';
            $insert['merchantid'] = $this->merchant->id;
            $insert['phonenumber'] = $currentuser->phonenumber;
            $insert['depositplatform'] = 'web';

            if ($paymentmethod == 'Manual') {
                $get = $this->mspaymentmethod->get(array(
                    'userid' => $this->merchant->id,
                    'id' => $payment,
                ));

                if ($get->num_rows() == 0) {
                    throw new Exception('Pembayaran tidak ditemukan');
                }

                $row = $get->row();

                if ($row->isdisabled == 1) {
                    throw new Exception('Pembayaran yang anda pilih tidak aktif');
                } elseif ($row->minnominal > $nominal) {
                    throw new Exception('Minimal Topup Rp' . IDR($row->minnominal));
                } else if ($row->maxnominal != 0 && $row->maxnominal < $nominal) {
                    throw new Exception('Maksimal Topup Rp' . IDR($row->maxnominal));
                } else if ($row->isunique == 1) {
                    if ($this->merchant->uniquenominal_start != null && $this->merchant->uniquenominal_end != null && $this->merchant->uniquenominal_start != 0 && $this->merchant->uniquenominal_end != 0) {
                        $unique = rand($this->merchant->uniquenominal_start, $this->merchant->uniquenominal_end);
                    } else {
                        $unique = rand(100, 999);
                    }

                    $nominal = $nominal + $unique;

                    if ($row->uniqueadmin == 1) {
                        $insert['uniqueadmin'] = $unique;
                    }
                }

                $insert['payment'] = $row->paymentmethod;
                $insert['nominal'] = $nominal;
                if ($row->image == null) {
                    $insert['note'] = "Silahkan transfer sebesar Rp " . IDR($insert['nominal']) . ",- Ke Rekening: $row->paymentmethod, no. $row->accountnumber a.n. $row->accountname. Batas waktu transfer 24 jam";
                } else {
                    $insert['note'] = "Silahkan transfer sebesar Rp " . IDR($insert['nominal']) . ",- Ke Rekening: $row->paymentmethod, a.n. $row->accountname. Batas waktu transfer 24 jam";
                }
                $insert['paymenttype'] = 'Manual';
                $insert['paymentmethodid'] = $row->id;

                if ($row->isbonus == 1) {
                    $insert['isbonus'] = 1;

                    if ($row->bonustype == 'Nominal') {
                        $insert['nominalbonus'] = $row->nominalbonus;
                    } else if ($row->bonustype == 'Persentase') {
                        $insert['nominalbonus'] = $nominal * (((int) $row->nominalbonus) / 100);
                    }
                }

                if ($row->feetype != null) {
                    $feeamount = 0;

                    if ($row->feetype == 'Persentase') {
                        $feeamount = $nominal * (((int) $row->nominalfee) / 100);
                    } else if ($row->feetype == 'Nominal') {
                        $feeamount = $row->nominalfee;
                    }

                    if ($feeamount > 0 && ($nominal - $feeamount) < 0) {
                        throw new Exception('Nominal Topup tidak boleh kurang dari biaya admin');
                    }

                    $insert['fee'] = $feeamount;
                }
            } else if ($paymentmethod == 'Otomatis') {
                $payment = stringEncryption('decrypt', $payment);
                $insert['paymenttype'] = 'Otomatis';

                if (!is_numeric($payment)) {
                    $json = json_decode($payment);

                    if (!is_object($json) && !isset($json->type)) {
                        $paymentgateway = $this->mspaymentgateway->get(array(
                            'userid' => $this->merchant->id,
                            'type' => 'Payment Gateway',
                            "(isdisabled IS NULL OR isdisabled = 0) =" => true
                        ))->row();

                        if ($paymentgateway == null) {
                            throw new Exception('Pembayaran tidak ditemukan');
                        } else if ($paymentgateway->isdisabled == 1) {
                            throw new Exception('Pembayaran yang anda pilih tidak aktif');
                        }

                        if ($paymentgateway->minnominal > $nominal) {
                            throw new Exception('Minimal Topup Rp' . IDR($paymentgateway->minnominal));
                        } else if ($paymentgateway->maxnominal != 0 && $paymentgateway->maxnominal < $nominal) {
                            throw new Exception('Maksimal Topup Rp' . IDR($paymentgateway->maxnominal));
                        }

                        if ($paymentgateway != null && $paymentgateway->vendor == 'Midtrans' && $paymentgateway->addons != null) {
                            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                            $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                            if (isset($addons->enabled_payments)) {
                                if (in_array($payment, $addons->enabled_payments) && isset(getMidtransPayments()[$payment])) {
                                    $fee = $this->feepaymentgateway->row(array(
                                        'paymentgatewayid' => $paymentgateway->id,
                                        'paymentname' => $payment,
                                        "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                    ));

                                    if ($fee != null) {
                                        $feeamount = 0;

                                        if ($fee->feetype == 'Persentase') {
                                            $feeamount = $nominal * $fee->fee / 100;
                                        } else if ($fee->feetype == 'Nominal') {
                                            $feeamount = $fee->fee;
                                        }

                                        if ($feeamount > 0 && ($nominal - $feeamount) < 0) {
                                            throw new Exception('Nominal Topup terlalu kecil');
                                        }

                                        $insert['fee'] = $feeamount;
                                    }

                                    Config::$serverKey = $detail->serverkey;
                                    Config::$clientKey = $detail->clientkey;
                                    Config::$isProduction = ENVIRONMENT == 'production' ? true : false;

                                    $params = array(
                                        'transaction_details' => array(
                                            'order_id' => $insert['code'],
                                            'gross_amount' => $nominal,
                                        ),
                                        'enabled_payments' => array(
                                            $payment
                                        )
                                    );

                                    try {
                                        $snapToken = Snap::getSnapToken($params);
                                    } catch (Exception $snapex) {
                                        if ($snapex->getMessage() != null) {
                                            log_message_user('error', "[MIDTRANS TOPUP] Response: " . $snapex->getMessage(), $this->merchant->id);
                                        }

                                        throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                                    }

                                    $insert['payment'] = getMidtransPayments()[$payment];
                                    $insert['nominal'] = $nominal;
                                    $insert['gatewayvendor'] = $paymentgateway->vendor;
                                    $insert['snaptoken'] = $snapToken;
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            } else {
                                throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                            }
                        } else if ($paymentgateway != null && $paymentgateway->vendor == 'Tripay' && $paymentgateway->addons != null) {
                            if ($payment == 'OVO') {
                                if ($phone == null) {
                                    throw new Exception('Nomor Handphone OVO tidak boleh kosong');
                                } else if (!is_numeric($phone)) {
                                    throw new Exception('Nomor Handphone OVO tidak valid');
                                } else {
                                    $insert['phonenumber'] = $phone;
                                }
                            }

                            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                            $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                            if (isset($addons->channel_payments)) {
                                if (in_array($payment, $addons->channel_payments) && isset(getTripayPayments()[$payment])) {
                                    $fee = $this->feepaymentgateway->row(array(
                                        'paymentgatewayid' => $paymentgateway->id,
                                        'paymentname' => $payment,
                                        "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                    ));

                                    if ($fee != null) {
                                        $feeamount = 0;

                                        if ($fee->feetype == 'Persentase') {
                                            $feeamount = $nominal * $fee->fee / 100;
                                        } else if ($fee->feetype == 'Nominal') {
                                            $feeamount = $fee->fee;
                                        }

                                        if ($feeamount > 0 && ($nominal - $feeamount) < 0) {
                                            throw new Exception('Nominal Topup terlalu kecil');
                                        }

                                        $insert['fee'] = $feeamount;
                                    }

                                    $tripay = new Tripay($detail->merchantcode, $detail->apikey, $detail->privatekey);
                                    $request = $tripay->requestTransaction($payment, $insert['code'], $nominal, getCurrentUser()->name, getCurrentUser()->email, $phone != null ? $phone : getCurrentUser()->phonenumber, "Deposit Saldo Akun", 1, base_url('deposit/history?userid=' . $this->userid));

                                    if (isset($request->success) && $request->success) {
                                        $insert['payment'] = getTripayPayments()[$payment];
                                        $insert['nominal'] = $nominal;
                                        $insert['gatewayvendor'] = $paymentgateway->vendor;
                                        $insert['checkouturl'] = $request->data->checkout_url;
                                        $insert['servercode'] = $request->data->reference;
                                        $insert['jsonresponse'] = json_encode($request);
                                    } else {
                                        if ($request != null) {
                                            log_message_user('error', '[TRIPAY TOPUP] Response: ' . json_encode($request), $this->merchant->id);
                                        }

                                        if (isset($request->message) && $request->message) {
                                            throw new Exception($request->message);
                                        } else {
                                            throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                                        }
                                    }
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            } else {
                                throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                            }
                        } else if ($paymentgateway != null && $paymentgateway->vendor == 'iPaymu' && $paymentgateway->addons != null) {
                            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                            $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                            if (isset($addons->channel_payments)) {
                                if (in_array($payment, $addons->channel_payments) && isset(getIpaymuPayments()[$payment])) {
                                    $fee = $this->feepaymentgateway->row(array(
                                        'paymentgatewayid' => $paymentgateway->id,
                                        'paymentname' => $payment,
                                        "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                    ));

                                    if ($fee != null) {
                                        $feeamount = 0;

                                        if ($fee->feetype == 'Persentase') {
                                            $feeamount = $nominal * $fee->fee / 100;
                                        } else if ($fee->feetype == 'Nominal') {
                                            $feeamount = $fee->fee;
                                        }

                                        if ($feeamount > 0 && ($nominal - $feeamount) < 0) {
                                            throw new Exception('Nominal Topup terlalu kecil');
                                        }

                                        $insert['fee'] = $feeamount;
                                    }

                                    $ipaymu = new iPaymu($detail->apikey, $detail->virtualaccount, ENVIRONMENT == 'production');
                                    $ipaymu->setURL([
                                        'ureturn' => base_url('deposit/topup'),
                                        'unotify' => base_url('callback/ipaymu')
                                    ]);

                                    $ipaymu->setBuyer([
                                        'name' => getCurrentUser()->name,
                                        'phone' => getCurrentUser()->phonenumber ?? '*************',
                                        'email' => getCurrentUser()->email
                                    ]);

                                    $ipaymu->setReferenceId($insert['code']);
                                    $ipaymu->setPaymentChannel($payment);
                                    $ipaymu->setPaymentMethod(getIpaymuMethod()[$payment]);

                                    $carts = [];
                                    $carts = $ipaymu->add($insert['code'], 'Deposit Saldo Akun', $nominal, 1, "Deposit Saldo Akun senilai Rp " . IDR($nominal), 0, 0, 0, 0);

                                    $ipaymu->addCart($carts);

                                    $request = $ipaymu->directPayment();

                                    if (isset($request['Status']) && $request['Status'] == 200) {
                                        $paymentNo = $request['Data']['PaymentNo'] ?? '- Unknown -';
                                        $paymentName = $request['Data']['PaymentName'] ?? '- Unknown -';
                                        $paymentChannel = $request['Data']['Channel'] ?? '- Unknown -';

                                        $insert['payment'] = getIpaymuPayments()[$payment];
                                        $insert['nominal'] = $nominal;
                                        $insert['gatewayvendor'] = $paymentgateway->vendor;
                                        $insert['servercode'] = $request['Data']['TransactionId'];
                                        $insert['jsonresponse'] = json_encode($request);

                                        if ($payment != 'qris') {
                                            $insert['note'] = "Silahkan transfer sebesar Rp " . IDR($nominal) . ",- Ke Rekening: $paymentChannel, no. $paymentNo a.n $paymentName. Batas waktu transfer 24 jam";
                                        } else {
                                            $insert['note'] = "Silahkan transfer sebesar Rp " . IDR($nominal) . ",- Ke Rekening: $paymentChannel a.n $paymentName. Batas waktu transfer 24 jam";
                                            $insert['isqr'] = 1;
                                        }
                                    } else {
                                        if ($request != null) {
                                            log_message_user('error', '[IPAYMU TOPUP] Response: ' . json_encode($request), $this->merchant->id);
                                        }

                                        throw new Exception($request['Message'] ?? 'Server sedang sibuk, Silahkan coba lagi nanti');
                                    }
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            } else {
                                throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                            }
                        } else if ($paymentgateway != null && $paymentgateway->vendor == 'Duitku' && $paymentgateway->addons != null) {
                            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                            $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                            if (isset($addons->channel_payments) && isset($addons->channel_payments_name) && isset($detail->apikey) && isset($detail->merchantcode)) {
                                if (in_array($payment, $addons->channel_payments)) {
                                    $fee = $this->feepaymentgateway->row(array(
                                        'paymentgatewayid' => $paymentgateway->id,
                                        'paymentname' => $payment,
                                        "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                    ));

                                    if ($fee != null) {
                                        $feeamount = 0;

                                        if ($fee->feetype == 'Persentase') {
                                            $feeamount = $nominal * $fee->fee / 100;
                                        } else if ($fee->feetype == 'Nominal') {
                                            $feeamount = $fee->fee;
                                        }

                                        if ($feeamount > 0 && ($nominal - $feeamount) < 0) {
                                            throw new Exception('Nominal Topup terlalu kecil');
                                        }

                                        $insert['fee'] = $feeamount;
                                    }

                                    $params = array(
                                        'paymentAmount' => $nominal,
                                        'paymentMethod' => $payment,
                                        'merchantOrderId' => $insert['code'],
                                        'productDetails' => 'Deposit Saldo Akun',
                                        'customerVaName' => $this->merchant->companyname,
                                        'email' => getCurrentUser()->email,
                                        'callbackUrl' => base_url('callback/duitku'),
                                        'returnUrl' => base_url('deposit/history?userid=' . $this->userid),
                                    );

                                    $apikey = $detail->apikey;
                                    $merchantcode = $detail->merchantcode;

                                    $duitku_config = new \Duitku\Config($apikey, $merchantcode, ENVIRONMENT == 'development', true, false);

                                    $paymentMethod = Api::getPaymentMethod($nominal, $duitku_config);
                                    $paymentMethod = json_decode($paymentMethod);

                                    $paymentName = null;
                                    foreach ($paymentMethod->paymentFee as $key => $value) {
                                        if ($value->paymentMethod == $payment) {
                                            $paymentName = $value->paymentName;
                                        }
                                    }

                                    if ($paymentName == null) {
                                        throw new Exception('Pembayaran tidak ditemukan');
                                    }

                                    $create = Api::createInvoice($params, $duitku_config);
                                    $create = json_decode($create);

                                    if (isset($create->statusCode) && $create->statusCode == '00') {
                                        $insert['payment'] = $paymentName;
                                        $insert['nominal'] = $nominal;
                                        $insert['gatewayvendor'] = $paymentgateway->vendor;
                                        $insert['checkouturl'] = $create->paymentUrl;
                                        $insert['servercode'] = $create->reference;
                                        $insert['jsonresponse'] = json_encode($create);
                                    } else {
                                        if ($create != null) {
                                            log_message_user('error', '[DUITKU TOPUP] Response: ' . json_encode($create), $this->merchant->id);
                                        }

                                        throw new Exception($create->statusMessage ?? 'Server sedang sibuk, Silahkan coba lagi nanti');
                                    }
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            } else {
                                throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                            }
                        } else if ($paymentgateway != null && $paymentgateway->vendor == 'Okeconnect' && $paymentgateway->addons != null) {
                            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                            $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                            if (isset($addons->channel_payments)) {
                                if (in_array($payment, $addons->channel_payments) && isset(getOkeconnectPayments()[$payment])) {
                                    $fee = $this->feepaymentgateway->row(array(
                                        'paymentgatewayid' => $paymentgateway->id,
                                        'paymentname' => $payment,
                                        "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                    ));

                                    if ($fee != null) {
                                        $feeamount = 0;

                                        if ($fee->feetype == 'Persentase') {
                                            $feeamount = $nominal * $fee->fee / 100;
                                        } else if ($fee->feetype == 'Nominal') {
                                            $feeamount = $fee->fee;
                                        }

                                        if ($feeamount > 0 && ($nominal - $feeamount) < 0) {
                                            throw new Exception('Nominal Topup terlalu kecil');
                                        }

                                        $insert['fee'] = $feeamount;
                                    }

                                    if ($payment == 'MANDIRI' || $payment == 'MUAMALAT' || $payment == 'BRI' || $payment == 'BNI' || $payment == 'PERMATA' || $payment == 'BSI' || $payment == 'CIMB' || $payment == 'NEO' || $payment == 'DANAMON' || $payment == 'MAYBANK') {
                                        $type = 'VA';
                                    } else if ($payment == 'DANA' || $payment == 'OVO' || $payment == 'Gopay' || $payment == 'Shopee' || $payment == 'Link Aja' || $payment == 'All E-Wallet' || $payment == 'Mobile Bangking') {
                                        $type = 'EWALLET';
                                    } else if ($payment == 'ALFAMART' || $payment == 'INDOMARET') {
                                        $type = 'RETAIL';
                                    }

                                    $okeconnect = new Okeconnect($detail->merchantcode, $detail->apikey);
                                    $request = $okeconnect->requestTransaction($nominal, $insert['code'], 'Deposit Saldo Akun', getCurrentUser()->email, $payment, $phone != null ? $phone : getCurrentUser()->phonenumber, null, base_url('deposit/history?userid=' . $this->userid), $type);

                                    if (($type == 'VA' && isset($request->status) && $request->status == TRUE) || ($type == 'RETAIL' && isset($request->success) && $request->success == TRUE && isset($request->response->status) && $request->response->status == 'success')) {
                                        $insert['payment'] = getOkeconnectPayments()[$payment];
                                        $insert['nominal'] = $nominal;
                                        $insert['gatewayvendor'] = $paymentgateway->vendor;

                                        if ($type == 'VA') {
                                            $insert['note'] = "Silahkan transfer sebesar Rp " . IDR($nominal) . ",- Ke Rekening Virual Account: " . getOkeconnectPayments()[$payment] . ", no. " . $request->code->$payment . " Batas waktu transfer 24 jam";
                                            $insert['servercode'] = $request->reff;
                                        } else if ($type == 'RETAIL') {
                                            $paymentresponse = strtolower($payment);
                                            $insert['note'] = "Silahkan datang ke kasir " . getOkeconnectPayments()[$payment] . " dan bayar Okeconnect Rp " . IDR($nominal) . ",dengan kode pembayaran " . $request->response->$paymentresponse->code . " Batas waktu transfer 24 jam";
                                            $insert['servercode'] = $request->response->$paymentresponse->code;
                                        }

                                        $insert['jsonresponse'] = json_encode($request);
                                    } else {
                                        if ($request != null) {
                                            log_message_user('error', '[Okeconnect TOPUP] Response: ' . json_encode($request), $this->merchant->id);
                                        }

                                        if (isset($request->message) && $request->message) {
                                            throw new Exception($request->message);
                                        } else {
                                            throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                                        }
                                    }
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            } else {
                                throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                            }
                        } else {
                            throw new Exception('Pembayaran tidak ditemukan');
                        }

                        if ($paymentgateway->isbonus == 1) {
                            $insert['isbonus'] = 1;

                            if ($paymentgateway->bonustype == 'Nominal') {
                                $insert['nominalbonus'] = $paymentgateway->nominalbonus;
                            } else {
                                $insert['nominalbonus'] = $nominal * (((int)$paymentgateway->nominalbonus) / 100);
                            }
                        }
                    } else {
                        if (isset($json->type)) {
                            if ($json->type == 'Notification Handler') {
                                $userbuynotificationhandler = $this->userbuynotificationhandler->select('a.*, b.packagename')
                                    ->join('msnotificationhandlerpackage b', 'b.id = a.notificationid')
                                    ->get(array(
                                        'a.userid' => $this->merchant->id,
                                        'a.id' => $json->id,
                                        'b.isactive' => 1,
                                        "(a.isdisabled IS NULL OR a.isdisabled = '0') =" => true,
                                    ));

                                if ($userbuynotificationhandler->num_rows() == 0) {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }

                                $rowuserbuynotificationhandler = $userbuynotificationhandler->row();

                                if ($rowuserbuynotificationhandler->isdisabled == 1) {
                                    throw new Exception('Pembayaran yang anda pilih tidak aktif');
                                } else if ($rowuserbuynotificationhandler->minnominal > $nominal) {
                                    throw new Exception('Minimal Topup Rp' . IDR($rowuserbuynotificationhandler->minnominal));
                                } else if ($rowuserbuynotificationhandler->maxnominal != 0 && $rowuserbuynotificationhandler->maxnominal < $nominal) {
                                    throw new Exception('Maksimal Topup Rp' . IDR($rowuserbuynotificationhandler->maxnominal));
                                }

                                if ($this->merchant->uniquenominal_start != null && $this->merchant->uniquenominal_end != null && $this->merchant->uniquenominal_start != 0 && $this->merchant->uniquenominal_end != 0) {
                                    $unique = rand($this->merchant->uniquenominal_start, $this->merchant->uniquenominal_end);
                                } else {
                                    $unique = rand(100, 999);
                                }
                                $nominal = $nominal + $unique;

                                $insert['payment'] = $rowuserbuynotificationhandler->packagename;
                                $insert['nominal'] = $nominal;
                                $insert['note'] = "Silahkan transfer sebesar Rp " . IDR($nominal) . ",- Ke Rekening: $rowuserbuynotificationhandler->packagename, no. $rowuserbuynotificationhandler->accountnumber a.n. $rowuserbuynotificationhandler->accountname. Batas waktu transfer 24 jam";
                                $insert['paymenttype'] = 'Otomatis';
                                $insert['gatewayvendor'] = 'Notification Handler Services';
                                $insert['paymentmethodid'] = $rowuserbuynotificationhandler->id;

                                if ($rowuserbuynotificationhandler->isbonus == 1) {
                                    $insert['isbonus'] = 1;

                                    if ($rowuserbuynotificationhandler->bonustype == 'Nominal') {
                                        $insert['nominalbonus'] = $rowuserbuynotificationhandler->nominalbonus;
                                    } else {
                                        $insert['nominalbonus'] = $nominal * (((int)$rowuserbuynotificationhandler->nominalbonus) / 100);
                                    }
                                }

                                if ($rowuserbuynotificationhandler->isfee == 1) {
                                    $feeamount = 0;

                                    if ($rowuserbuynotificationhandler->feetype == 'Persentase') {
                                        $feeamount = $nominal * $rowuserbuynotificationhandler->nominalfee / 100;
                                    } else if ($rowuserbuynotificationhandler->feetype == 'Nominal') {
                                        $feeamount = $rowuserbuynotificationhandler->nominalfee;
                                    }

                                    if ($feeamount > 0 && ($nominal - $feeamount) < 0) {
                                        throw new Exception('Nominal Topup tidak boleh kurang dari biaya admin');
                                    }

                                    $insert['fee'] = $feeamount;
                                }
                            } else {
                                $paymentgateway = $this->mspaymentgateway->get(array(
                                    'userid' => $this->merchant->id,
                                    'type' => 'Payment Gateway',
                                    "(isdisabled IS NULL OR isdisabled = 0) =" => true
                                ))->row();

                                if ($paymentgateway == null) {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                } else if ($paymentgateway->isdisabled == 1) {
                                    throw new Exception('Pembayaran yang anda pilih tidak aktif');
                                }

                                if ($paymentgateway->minnominal > $nominal) {
                                    throw new Exception('Minimal Topup Rp' . IDR($paymentgateway->minnominal));
                                } else if ($paymentgateway->maxnominal != 0 && $paymentgateway->maxnominal < $nominal) {
                                    throw new Exception('Maksimal Topup Rp' . IDR($paymentgateway->maxnominal));
                                }

                                if ($paymentgateway->vendor == 'PayDisini' && $paymentgateway->addons != null && isset($json->id)) {
                                    $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                                    $addons = json_decode(stringEncryption('decrypt', $paymentgateway->addons));

                                    $paydisini = new PayDisini($detail->apikey);
                                    $paymentchannel = $paydisini->paymentChannel();

                                    if (isset($paymentchannel->success) && $paymentchannel->success == true) {
                                        $paymentchannel = $paymentchannel->data;

                                        if (isset($addons->channel_payments)) {
                                            if (in_array($json->id, $addons->channel_payments) && in_array($json->id, array_column($paymentchannel, 'id'))) {
                                                if ($paymentgateway->isbonus == 1) {
                                                    $insert['isbonus'] = 1;

                                                    if ($paymentgateway->bonustype == 'Nominal') {
                                                        $insert['nominalbonus'] = $paymentgateway->nominalbonus;
                                                    } else if ($paymentgateway->bonustype == 'Persentase') {
                                                        $insert['nominalbonus'] = $nominal * (((int)$paymentgateway->nominalbonus) / 100);
                                                    }
                                                }

                                                $fee = $this->feepaymentgateway->row(array(
                                                    'paymentgatewayid' => $paymentgateway->id,
                                                    'paymentname' => $json->id,
                                                    "(vendor IS NULL OR vendor = '$paymentgateway->vendor') =" => true
                                                ));

                                                if ($fee != null) {
                                                    $feeamount = 0;

                                                    if ($fee->feetype == 'Persentase') {
                                                        $feeamount = $nominal * $fee->fee / 100;
                                                    } else if ($fee->feetype == 'Nominal') {
                                                        $feeamount = $fee->fee;
                                                    }

                                                    if ($feeamount > 0 && ($nominal - $feeamount) < 0) {
                                                        throw new Exception('Nominal Topup terlalu kecil');
                                                    }

                                                    $insert['fee'] = $feeamount;
                                                }

                                                $insert['paydisinicode'] = generateUniqueCodePayDisini();

                                                $request = $paydisini->requestTransaction($json->id, $insert['paydisinicode'], $nominal, 'Deposit Saldo Akun', $phone != null ? $phone : getCurrentUser()->phonenumber, $json->type);

                                                if (isset($request->success) && $request->success == TRUE) {
                                                    $insert['payment'] = $addons->channel_payments_name[array_search($json->id, $addons->channel_payments)];
                                                    $insert['nominal'] = $nominal;
                                                    $insert['gatewayvendor'] = $paymentgateway->vendor;

                                                    if ($json->type == 'VA') {
                                                        $insert['note'] = "Silahkan transfer sebesar Rp " . IDR($nominal) . ",- Ke Rekening: " . $addons->channel_payments_name[array_search($json->id, $addons->channel_payments)] . ", no. " . $request->data->virtual_account . " Batas waktu transfer 3 jam hingga pukul" . DateFormat($request->data->expired, 'd F Y H:i:s');
                                                    } else if ($json->type == 'Retail') {
                                                        $insert['note'] = "Bayar di " . $addons->channel_payments_name[array_search($json->id, $addons->channel_payments)] . " sebesar Rp " . IDR($nominal) . ",- dan beritahu kasir kode pembayaran: " . $request->data->payment_code . " Batas waktu transfer 3 jam hingga pukul" . DateFormat($request->data->expired, 'd F Y H:i:s');
                                                    } else {
                                                        $insert['note'] = $request->data->note;
                                                    }

                                                    if ($json->type == 'QRIS') {
                                                        $insert['isqr'] = 1;
                                                    }

                                                    $insert['servercode'] = $request->data->pay_id;
                                                    $insert['checkouturl'] = $request->data->checkout_url;
                                                    $insert['jsonresponse'] = json_encode($request);
                                                } else {
                                                    if ($request != null) {
                                                        log_message_user('error', '[PayDisini TOPUP] Response: ' . json_encode($request), $this->merchant->id);
                                                    }

                                                    if (isset($request->msg) && $request->msg) {
                                                        throw new Exception($request->msg);
                                                    } else {
                                                        throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                                                    }
                                                }
                                            } else {
                                                throw new Exception('Pembayaran tidak ditemukan');
                                            }
                                        } else {
                                            throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                                        }
                                    } else {
                                        throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
                                    }
                                } else {
                                    throw new Exception('Pembayaran tidak ditemukan');
                                }
                            }
                        } else {
                            throw new Exception('Pembayaran tidak ditemukan');
                        }
                    }
                } else {
                    $paymentgateway = $this->mspaymentgateway->get(array(
                        'id' => $payment,
                        'userid' => $this->merchant->id,
                        "(isdisabled IS NULL OR isdisabled = 0) =" => true
                    ));

                    if ($paymentgateway->num_rows() == 0) {
                        throw new Exception('Pembayaran tidak ditemukan');
                    }

                    $rowpaymentgateway = $paymentgateway->row();

                    if ($rowpaymentgateway->isdisabled == 1) {
                        throw new Exception('Pembayaran yang anda pilih tidak aktif');
                    } else if ($rowpaymentgateway->minnominal > $nominal) {
                        throw new Exception('Minimal Topup Rp' . IDR($rowpaymentgateway->minnominal));
                    } else if ($rowpaymentgateway->maxnominal != 0 && $rowpaymentgateway->maxnominal < $nominal) {
                        throw new Exception('Maksimal Topup Rp' . IDR($rowpaymentgateway->maxnominal));
                    }

                    $detail = json_decode(stringEncryption('decrypt', $rowpaymentgateway->detail));

                    $insert['payment'] = $rowpaymentgateway->type;

                    if ($this->merchant->uniquenominal_start != null && $this->merchant->uniquenominal_end != null && $this->merchant->uniquenominal_start != 0 && $this->merchant->uniquenominal_end != 0) {
                        $unique = rand($this->merchant->uniquenominal_start, $this->merchant->uniquenominal_end);

                        $insert['nominal'] = $nominal + $unique;
                    } else {
                        $insert['nominal'] = $nominal + rand(100, 999);
                    }

                    if ($rowpaymentgateway->vendor == 'BCA') {
                        $insert['note'] = "Silahkan transfer sebesar Rp " . IDR($insert['nominal']) . ",- Ke Rekening: BCA, no. $detail->accountnumber a.n. $detail->accountname. Batas waktu transfer 24 jam";
                    } else if ($rowpaymentgateway->vendor == 'GOPAY') {
                        $insert['note'] = "Silahkan transfer sebesar Rp " . IDR($insert['nominal']) . ",- Ke Rekening: GOPAY, no. $detail->phonenumber. Batas waktu transfer 24 jam";
                    } else if ($rowpaymentgateway->vendor == 'OVO') {
                        $insert['note'] = "Silahkan transfer sebesar Rp " . IDR($insert['nominal']) . ",- Ke Rekening: OVO, no. $detail->phonenumber. Batas waktu transfer 24 jam";
                    }

                    if ($rowpaymentgateway->isbonus == 1) {
                        $insert['isbonus'] = 1;

                        if ($rowpaymentgateway->bonustype == 'Nominal') {
                            $insert['nominalbonus'] = $rowpaymentgateway->nominalbonus;
                        } else {
                            $insert['nominalbonus'] = $nominal * (((int)$rowpaymentgateway->nominalbonus) / 100);
                        }
                    }

                    if ($rowpaymentgateway->feetype != null) {
                        $feeamount = 0;

                        if ($rowpaymentgateway->feetype == 'Persentase') {
                            $feeamount = $nominal * $rowpaymentgateway->nominalfee / 100;
                        } else if ($rowpaymentgateway->feetype == 'Nominal') {
                            $feeamount = $rowpaymentgateway->nominalfee;
                        }

                        if ($feeamount > 0 && ($nominal - $feeamount) < 0) {
                            throw new Exception('Nominal Topup terlalu kecil');
                        }

                        $insert['fee'] = $feeamount;
                    }
                }
            } else {
                throw new Exception('Pembayaran tidak ditemukan');
            }

            $this->deposits->insert($insert);

            $this->send_notification($this->db->insert_id(), $this->merchant->id, $currentuser->phonenumber);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Permintaan topup gagal');
            }

            $this->db->trans_commit();

            return JSONResponse(array(
                'RESULT' => 'OK',
                'MESSAGE' => 'Permintaan topup berhasil',
                'REDIRECT' => base_url('deposit/detail/' . $insert['code'] . '?userid=' . $this->userid)
            ));
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function history()
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $data = array();
        $data['title'] = $this->merchant->companyname . ' | Riwayat';
        $data['content'] = 'deposit/history';

        return viewTemplate($this->merchant->id, "master", $data);
    }

    public function datatables_history_deposit()
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            if (isLogin()) {
                $data = array();

                $datatables = $this->datatables->make('Deposits', 'QueryDatatables', 'SearchDatatables');

                $where = array(
                    'a.userid' => getCurrentIdUser(),
                    'a.merchantid' => $this->merchant->id
                );

                $currenttheme = getCurrentThemeConfiguration($this->merchant->id);

                if ($currenttheme != 'HighAdmin-TopNav' && $currenttheme != 'Dason-TopNav' && $currenttheme != 'Able') {
                    $label = "label label";
                } else if ($currenttheme == 'Dason-TopNav' || $currenttheme == 'Able') {
                    $label = "badge bg";
                } else {
                    $label = "badge badge";
                }

                if ($currenttheme != 'Able') {
                    $btn = "btn-xs mb-1";
                } else {
                    $btn = "btn-sm";
                }

                foreach ($datatables->getData($where) as $key => $value) {
                    if ($value->status == 'Pending') {
                        $status = "<span class=\"$label-warning\">Menunggu</span>";
                    } else if ($value->status == 'Success' || $value->status == 'Paid') {
                        $status = "<span class=\"$label-success\">Berhasil</span>";
                    } else {
                        $status = "<span class=\"$label-danger\">Gagal</span>";
                    }

                    $actions = "-";

                    if ($value->status == 'Pending') {
                        $actions = "";

                        if ($currenttheme != 'Able') {
                            if ($value->gatewayvendor == 'Midtrans' && $value->snaptoken != null) {
                                $actions .= "<button type=\"button\" class=\"btn btn-primary btn-sm\" onclick=\"paySnap('" . stringEncryption('encrypt', $value->id) . "', '" . $value->snaptoken . "')\">
                                <i class=\"fa fa-credit-card\"></i>
                                <span>Bayar</span>
                            </button>";
                            } elseif (($value->gatewayvendor == 'Tripay' || $value->gatewayvendor == 'Duitku' || $value->gatewayvendor == 'PayDisini') && $value->checkouturl != null) {
                                $actions .= "<a href=\"$value->checkouturl\" target=\"_blank\" class=\"btn btn-primary btn-sm\">
                                <i class=\"fa fa-credit-card\"></i>
                                <span>Bayar</span>
                            </a> ";
                            } elseif ($value->gatewayvendor == 'iPaymu' && $value->isqr == 1) {
                                $actions .= "<a href=\"" . base_url('deposit/topup/qr/' . $value->code . '?userid=' . $this->userid) . "\" target=\"_blank\" class=\"btn btn-primary btn-sm\">
                                <i class=\"fa fa-qrcode\"></i>
                                <span>Bayar</span>
                            </a>";
                            } else {
                                if ($value->image != null) {
                                    $actions .= "<a href=\"" . base_url('deposit/history/bayarscan/' . $value->image . '?userid=' . $this->userid) . "\" target=\"_blank\" class=\"btn btn-primary btn-sm mr-1\">
                                    <i class=\"fa fa-credit-card\"></i>
                                    <span>Bayar</span>
                                </a>";
                                }

                                if ($value->qrimage != null) {
                                    $actions .= "<a href=\"" . base_url('deposit/history/bayarscan/' . $value->qrimage . '?userid=' . $this->userid) . "\" target=\"_blank\" class=\"btn btn-primary btn-sm mr-1\">
                                    <i class=\"fa fa-credit-card\"></i>
                                    <span>Bayar</span>
                                </a>";
                                }
                            }

                            $actions .= "<button type=\"button\" class=\"btn btn-danger btn-sm\" onclick=\"cancelDeposit('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"fa fa-times me-1\"></i>
                            <span>Cancel</span>
                        </button>";
                        } else {
                            if ($value->gatewayvendor == 'Midtrans' && $value->snaptoken != null) {
                                $actions .= "<button type=\"button\" class=\"btn btn-outline-primary d-inline-flex mr-1\" style=\"margin-right:5px\" onclick=\"paySnap('" . stringEncryption('encrypt', $value->id) . "', '" . $value->snaptoken . "')\">
                                <i class=\"ti ti-credit-card\"></i> Bayar
                            </button>";
                            } elseif (($value->gatewayvendor == 'Tripay' || $value->gatewayvendor == 'Duitku' || $value->gatewayvendor == 'PayDisini') && $value->checkouturl != null) {
                                $actions .= "<a href=\"$value->checkouturl\" target=\"_blank\" class=\"btn btn-outline-primary d-inline-flex mr-1\" style=\"margin-right:5px\">
                                <i class=\"ti ti-credit-card\"></i> Bayar
                            </a>";
                            } elseif ($value->gatewayvendor == 'iPaymu' && $value->isqr == 1) {
                                $actions .= "<a href=\"" . base_url('deposit/topup/qr/' . $value->code . '?userid=' . $this->userid) . "\" target=\"_blank\" class=\"btn btn-outline-primary d-inline-flex mr-1\" style=\"margin-right:5px\">
                                <i class=\"ti ti-qrcode\"></i> Bayar
                            </a>";
                            } else {
                                if ($value->image != null) {
                                    $actions .= "<a href=\"" . base_url('deposit/history/bayarscan/' . $value->image . '?userid=' . $this->userid) . "\" target=\"_blank\" class=\"btn btn-outline-primary d-inline-flex mr-1\" style=\"margin-right:5px\">
                                    <i class=\"ti ti-credit-card\"></i>Bayar
                                </a>";
                                }

                                if ($value->qrimage != null) {
                                    $actions .= "<a href=\"" . base_url('deposit/history/bayarscan/' . $value->qrimage . '?userid=' . $this->userid) . "\" target=\"_blank\" class=\"btn btn-outline-primary d-inline-flex mr-1\" style=\"margin-right:5px\">
                                    <i class=\"ti ti-credit-card\"></i>Bayar
                                </a>";
                                }
                            }

                            $actions .= "<button type=\"button\" class=\"btn btn-outline-danger d-inline-flex\" onclick=\"cancelDeposit('" . stringEncryption('encrypt', $value->id) . "')\">
                            <i class=\"ti ti-x me-1\"></i> Cancel
                        </button>";
                        }
                    }

                    $detail = array();
                    $detail[] = $value->code;
                    $detail[] = $value->payment;
                    $detail[] = IDR($value->nominal);
                    $detail[] = IDR($value->nominal + $value->nominalbonus - $value->uniqueadmin - $value->fee);
                    $detail[] = $value->note ?? '-';
                    $detail[] = $status;
                    $detail[] = $actions;

                    $data[] = $detail;
                }

                return $datatables->json($data);
            } else {
                throw new Exception('Access denied');
            }
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    private function send_notification($orderid, $userid, $phonenumber)
    {
        try {
            // Kirim Firebase notification untuk deposit
            sendFirebaseNotificationDeposit($orderid, $userid);

            $apikeys_whatsapp = $this->apikeys_whatsapp->get(array(
                'userid' => $userid,
            ));

            if ($apikeys_whatsapp->num_rows() == 0) {
                return false;
            }

            $row = $apikeys_whatsapp->row();

            $wablasingateway = new WABlasinGateway(stringEncryption('decrypt', $row->apikey), stringEncryption('decrypt', $row->devicecode));

            $messagenotification = replaceParameterNotificationDeposit($orderid, $userid);

            if ($messagenotification != null && $phonenumber != null) {
                $phonenumber = changePrefixPhone($phonenumber);

                $send = $wablasingateway->sendMessage($phonenumber, $messagenotification);

                if (!isset($send->RESULT) || (isset($send->RESULT) && $send->RESULT != 'OK')) {
                    log_message_user('error', '[WHATSAPP SEND MESSAGE] Send notification failed: ' . json_encode($send), $userid);
                }

                return true;
            }

            return false;
        } catch (Exception $ex) {
            return false;
        }
    }

    public function process_cancel()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            }

            $id = getPost('id');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->deposits->select('a.*')
                ->join('msusers b', 'b.id = a.userid')
                ->get(array(
                    'a.id' => $id,
                    'a.status' => 'Pending',
                    'a.userid' => getCurrentIdUser(),
                    'a.merchantid' => $this->merchant->id
                ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            $update = array();
            $update['status'] = 'Cancel';

            $this->deposits->update(array(
                'id' => $id
            ), $update);

            $this->send_notification($id, $this->merchant->id, $row->phonenumber);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Deposit gagal dibatalkan');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Deposit berhasil dibatalkan');
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function payment_check()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            }

            $id = getPost('id');
            $snaptoken = getPost('snaptoken');

            if ($id == null) {
                throw new Exception('Data tidak ditemukan');
            }

            $id = stringEncryption('decrypt', $id);

            $get = $this->deposits->select('a.*')
                ->join('msusers b', 'b.id = a.userid')
                ->get(array(
                    'a.id' => $id,
                    'a.userid' => getCurrentIdUser(),
                    'a.status' => 'Pending',
                    'a.snaptoken' => $snaptoken
                ));

            if ($get->num_rows() == 0) {
                throw new Exception('Data tidak ditemukan');
            }

            $row = $get->row();

            $paymentgateway = $this->mspaymentgateway->get(array(
                'userid' => $this->merchant->id,
                'type' => 'Payment Gateway',
                "(isdisabled IS NULL OR isdisabled = 0) =" => true
            ))->row();

            if ($paymentgateway == null) {
                throw new Exception('Payment Gateway tidak ditemukan');
            } else if ($paymentgateway->vendor != 'Midtrans') {
                throw new Exception('Akses ditolak');
            }

            $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));

            Config::$serverKey = $detail->serverkey;
            Config::$clientKey = $detail->clientkey;
            Config::$isProduction = ENVIRONMENT == 'production' ? true : false;

            try {
                $transactionstatus = (object) Midtrans\Transaction::status($row->code);
            } catch (Exception $midex) {
                if ($midex->getMessage() != null) {
                    log_message_user('error', '[MIDTRANS CHECK PAYMENT] Response: ' . $midex->getMessage(), $this->merchant->id);
                }

                throw new Exception('Server sedang sibuk, Silahkan coba lagi nanti');
            }

            if ($transactionstatus->status_code == 200 || $transactionstatus->status_code == 201) {
                if ($transactionstatus->transaction_status == 'settlement' && $transactionstatus->fraud_status == 'accept') {
                    $signature = openssl_digest($row->code . $transactionstatus->status_code . $row->nominal . '.00' . $detail->serverkey, "sha512");

                    if ($transactionstatus->signature_key != $signature) {
                        throw new Exception('Invalid signature');
                    }

                    $grossamount = (int) $transactionstatus->gross_amount;

                    if ($grossamount == $row->nominal) {
                        $total_balance = 0;
                        if ($row->isbonus == 1) {
                            $total_balance = $row->nominal + $row->nominalbonus - ($row->fee ?? 0);
                        } else {
                            $total_balance = $row->nominal - ($row->fee ?? 0);
                        }

                        $check_history_balance = $this->historybalance->total(array(
                            'depositid' => $row->id,
                            'type' => 'IN',
                            'userid' => getCurrentIdUser()
                        ));

                        if ($check_history_balance == 0) {
                            $currentbalance = getCurrentBalance(getCurrentIdUser(), true);

                            $inserthistorybalance = array();
                            $inserthistorybalance['userid'] = getCurrentIdUser();
                            $inserthistorybalance['type'] = 'IN';
                            $inserthistorybalance['nominal'] = $total_balance;
                            $inserthistorybalance['currentbalance'] = $currentbalance;
                            $inserthistorybalance['depositid'] = $row->id;
                            $inserthistorybalance['createdby'] = getCurrentIdUser();
                            $inserthistorybalance['createddate'] = getCurrentDate();

                            $this->historybalance->insert($inserthistorybalance);

                            $update = array();
                            $update['balance'] = $currentbalance + $total_balance;

                            $this->msusers->update(array(
                                'id' => getCurrentIdUser()
                            ), $update);
                        }
                    } else {
                        throw new Exception('Nominal transfer tidak sesuai');
                    }
                }
            }

            $update = array();

            if ($transactionstatus->transaction_status == 'settlement') {
                $update['status'] = 'Success';
            } else if ($transactionstatus->transaction_status == 'pending') {
                $update['status'] = 'Pending';
            } else {
                $update['status'] = 'Failed';
            }

            $this->deposits->update(array(
                'id' => $row->id
            ), $update);

            $this->send_notification($row->id, $this->merchant->id, $row->phonenumber);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Pembayaran gagal');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', $transactionstatus->status_message);
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function report()
    {
        if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
            return show_404();
        }

        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $data = array();
        $data['title'] = $this->merchant->companyname . ' | Laporan Deposit';
        $data['content'] = 'deposit/report/index';
        $data['unpaid'] = $this->deposits->select('status, count(*) as total, sum(nominal) as nominal')
            ->get(array(
                'userid' => getCurrentIdUser(),
                'status' => 'Pending',
                'MONTH(createddate)' => getCurrentDate('m'),
                'YEAR(createddate)' => getCurrentDate('Y')
            ))->row();
        $data['paid'] = $this->deposits->select('status, count(*) as total, sum(nominal) as nominal')
            ->get(array(
                'userid' => getCurrentIdUser(),
                'status' => 'Success',
                'MONTH(createddate)' => getCurrentDate('m'),
                'YEAR(createddate)' => getCurrentDate('Y')
            ))->row();
        $data['failed'] = $this->deposits->select('status, count(*) as total, sum(nominal) as nominal')
            ->get(array(
                'userid' => getCurrentIdUser(),
                "(status = 'Failed' OR status = 'Expired' OR status = 'Cancel') =" => true,
                'MONTH(createddate)' => getCurrentDate('m'),
                'YEAR(createddate)' => getCurrentDate('Y')
            ))->row();
        $data['startperiode'] = getCurrentDate('1 F Y');
        $data['endperiode'] = getCurrentDate('t F Y');

        return viewTemplate($this->merchant->id, "master", $data);
    }

    public function filter_report()
    {
        try {
            if (in_array(getCurrentThemeConfiguration($this->merchant->id), array('Fin-App', 'Sobat-Serverppob'))) {
                return show_404();
            }

            if (!isLogin()) {
                throw new Exception('Sesi anda telah habis, Silahkan login kembali');
            }

            $startdate = getPost('startdate');
            $enddate = getPost('enddate');

            if (empty($startdate) || empty($enddate)) {
                throw new Exception('Tanggal tidak boleh kosong');
            } else if ($enddate < $startdate) {
                throw new Exception('Tanggal akhir tidak boleh kurang dari tanggal awal');
            }

            $data = array();
            $data['unpaid'] = $this->deposits->select('status, count(*) as total, sum(nominal) as nominal')
                ->get(array(
                    'userid' => getCurrentIdUser(),
                    'status' => 'Pending',
                    'DATE(createddate) >=' => $startdate,
                    'DATE(createddate) <=' => $enddate
                ))->row();
            $data['paid'] = $this->deposits->select('status, count(*) as total, sum(nominal) as nominal')
                ->get(array(
                    'userid' => getCurrentIdUser(),
                    'status' => 'Success',
                    'DATE(createddate) >=' => $startdate,
                    'DATE(createddate) <=' => $enddate
                ))->row();
            $data['failed'] = $this->deposits->select('status, count(*) as total, sum(nominal) as nominal')
                ->get(array(
                    'userid' => getCurrentIdUser(),
                    "(status = 'Failed' OR status = 'Expired' OR status = 'Cancel') =" => true,
                    'DATE(createddate) >=' => $startdate,
                    'DATE(createddate) <=' => $enddate
                ))->row();
            $data['startperiode'] = DateFormat($startdate, 'd F Y');
            $data['endperiode'] = DateFormat($enddate, 'd F Y');

            return JSONResponse(array(
                'RESULT' => 'OK',
                'CONTENT' => viewTemplate($this->merchant->id, 'deposit/report/content', $data, true)
            ));
        } catch (Exception $ex) {
            return JSONResponseDefault('FAILED', $ex->getMessage());
        }
    }

    public function qr($code)
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $get = $this->deposits->get(array(
            'userid' => getCurrentIdUser(),
            'code' => $code,
            'status' => 'Pending',
            'isqr' => 1
        ));

        if ($get->num_rows() == 0) {
            return redirect(base_url('deposit/history?userid=' . $this->userid));
        }

        $row = $get->row();

        $jsonresponse = json_decode($row->jsonresponse);

        if ($jsonresponse->Status != true) {
            return redirect(base_url('deposit/history?userid=' . $this->userid));
        }

        $data = array();
        $data['title'] = $this->merchant->companyname . ' | QR Code Deposit ' . $row->code;
        $data['content'] = 'deposit/qr';
        $data['deposit'] = $row;
        $data['paymentno'] = $jsonresponse->Data->PaymentNo;

        return viewTemplate($this->merchant->id, "master", $data);
    }

    public function bayar_scan($image)
    {
        if ($image != null && file_exists('../../uploads/' . $image)) {
            $upload = '../../uploads/' . $image;
        } else {
            return show_404();
        }

        header('Content-Type: ' . mime_content_type($upload));
        header('Content-Length: ' . filesize($upload));

        readfile($upload);
    }

    public function detail_topup($code)
    {
        if (getCurrentThemeConfiguration($this->merchant->id) != 'Fin-App') {
            return show_404();
        }

        if (!isLogin()) {
            return redirect(base_url('auth/login?userid=' . $this->userid));
        }

        $where = array(
            'a.userid' => getCurrentIdUser(),
            'a.merchantid' => $this->merchant->id,
            'a.code' => $code
        );

        $data = array();
        $data['title'] = $this->merchant->companyname . ' | Detail Topup';
        $data['content'] = 'deposit/detailtopup';
        $data['row'] = $this->deposits->QueryDatatables()->selfGet($where)->row();
        $data['back'] = base_url('deposit/topup?userid=' . $this->userid);

        return viewTemplate($this->merchant->id, "master", $data);
    }
}
