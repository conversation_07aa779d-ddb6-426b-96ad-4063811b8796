<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsPlatformSosmed $msplatformsosmed
 * @property CI_Output $output
 * @property MsDetailPlatform $msdetailplatform
 * @property MsProduct $msproduct
 * @property MobileSession $mobilesession
 * @property DisabledCategory $disabledcategory
 * @property MsRole $msrole
 * @property MsRoleDiscount $msrolediscount
 * @property MsRoleDiscountAdv $msrolediscountadv
 * @property TrOrder $trorder
 * @property MsUsers $msusers
 * @property ApiKeys $apikeys
 * @property HistoryBalance $historybalance
 * @property ApiKeys_WhatsApp $apikeys_whatsapp
 */
class SMM extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsPlatformSosmed', 'msplatformsosmed');
        $this->load->model('MsDetailPlatform', 'msdetailplatform');
        $this->load->model('MsProduct', 'msproduct');
        $this->load->model('MobileSession', 'mobilesession');
        $this->load->model('DisabledCategory', 'disabledcategory');
        $this->load->model('MsRole', 'msrole');
        $this->load->model('MsRoleDiscount', 'msrolediscount');
        $this->load->model('MsRoleDiscountAdv', 'msrolediscountadv');
        $this->load->model('TrOrder', 'trorder');
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('ApiKeys', 'apikeys');
        $this->load->model('HistoryBalance', 'historybalance');
        $this->load->model('ApiKeys_WhatsApp', 'apikeys_whatsapp');
        $this->load->model('MsVendorDetail', 'msvendordetail');
    }

    private function send_notification($orderid, $userid, $phonenumber)
    {
        // Kirim Firebase notification untuk order
        sendFirebaseNotificationOrder($orderid, $userid);

        $apikeys_whatsapp = $this->apikeys_whatsapp->get(array(
            'userid' => $userid,
        ));

        if ($apikeys_whatsapp->num_rows() == 0) {
            return false;
        }

        $row = $apikeys_whatsapp->row();

        $wablasingateway = new WABlasinGateway(stringEncryption('decrypt', $row->apikey), stringEncryption('decrypt', $row->devicecode));

        $messagenotification = replaceParameterNotification($orderid, $userid);
        if ($messagenotification != null && $phonenumber != null) {
            $phonenumber = changePrefixPhone($phonenumber);

            $send = $wablasingateway->sendMessage($phonenumber, $messagenotification);

            if (!isset($send->RESULT) || (isset($send->RESULT) && $send->RESULT != 'OK')) {
                log_message_user('error', '[WHATSAPP SEND MESSAGE] Send notification failed: ' . json_encode($send), $userid);
            }

            return true;
        }

        return false;
    }

    public function category()
    {
        $category = getPost('category');

        if ($category == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Kategori tidak boleh kosong',
            ));
        }

        $get = $this->msplatformsosmed->get(array(
            'name' => $category,
            'userid' => $this->merchant->id
        ))->row();

        if ($get == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Kategori tidak ditemukan',
            ));
        }

        $detailplatform = $this->msdetailplatform->order_by('category', 'ASC')->result(array(
            'platformid' => $get->id
        ));

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => true,
            'message' => 'Data berhasil diambil',
            'data' => $detailplatform
        ));
    }

    public function product()
    {
        $token = getPost('token');
        $category = getPost('category');
        $detailcategory = getPost('detailcategory');

        if ($category == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Kategori tidak boleh kosong',
            ));
        }

        if ($detailcategory == null) {
            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => 'Detail kategori tidak boleh kosong',
            ));
        }

        $validate = $this->validateToken($token);

        if ($validate['status'] == false) {
            $this->output->set_status_header(401);
            $this->output->set_content_type('application/json');

            return JSONResponse($validate);
        }

        $currentuser = $validate['data'];

        if ($currentuser->roleid == null) {
            $getrole = $this->msrole->get(array(
                'createdby' => $this->merchant->id,
                'isdefault' => '1'
            ))->row();
        } else {
            $getrole = $this->msrole->get(array(
                'createdby' => $this->merchant->id,
                'id' => $currentuser->roleid
            ))->row();
        }

        if ($getrole != null) {
            if ($getrole->discounttype == 'Simple') {
                $getrolediscount = $this->msrolediscount->get(array(
                    'userid' => $this->merchant->id,
                    'roleid' => $getrole->id,
                ))->row();

                $discount = $getrolediscount->trxdiscount ?? 0;
            } else {
                $discount = $this->msrolediscountadv->result(array(
                    'createdby' => $this->merchant->id,
                    'roleid' => $getrole->id,
                    'servicetype' => 'SMM'
                ));
            }
        } else {
            $getrole = new stdClass();
            $getrole->discounttype = "Simple";
            $discount = 0;
        }

        $where = array(
            'a.userid' => $this->merchant->id,
            'a.category' => $detailcategory,
            'a.status' => 1
        );

        if ($this->merchant->multivendor != 1) {
            $vendor = getCurrentVendor('SMM', $this->merchant->id);
            $where['a.vendor'] = $vendor;
            $where['a.vendorid'] = null;
        } else {
            $where['a.vendorid !='] = null;
            $where['a.vendorenabled'] = 1;
        }

        $product = $this->msproduct->select('a.id, a.productname, a.price, a.description, c.asseturl, a.minorder, a.maxorder')
            ->join('categoryimage b', 'b.categoryname = a.category AND b.userid = a.userid', 'LEFT')
            ->join('msicons c', 'c.id = b.assetid', 'LEFT')
            ->order_by('a.price')
            ->result($where);

        foreach ($product as $key => $value) {
            if ($getrole->discounttype == 'Simple') {
                $product[$key]->price = $value->price - $discount;
            } else {
                $found = false;
                foreach ($discount as $k => $v) {
                    if ($found) continue;

                    if ($v->startrange <= $value->price && $v->endrange >= $value->price) {
                        if ($v->discounttype == 'Persentase') {
                            $product[$key]->price = round($value->price - ($value->price * ($v->nominal / 100)));
                            $found = true;
                        } else {
                            $product[$key]->price = round($value->price - $v->nominal);
                            $found = true;
                        }
                    }
                }

                if ($found == false) {
                    $product[$key]->price = round($value->price);
                }
            }
        }

        $this->output->set_status_header(200);
        $this->output->set_content_type('application/json');

        return JSONResponse(array(
            'status' => true,
            'message' => 'Data berhasil diambil',
            'data' => $product
        ));
    }

    private function validateToken($token)
    {
        if ($token == null) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $token = stringEncryption('decrypt', $token);

        if ($token == null) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $token = json_decode($token);

        if (json_last_error() != JSON_ERROR_NONE) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $validate = $this->mobilesession->total(array(
            'token' => getPost('token')
        ));

        if ($validate == 0) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $user = $this->msusers->get(array(
            'id' => $token->id,
            'merchantid' => $this->merchant->id,
        ));

        if ($user->num_rows() == 0) {
            return array(
                'status' => false,
                'message' => 'Autorisasi ditolak'
            );
        }

        $row = $user->row();

        return array(
            'status' => true,
            'message' => 'Data berhasil diambil',
            'data' => $row
        );
    }

    public function order()
    {
        try {
            $this->db->trans_begin();

            $token = getPost('token');
            $product = getPost('product');
            $target = getPost('target');
            $qty = getPost('qty');
            $pin = getPost('pin');

            if ($product == null) {
                throw new Exception('Produk tidak boleh kosong');
            }

            if ($target == null) {
                throw new Exception('Target tidak boleh kosong');
            }

            if ($qty == null) {
                throw new Exception('Jumlah tidak boleh kosong');
            }

            if ($pin == null) {
                throw new Exception('PIN tidak boleh kosong');
            }

            $validate = $this->validateToken($token);

            if ($validate['status'] == false) {
                throw new Exception('Autorisasi ditolak');
            }

            $currentuser = getCurrentUser($validate['data']->id, true);

            if (stringEncryption('encrypt', $pin) != $currentuser->pin) {
                throw new Exception('PIN Transaksi yang anda masukkan salah');
            }

            $disabled = $this->disabledcategory->result(array(
                'userid' => $this->merchant->id,
                'category_apikey' => 'SMM'
            ));

            $disabled_category = array();
            foreach ($disabled as $key => $value) {
                $disabled_category[] = $value->categoryname;
            }

            $where = array(
                'id' => $product,
                'userid' => $this->merchant->id,
                'subcategory_apikey' => 'SMM',
                'category !=' => null
            );

            if ($this->merchant->multivendor != 1) {
                $vendor = getCurrentVendor('SMM', $this->merchant->id);
                $where['vendor'] = $vendor;
                $where['vendorid'] = null;
            } else {
                $where['vendorid !='] = null;
                $where['vendorenabled'] = 1;
            }

            $getproduct = $this->msproduct->where_not_in('category', $disabled_category)
                ->get($where);

            if ($getproduct->num_rows() == 0) {
                throw new Exception('Produk tidak ditemukan');
            }

            $productRow = $getproduct->row();

            if ($productRow->status != 1) {
                throw new Exception('Produk tidak tersedia');
            }

            if ($productRow->minorder > $qty) {
                throw new Exception('Minimal order ' . $productRow->minorder);
            }

            if ($productRow->maxorder < $qty) {
                throw new Exception('Maksimal order ' . $productRow->maxorder);
            }

            if ($currentuser->roleid == null) {
                $getrole = $this->msrole->get(array(
                    'createdby' => $this->merchant->id,
                    'isdefault' => '1'
                ))->row();
            } else {
                $getrole = $this->msrole->get(array(
                    'createdby' => $this->merchant->id,
                    'id' => $currentuser->roleid
                ))->row();
            }

            if ($getrole != null) {
                if ($getrole->discounttype == 'Simple') {
                    $getrolediscount = $this->msrolediscount->get(array(
                        'userid' => $this->merchant->id,
                        'roleid' => $getrole->id
                    ))->row();

                    $discount = $getrolediscount->trxdiscount ?? 0;
                } else {
                    $discount = $this->msrolediscountadv->get(array(
                        'createdby' => $this->merchant->id,
                        'roleid' => $getrole->id,
                        'servicetype' => 'SMM'
                    ))->result();
                }
            } else {
                $getrole = new stdClass();
                $getrole->discounttype = 'Simple';
                $discount = 0;
            }

            $fixproductprice = 0;

            if ($getrole->discounttype == 'Simple') {
                $fixproductprice = $productRow->price - $discount;
            } else {
                $found = false;

                foreach ($discount as $val) {
                    if ($found) continue;

                    if ($val->startrange <= $productRow->price && $val->endrange >= $productRow->price) {
                        if ($val->discounttype == 'Persentase') {
                            $fixproductprice = $productRow->price - ($productRow->price * $val->nominal / 100);

                            $found = true;
                        } else {
                            $fixproductprice = $productRow->price - $val->nominal;

                            $found = true;
                        }
                    }
                }

                if ($found == false) {
                    $fixproductprice = $productRow->price;
                }
            }

            $totalharga = ($fixproductprice / 1000) * $qty;
            $totalhargaorigin = ($productRow->price / 1000) * $qty;

            $potonganprofit = $totalhargaorigin - $totalharga;

            $totalhargabyvendor = ($productRow->vendorprice / 1000) * $qty;
            $profit = (($productRow->profit / 1000) * $qty) - $potonganprofit;

            if ($currentuser->balance < $totalharga) {
                throw new Exception('Saldo anda tidak mencukupi');
            }

            $getpending = $this->trorder->total(array(
                'userid' => $currentuser->id,
                'serviceid' => $productRow->id,
                'target' => $target,
                'status' => 'pending'
            ));

            if ($getpending > 0) {
                throw new Exception('Anda memiliki transaksi yang masih dalam proses');
            }

            $queuetransaction = false;

            if ($this->merchant->multivendor != 1) {
                $apikeys = getCurrentAPIKeys('SMM', $this->merchant->id);
                if ($apikeys->balance < $totalhargabyvendor) {
                    $queuetransaction = true;
                }
            } else {
                if (getCurrentBalanceVendor($this->merchant->id, $productRow->vendorid) < $totalhargabyvendor) {
                    $queuetransaction = true;
                }
            }

            $insert = array();
            $insert['userid'] = $currentuser->id;
            $insert['clientcode'] = generateTransactionNumber('SMM');
            $insert['serviceid'] = $productRow->id;
            $insert['target'] = $target;
            $insert['qty'] = $qty;
            $insert['price'] = $totalharga;
            $insert['profit'] = $profit;
            $insert['currentsaldo'] = $currentuser->balance;
            $insert['status'] = 'pending';
            $insert['type'] = 'SMM';
            $insert['queuetransaction'] = $queuetransaction ? 1 : 0;
            $insert['category_apikey'] = $productRow->category_apikey;
            $insert['subcategory_apikey'] = $productRow->subcategory_apikey;
            $insert['orderplatform'] = 'apps';
            $insert['productcode'] = $productRow->code;
            $insert['vendor'] = $productRow->vendor;
            $insert['productname_order'] = $productRow->productname;
            $insert['merchantid_order'] = $this->merchant->id;
            $insert['status_payment'] = 'sukses';
            $insert['vendorid'] = $productRow->vendorid;

            $this->trorder->insert($insert);
            $insert_id = $this->db->insert_id();

            $inserthistorybalance = array();
            $inserthistorybalance['userid'] = $currentuser->id;
            $inserthistorybalance['type'] = 'OUT';
            $inserthistorybalance['nominal'] = $totalharga;
            $inserthistorybalance['currentbalance'] = $currentuser->balance;
            $inserthistorybalance['orderid'] = $insert_id;
            $inserthistorybalance['createdby'] = $currentuser->id;
            $inserthistorybalance['createddate'] = getCurrentDate();

            $this->historybalance->insert($inserthistorybalance);

            $update = array();
            $update['balance'] = $currentuser->balance - $totalharga;

            $this->msusers->update(array(
                'id' => $currentuser->id
            ), $update);

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Gagal melakukan transaksi');
            }

            pullTransaction($this->merchant->id);

            if ($queuetransaction == false && ENVIRONMENT == 'production') {
                if ($this->merchant->multivendor != 1) {
                    if ($vendor == 'BuzzerPanel') {
                        $buzzerpanel = new BuzzerPanel(stringEncryption('decrypt', $apikeys->apikey), stringEncryption('decrypt', $apikeys->secretkey));
                        $order = $buzzerpanel->order($productRow->code, $target, $qty);

                        if ($order->status) {
                            $update = array();
                            $update['servercode'] = $order->data->id;
                            $update['jsonresponse'] = json_encode($order);

                            $this->trorder->update(array(
                                'id' => $insert_id
                            ), $update);

                            $this->db->trans_commit();

                            $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                            $this->output->set_status_header(200);
                            $this->output->set_content_type('application/json');

                            return JSONResponse(array(
                                'status' => true,
                                'message' => 'Transaksi berhasil',
                                'transactionid' => $insert_id,
                            ));
                        } else {
                            if ($order != null) {
                                log_message_user('error', '[BUZZERPANEL ORDER] Response: ' . json_encode($order), $this->merchant->id);
                            }

                            throw new Exception('Gagal melakukan transaksi');
                        }
                    } else if ($vendor == 'MedanPedia') {
                        $medanpedia = new MedanPedia(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));
                        $order = $medanpedia->order($productRow->code, $target, $qty, '');

                        if ($order->status) {
                            $update = array();
                            $update['servercode'] = $order->data->id;
                            $update['jsonresponse'] = json_encode($order);

                            $this->trorder->update(array(
                                'id' => $insert_id
                            ), $update);

                            $this->db->trans_commit();

                            $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                            $this->output->set_status_header(200);
                            $this->output->set_content_type('application/json');

                            return JSONResponse(array(
                                'status' => true,
                                'message' => 'Transaksi berhasil',
                                'transactionid' => $insert_id,
                            ));
                        } else {
                            if ($order != null) {
                                log_message_user('error', '[MEDANPEDIA ORDER] Response: ' . json_encode($order), $this->merchant->id);
                            }

                            throw new Exception('Gagal melakukan transaksi');
                        }
                    } else if ($vendor == 'IrvanKede') {
                        if ($productRow->iscustom == null) {
                            $additional = '';
                        }

                        $irvankede = new IrvanKede(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));
                        $order = $irvankede->order(array(
                            'service' => $productRow->code,
                            'target' => $target,
                            'quantity' => $qty,
                            'custom_comments' => $additional,
                            'custom_link' => $additional
                        ));

                        if ($order->status) {
                            $update = array();
                            $update['servercode'] = $order->data->id;
                            $update['jsonresponse'] = json_encode($order);

                            $this->trorder->update(array(
                                'id' => $insert_id
                            ), $update);

                            $this->db->trans_commit();

                            $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                            $this->output->set_status_header(200);
                            $this->output->set_content_type('application/json');

                            return JSONResponse(array(
                                'status' => true,
                                'message' => 'Transaksi berhasil',
                                'transactionid' => $insert_id,
                            ));
                        } else {
                            if ($order != null) {
                                log_message_user('error', '[IRVANKEDE ORDER] Response: ' . json_encode($order), $this->merchant->id);
                            }

                            throw new Exception('Gagal melakukan transaksi');
                        }
                    } else if ($vendor == 'DailyPanel') {
                        $dailypanel = new DailyPanel(stringEncryption('decrypt', $apikeys->apikey), stringEncryption('decrypt', $apikeys->secretkey));
                        $order = $dailypanel->order($productRow->code, $target, $qty, '');

                        if (isset($order->success) && $order->success) {
                            $update = array();
                            $update['servercode'] = $order->msg->order_id;
                            $update['jsonresponse'] = json_encode($order);

                            $this->trorder->update(array(
                                'id' => $insert_id
                            ), $update);

                            $this->db->trans_commit();

                            $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                            $this->output->set_status_header(200);
                            $this->output->set_content_type('application/json');

                            return JSONResponse(array(
                                'status' => true,
                                'message' => 'Transaksi berhasil',
                                'transactionid' => $insert_id,
                            ));
                        } else {
                            if ($order != null) {
                                log_message_user('error', '[DAILYPANEL ORDER] Response: ' . json_encode($order), $this->merchant->id);
                            }

                            throw new Exception('Gagal melakukan transaksi');
                        }
                    } else if ($vendor == 'WStore') {
                        $wstore = new WStore(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey), stringEncryption('decrypt', $apikeys->secretkey));
                        $order = $wstore->order($productRow->code, $target, $qty);

                        if (isset($order->response) && $order->response) {
                            $update = array();
                            $update['servercode'] = $order->data->id;
                            $update['jsonresponse'] = json_encode($order);

                            $this->trorder->update(array(
                                'id' => $insert_id
                            ), $update);

                            $this->db->trans_commit();

                            $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                            $this->output->set_status_header(200);
                            $this->output->set_content_type('application/json');

                            return JSONResponse(array(
                                'status' => true,
                                'message' => 'Transaksi berhasil',
                                'transactionid' => $insert_id,
                            ));
                        } else {
                            if ($order != null) {
                                log_message_user('error', '[WSTORE ORDER] Response: ' . json_encode($order), $this->merchant->id);
                            }

                            throw new Exception('Gagal melakukan transaksi');
                        }
                    } else if ($vendor == 'UNDRCTRL') {
                        $undrctrl = new UNDRCTRL(stringEncryption('decrypt', $apikeys->apikey));
                        $order = $undrctrl->order($productRow->code, $target, $qty);

                        if (isset($order->order)) {
                            $update = array();
                            $update['servercode'] = $order->order;
                            $update['jsonresponse'] = json_encode($order);

                            $this->trorder->update(array(
                                'id' => $insert_id
                            ), $update);

                            $this->db->trans_commit();

                            $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                            $this->output->set_status_header(200);
                            $this->output->set_content_type('application/json');

                            return JSONResponse(array(
                                'status' => true,
                                'message' => 'Transaksi berhasil',
                                'transactionid' => $insert_id,
                            ));
                        } else {
                            if ($order != null) {
                                log_message_user('error', '[UNDRCTRL ORDER] Response: ' . json_encode($order), $this->merchant->id);
                            }

                            throw new Exception('Gagal melakukan transaksi');
                        }
                    } else if ($vendor == 'SosmedOnline') {
                        $sosmedonline = new SosmedOnline(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));
                        $order = $sosmedonline->order($productRow->code, $target, $qty, '');

                        if (isset($order->status) && $order->status) {
                            $update = array();
                            $update['servercode'] = $order->data->id;
                            $update['jsonresponse'] = json_encode($order);

                            $this->trorder->update(array(
                                'id' => $insert_id
                            ), $update);

                            $this->db->trans_commit();

                            $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                            $this->output->set_status_header(200);
                            $this->output->set_content_type('application/json');

                            return JSONResponse(array(
                                'status' => true,
                                'message' => 'Transaksi berhasil',
                                'transactionid' => $insert_id,
                            ));
                        } else {
                            if ($order != null) {
                                log_message_user('error', '[SOSMEDONLINE ORDER] Response: ' . json_encode($order), $this->merchant->id);
                            }

                            throw new Exception('Gagal melakukan transaksi');
                        }
                    } else if ($vendor == 'SosmedOnlineVIP') {
                        $sosmedonlinevip = new SosmedOnlineVIP(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey));
                        $order = $sosmedonlinevip->order($productRow->code, $target, $qty, '');

                        if (isset($order->status) && $order->status) {
                            $update = array();
                            $update['servercode'] = $order->data->id;
                            $update['jsonresponse'] = json_encode($order);

                            $this->trorder->update(array(
                                'id' => $insert_id
                            ), $update);

                            $this->db->trans_commit();

                            $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                            $this->output->set_status_header(200);
                            $this->output->set_content_type('application/json');

                            return JSONResponse(array(
                                'status' => true,
                                'message' => 'Transaksi berhasil',
                                'transactionid' => $insert_id,
                            ));
                        } else {
                            if ($order != null) {
                                log_message_user('error', '[SOSMEDONLINEVIP ORDER] Response: ' . json_encode($order), $this->merchant->id);
                            }

                            throw new Exception('Gagal melakukan transaksi');
                        }
                    } else if ($vendor == 'DjuraganSosmed') {
                        $djuragansosmed = new DjuraganSosmed(stringEncryption('decrypt', $apikeys->apikey));
                        $order = $djuragansosmed->order($productRow->code, $target, $qty);

                        if (isset($order->order)) {
                            $update = array();
                            $update['servercode'] = $order->order;
                            $update['jsonresponse'] = json_encode($order);

                            $this->trorder->update(array(
                                'id' => $insert_id
                            ), $update);

                            $this->db->trans_commit();

                            $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                            $this->output->set_status_header(200);
                            $this->output->set_content_type('application/json');

                            return JSONResponse(array(
                                'status' => true,
                                'message' => 'Transaksi berhasil',
                                'transactionid' => $insert_id,
                            ));
                        } else {
                            if ($order != null) {
                                log_message_user('error', '[DJURAGANSOSMED ORDER] Response: ' . json_encode($order), $this->merchant->id);
                            }

                            throw new Exception('Gagal melakukan transaksi');
                        }
                    } else if ($vendor == 'SMMRaja') {
                        $smmraja = new SMMRaja(stringEncryption('decrypt', $apikeys->apikey));
                        $order = $smmraja->order($productRow->code, $target, $qty);

                        if (isset($order->order)) {
                            $update = array();
                            $update['servercode'] = $order->order;
                            $update['jsonresponse'] = json_encode($order);

                            $this->trorder->update(array(
                                'id' => $insert_id
                            ), $update);

                            $this->db->trans_commit();

                            $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                            $this->output->set_status_header(200);
                            $this->output->set_content_type('application/json');

                            return JSONResponse(array(
                                'status' => true,
                                'message' => 'Transaksi berhasil',
                                'transactionid' => $insert_id,
                            ));
                        } else {
                            if ($order != null) {
                                log_message_user('error', '[SMMRAJA ORDER] Response: ' . json_encode($order), $this->merchant->id);
                            }

                            throw new Exception('Gagal melakukan transaksi');
                        }
                    } else if ($vendor == 'SMMIllusion') {
                        $smmillusion = new SMMIllusion(stringEncryption('decrypt', $apikeys->apikey));
                        $order = $smmillusion->order($productRow->code, $target, $qty);

                        if (isset($order->order)) {
                            $update = array();
                            $update['servercode'] = $order->order;
                            $update['jsonresponse'] = json_encode($order);

                            $this->trorder->update(array(
                                'id' => $insert_id
                            ), $update);

                            $this->db->trans_commit();

                            $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                            $this->output->set_status_header(200);
                            $this->output->set_content_type('application/json');

                            return JSONResponse(array(
                                'status' => true,
                                'message' => 'Transaksi berhasil',
                                'transactionid' => $insert_id,
                            ));
                        } else {
                            if ($order != null) {
                                log_message_user('error', '[SMMILLUSION ORDER] Response: ' . json_encode($order), $this->merchant->id);
                            }

                            throw new Exception('Gagal melakukan transaksi');
                        }
                    } else if ($vendor == 'V1Pedia') {
                        $v1pedia = new V1Pedia(stringEncryption('decrypt', $apikeys->usercode), stringEncryption('decrypt', $apikeys->apikey), stringEncryption('decrypt', $apikeys->secretkey));
                        $order = $v1pedia->order($productRow->code, $target, $qty);

                        if (isset($order->response) && $order->response) {
                            $update = array();
                            $update['servercode'] = $order->data->id;
                            $update['jsonresponse'] = json_encode($order);

                            $this->trorder->update(array(
                                'id' => $insert_id
                            ), $update);

                            $this->db->trans_commit();

                            $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                            $this->output->set_status_header(200);
                            $this->output->set_content_type('application/json');

                            return JSONResponse(array(
                                'status' => true,
                                'message' => 'Transaksi berhasil',
                                'transactionid' => $insert_id,
                            ));
                        } else {
                            if ($order != null) {
                                log_message_user('error', '[V1PEDIA ORDER] Response: ' . json_encode($order), $this->merchant->id);
                            }

                            throw new Exception('Gagal melakukan transaksi');
                        }
                    }
                } else {
                    $order = $this->msvendordetail->select('a.*, b.default_config')
                        ->join('msvendor b', 'b.id = a.vendorid')
                        ->get(array(
                            'a.vendorid' => $productRow->vendorid,
                            'a.apitype' => 'Order'
                        ));

                    if ($order->num_rows() == 0) {
                        throw new Exception('Vendor tidak ditemukan');
                    }

                    $vendor_order = $order->row();

                    if ($vendor_order->default_config == null) {
                        throw new Exception('Konfigurasi vendor tidak ditemukan');
                    }

                    $response_indicator = json_decode($vendor_order->response_indicator);
                    $response_setting = json_decode($vendor_order->response_setting);

                    $dynamicvendor = new DynamicVendor($productRow->vendorid, json_decode($vendor_order->default_config, true));
                    $order = $dynamicvendor->order($insert_id);

                    if (
                        ($response_indicator->index == null && isset($order[$response_indicator->key]) && (
                            $response_indicator->datatype == 'string' && $order[$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'number' && $order[$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'boolean' && $order[$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'exists' && isset($order[$response_indicator->key])
                        ))
                        || ($response_indicator->index != null && isset($order[$response_indicator->index][$response_indicator->key]) && (
                            $response_indicator->datatype == 'string' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'number' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'boolean' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'exists' && isset($order[$response_indicator->index][$response_indicator->key])
                        ))
                    ) {
                        $var_referenceid = $response_setting->referenceid ?? null;
                        $var_price = $response_setting->price ?? null;
                        $var_status = $response_setting->status ?? null;
                        $var_note = $response_setting->note ?? null;
                        $var_sn = $response_setting->sn ?? null;
                        $var_errorrefund = $response_setting->errorrefund;

                        $exploding_errorefund = explode('[#]', strtolower($var_errorrefund ?? ''));

                        if ($response_setting->index != null) {
                            $order = $order[$response_setting->index] ?? null;
                        }

                        $referenceid = $var_referenceid != null ? ($order[$var_referenceid] ?? null) : null;
                        $price = $var_price != null ? ($order[$var_price] ?? null) : null;
                        $status = $var_status != null ? ($order[$var_status] ?? null) : null;
                        $note = $var_note != null ? ($order[$var_note] ?? null) : null;
                        $sn = $var_sn != null ? ($order[$var_sn] ?? null) : null;

                        if ($status != null) {
                            if (in_array($status, $exploding_errorefund)) {
                                log_message_user('error', '[MULTIVENDOR ORDER] Failed to parsing error refund, response: ' . json_encode($order), $this->merchant->id);
                                throw new Exception('Gagal melakukan transaksi');
                            }
                        } else {
                            if ($var_status == null) {
                                $status = 'pending';
                            } else if ($var_status != null && $status == null) {
                                log_message_user('error', '[MULTIVENDOR ORDER] Failed to parsing status, response: ' . json_encode($order), $this->merchant->id);

                                throw new Exception('Gagal melakukan transaksi');
                            }
                        }

                        $update = array();
                        $update['jsonresponse'] = json_encode($order);

                        if ($referenceid != null) {
                            $update['servercode'] = $referenceid;
                        }

                        if ($price != null) {
                            $update['price'] = $price;
                        }

                        if ($status != null) {
                            $update['status'] = $status;
                        }

                        if ($note != null) {
                            $update['note'] = $note;
                        }

                        if ($sn != null) {
                            $update['sn'] = $sn;
                        }

                        $this->trorder->update(array(
                            'id' => $insert_id
                        ), $update);

                        $this->db->trans_commit();

                        $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

                        $this->output->set_status_header(200);
                        $this->output->set_content_type('application/json');

                        return JSONResponse(array(
                            'status' => true,
                            'message' => 'Transaksi berhasil',
                            'transactionid' => $insert_id,
                        ));
                    } else {
                        log_message_user('error', '[MULTIVENDOR ORDER] Failed to parse response, response: ' . json_encode($order), $this->merchant->id);
                        throw new Exception('Vendor tidak ditemukan');
                    }
                }
            }

            $this->db->trans_commit();

            $this->send_notification($insert_id, $this->merchant->id, $currentuser->phonenumber);

            $this->output->set_status_header(200);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => true,
                'message' => 'Transaksi berhasil',
                'transactionid' => $insert_id,
            ));
        } catch (Exception $ex) {
            $this->db->trans_rollback();

            $this->output->set_status_header(400);
            $this->output->set_content_type('application/json');

            return JSONResponse(array(
                'status' => false,
                'message' => $ex->getMessage(),
            ));
        }
    }
}
