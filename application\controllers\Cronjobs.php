<?php

use <PERSON><PERSON><PERSON>\GojekPay;
use <PERSON><PERSON><PERSON>\Ovo;

defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsUsers $msusers
 * @property MsProduct $msproduct
 * @property MsPaymentGateway $mspaymentgateway
 * @property TrOrder $trorder
 * @property ApiKeys $apikeys
 * @property QueueSync $queuesync
 * @property ProductPriceLog $productpricelog
 * @property LoginActivity $loginactivity
 * @property ErrorLogger $errorlogger
 * @property Invoices $invoices
 * @property DomainRequestNs $domainrequestns
 * @property Deposits $deposits
 * @property CI_DB_mysqli_driver $db
 * @property TbConfigs $tbconfigs
 * @property MsRole $msrole
 * @property HistoryBalance $historybalance
 * @property MobileNotification $mobilenotification
 * @property ApiKeys_WhatsApp $apikeys_whatsapp
 * @property QueueBuildApp $queuebuildapp
 * @property ThemeConfiguration $themeconfiguration
 * @property MsVendor $msvendor
 * @property MsVendorDetail $msvendordetail
 * @property MsLicense $mslicense
 * @property MsTempProduct $mstempproduct
 * @property MsEmailUnsubscribe $msemailunsubscribe
 */
class Cronjobs extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('Deposits', 'deposits');
        $this->load->model('TbConfigs', 'tbconfigs');
        $this->load->model('MsUsers', 'msusers');
        $this->load->model('MsProduct', 'msproduct');
        $this->load->model('MsPaymentGateway', 'mspaymentgateway');
        $this->load->model('TrOrder', 'trorder');
        $this->load->model('ApiKeys', 'apikeys');
        $this->load->model('QueueSync', 'queuesync');
        $this->load->model('ProductPriceLog', 'productpricelog');
        $this->load->model('LoginActivity', 'loginactivity');
        $this->load->model('ErrorLogger', 'errorlogger');
        $this->load->model('Invoices', 'invoices');
        $this->load->model('DomainRequestNs', 'domainrequestns');
        $this->load->model('MsRole', 'msrole');
        $this->load->model('MobileNotification', 'mobilenotification');
        $this->load->model('ApiKeys_WhatsApp', 'apikeys_whatsapp');
        $this->load->model('HistoryBalance', 'historybalance');
        $this->load->model('QueueBuildApp', 'queuebuildapp');
        $this->load->model('ThemeConfiguration', 'themeconfiguration');
        $this->load->model('MsVendor', 'msvendor');
        $this->load->model('MsVendorDetail', 'msvendordetail');
        $this->load->model('MsLicense', 'mslicense');
        $this->load->model('MsTempProduct', 'mstempproduct');
        $this->load->model('MsEmailUnsubscribe', 'msemailunsubscribe');
    }

    public function topup_transaction_midtrans()
    {
        try {
            $pending = $this->deposits->select('a.*')
                ->join('msusers b', 'b.id = a.userid')
                ->result(array(
                    'a.paymenttype' => 'Otomatis',
                    'a.status' => 'Pending',
                    'a.merchantid' => null,
                    'a.gatewayvendor' => 'Midtrans',
                    'a.snaptoken !=' => null
                ));

            if (count($pending) > 0) {
                foreach ($pending as $key => $value) {
                    $status = (object)Midtrans\Transaction::status($value->code);

                    if ($status->transaction_status == 'settlement') {
                        $this->db->trans_begin();

                        $check_history_balance = $this->historybalance->total(array(
                            'userid' => $value->userid,
                            'type' => 'IN',
                            'depositid' => $value->id
                        ));

                        if ($check_history_balance == 0) {
                            $currentbalance = getCurrentBalance($value->userid, true);

                            $inserthistorybalance = array();
                            $inserthistorybalance['userid'] = $value->userid;
                            $inserthistorybalance['type'] = 'IN';
                            $inserthistorybalance['nominal'] = $value->nominal;
                            $inserthistorybalance['currentbalance'] = $currentbalance;
                            $inserthistorybalance['depositid'] = $value->id;
                            $inserthistorybalance['createdby'] = $value->userid;
                            $inserthistorybalance['createddate'] = getCurrentDate();

                            $this->historybalance->insert($inserthistorybalance);

                            $updateUser = array();
                            $updateUser['balance'] = $currentbalance + $value->nominal;

                            $this->msusers->update(array(
                                'id' => $value->userid
                            ), $updateUser);
                        }

                        $update = array();
                        $update['status'] = $status->transaction_status;

                        $this->deposits->update(array(
                            'id' => $value->id
                        ), $update);

                        if ($this->db->trans_status() === FALSE) {
                            throw new Exception('Error');
                        }

                        $this->db->trans_commit();
                    }
                }
            }
        } catch (Exception $ex) {
            $this->db->trans_rollback();
        }
    }

    public function topup_transaction()
    {
        try {
            $this->db->trans_begin();

            if (get_setting_web('bca_bot') == 1) {
                $pending = $this->deposits->select('a.*')
                    ->join('msusers b', 'b.id = a.userid')
                    ->result(array(
                        'a.payment' => 'Bank BCA',
                        'a.status' => 'Pending',
                        'a.paymenttype' => 'Otomatis',
                        'a.merchantid' => null
                    ));

                if (count($pending) > 0) {
                    $bca = new BCAV2(String_Helper::BCA_USERNAME, String_Helper::BCA_PASSWORD);
                    $mutasi = $bca->getMutasiRekening();

                    if ($mutasi['RESULT'] == 'ERROR') {
                        $get_config = get_setting_web('bca_failed_login_count');

                        $update = array();
                        $update['configvalue'] = $get_config != null ? $get_config + 1 : 1;

                        $this->tbconfigs->update(array(
                            'configname' => 'bca_failed_login_count'
                        ), $update);

                        if ($update['configvalue'] == 2) {
                            $update = array();
                            $update['configvalue'] = 0;

                            $this->tbconfigs->update(array(
                                'configname' => 'bca_bot'
                            ), $update);
                        }
                    } else {
                        $update = array();
                        $update['configvalue'] = 0;

                        $this->tbconfigs->update(array(
                            'configname' => 'bca_failed_login_count'
                        ), $update);

                        $mutasi_rekening = array();
                        if (isset($mutasi['DATA'])) {
                            foreach ($mutasi['DATA'] as $key => $value) {
                                if ($value['mutasi'] == 'CR') {
                                    $data_mutasi = array();
                                    $data_mutasi['date'] = $value['tanggal'];
                                    $data_mutasi['nominal'] = $value['nominal'];

                                    $mutasi_rekening[] = $data_mutasi;
                                }
                            }
                        }

                        foreach ($pending as $key => $value) {
                            $user = getCurrentUser($value->userid);
                            if ($user == null) continue;

                            $one_days = date('Y-m-d H:i:s', strtotime("$value->createddate +1 days"));

                            if (getCurrentDate() >= $one_days) {
                                $update = array();
                                $update['status'] = 'Failed';

                                $this->deposits->update(array(
                                    'id' => $value->id
                                ), $update);
                            } else {
                                $found = false;

                                foreach ($mutasi_rekening as $k => $v) {
                                    if ($found == true) continue;

                                    $nominal = $v['nominal'];
                                    $nominal = str_replace(",", "", $nominal);
                                    $nominal = str_replace(".", "", $nominal);

                                    if ($nominal == $value->nominal) {
                                        $check_history_balance = $this->historybalance->total(array(
                                            'userid' => $value->userid,
                                            'type' => 'IN',
                                            'depositid' => $value->id
                                        ));

                                        if ($check_history_balance == 0) {
                                            $inserthistorybalance = array();
                                            $inserthistorybalance['userid'] = $value->userid;
                                            $inserthistorybalance['type'] = 'IN';
                                            $inserthistorybalance['nominal'] = $value->nominal;
                                            $inserthistorybalance['currentbalance'] = $user->balance;
                                            $inserthistorybalance['depositid'] = $value->id;
                                            $inserthistorybalance['createdby'] = $value->userid;
                                            $inserthistorybalance['createddate'] = getCurrentDate();

                                            $this->historybalance->insert($inserthistorybalance);

                                            $update = array();
                                            $update['balance'] = $user->balance + $value->nominal;

                                            $this->msusers->update(array(
                                                'id' => $value->userid
                                            ), $update);
                                        }

                                        $update = array();
                                        $update['status'] = 'Success';

                                        $this->deposits->update(array(
                                            'id' => $value->id
                                        ), $update);

                                        $found = true;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Error');
            }

            $this->db->trans_commit();
        } catch (Exception $ex) {
            $this->db->trans_rollback();
        }
    }

    public function member_topup_transaction_ovo()
    {
        try {
            $this->db->trans_begin();

            $users = $this->msusers->select('id')
                ->result(array(
                    'merchantid' => null
                ));

            foreach ($users as $key => $value) {
                $pending = $this->deposits->select('a.*')
                    ->join('msusers b', 'b.id = a.userid')
                    ->result(array(
                        'a.merchantid' => $value->id,
                        'a.paymenttype' => 'Otomatis',
                        'a.status' => 'Pending',
                        'a.payment' => 'OVO'
                    ));

                if (count($pending) > 0) {
                    $paymentgateway = $this->mspaymentgateway->get(array(
                        'userid' => $value->id,
                        'type' => 'OVO'
                    ));

                    if ($paymentgateway->num_rows() == 0) continue;
                    $paymentgatewayRow = $paymentgateway->row();
                    $detailPaymentGateway = json_decode(stringEncryption('decrypt', $paymentgatewayRow->detail));

                    if ($detailPaymentGateway == null) continue;

                    $transactionlist = array();
                    $ovo = new Ovo($detailPaymentGateway->token);
                    $transaction = $ovo->transactionHistory();

                    if ($transaction) {
                        $transaction = json_decode($transaction);

                        if (isset($transaction->response_message) && $transaction->response_message == 'Success') {
                            if ($transaction->data->status == 200) {
                                $firstpage = $transaction->data->orders[0];

                                if (isset($firstpage->complete)) {
                                    $completetransaction = $firstpage->complete;

                                    foreach ($completetransaction as $k => $v) {
                                        if ($v->transaction_type == 'INCOMING TRANSFER') {
                                            $transactionlist[] = $v;
                                        }
                                    }

                                    if (count($transactionlist) > 0) {
                                        foreach ($pending as $key_pending => $value_pending) {
                                            $user = getCurrentUser($value_pending->userid);
                                            if ($user == null) continue;

                                            $one_days = date('Y-m-d H:i:s', strtotime($value_pending->createddate . ' +1 days'));

                                            if (getCurrentDate() >= $one_days) {
                                                $update = array();
                                                $update['status'] = 'Failed';

                                                $this->deposits->update(array(
                                                    'id' => $value_pending->id
                                                ), $update);

                                                $this->send_notification($value_pending->id, $value_pending->merchantid, $value_pending->phonenumber, 'deposit');
                                            } else {
                                                $found = false;

                                                foreach ($transactionlist as $key_mutasi => $value_mutasi) {
                                                    if ($found) continue;

                                                    if ($value_mutasi->transaction_amount == $value_pending->nominal) {
                                                        $check_history_balance = $this->historybalance->total(array(
                                                            'userid' => $value_pending->userid,
                                                            'type' => 'IN',
                                                            'depositid' => $value_pending->id
                                                        ));

                                                        if ($check_history_balance == 0) {
                                                            $balance = null;
                                                            if ($value_pending->isbonus == 1) {
                                                                $balance = $value_pending->nominal + $value_pending->nominalbonus - ($value_pending->fee ?? 0);
                                                            } else {
                                                                $balance = $value_pending->nominal - ($value_pending->fee ?? 0);
                                                            }

                                                            $inserthistorybalance = array();
                                                            $inserthistorybalance['userid'] = $value_pending->userid;
                                                            $inserthistorybalance['type'] = 'IN';
                                                            $inserthistorybalance['nominal'] = $balance;
                                                            $inserthistorybalance['currentbalance'] = $user->balance;
                                                            $inserthistorybalance['depositid'] = $value_pending->id;
                                                            $inserthistorybalance['createdby'] = $value_pending->userid;
                                                            $inserthistorybalance['createddate'] = getCurrentDate();

                                                            $this->historybalance->insert($inserthistorybalance);

                                                            $balance = $user->balance + $balance;

                                                            $update = array();
                                                            $update['balance'] = $balance;

                                                            $this->msusers->update(array(
                                                                'id' => $value_pending->userid
                                                            ), $update);
                                                        }

                                                        $update = array();
                                                        $update['status'] = 'Success';

                                                        $this->deposits->update(array(
                                                            'id' => $value_pending->id
                                                        ), $update);

                                                        $this->send_notification($value_pending->id, $value_pending->merchantid, $value_pending->phonenumber, 'deposit');

                                                        $found = true;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            } else {
                                log_message_user('error', '[OVO CRON TRANSACTION] Response: ' . json_encode($transaction), $value->id);
                            }
                        } else {
                            log_message_user('error', '[OVO CRON TRANSACTION] Response: ' . json_encode($transaction), $value->id);
                        }
                    }
                }
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Error');
            }

            $this->db->trans_commit();
        } catch (Exception $ex) {
            $this->db->trans_rollback();
        }
    }

    public function member_topup_transaction_gopay()
    {
        try {
            $this->db->trans_begin();

            $users = $this->msusers->select('id')
                ->result(array(
                    'merchantid' => null
                ));

            foreach ($users as $key => $value) {
                $pending = $this->deposits->select('a.*')
                    ->join('msusers b', 'b.id = a.userid')
                    ->result(array(
                        'a.merchantid' => $value->id,
                        'a.paymenttype' => 'Otomatis',
                        'a.status' => 'Pending',
                        'a.payment' => 'GOPAY'
                    ));

                if (count($pending) > 0) {
                    $paymentgateway = $this->mspaymentgateway->get(array(
                        'userid' => $value->id,
                        'type' => 'GOPAY'
                    ));

                    if ($paymentgateway->num_rows() == 0) continue;
                    $paymentgatewayRow = $paymentgateway->row();
                    $detailPaymentGateway = json_decode(stringEncryption('decrypt', $paymentgatewayRow->detail));

                    if ($detailPaymentGateway == null) continue;

                    $gopay = new GojekPay($detailPaymentGateway->token);
                    $mutasi = json_decode($gopay->getTransactionHistory());

                    if (isset($mutasi->success) && $mutasi->success == true) {
                        $data = $mutasi->data->success;

                        $mutasi_rekening = array();
                        foreach ($data as $key_data => $value_data) {
                            if ($value_data->type == 'credit' && $value_data->status == 'SUCCESS') {
                                $data_mutasi = array();
                                $data_mutasi['amount'] = $value_data->amount->value;

                                $mutasi_rekening[] = $data_mutasi;
                            }
                        }

                        foreach ($pending as $key_pending => $value_pending) {
                            $user = getCurrentUser($value_pending->userid);
                            if ($user == null) continue;

                            $one_days = date('Y-m-d H:i:s', strtotime("$value_pending->createddate +1 days"));

                            if (getCurrentDate() >= $one_days) {
                                $update = array();
                                $update['status'] = 'Failed';

                                $this->deposits->update(array(
                                    'id' => $value_pending->id
                                ), $update);

                                $this->send_notification($value_pending->id, $value_pending->merchantid, $value_pending->phonenumber, 'deposit');
                            } else {
                                $found = false;

                                foreach ($mutasi_rekening as $key_mutasi => $value_mutasi) {
                                    if ($found == true) continue;

                                    if ($value_mutasi['amount'] == $value_pending->nominal) {
                                        $check_history_balance = $this->historybalance->total(array(
                                            'userid' => $value_pending->userid,
                                            'type' => 'IN',
                                            'depositid' => $value_pending->id
                                        ));

                                        if ($check_history_balance == 0) {
                                            $balance = null;
                                            if ($value_pending->isbonus == 1) {
                                                $balance = $value_pending->nominal + $value_pending->nominalbonus - ($value_pending->fee ?? 0);
                                            } else {
                                                $balance = $value_pending->nominal - ($value_pending->fee ?? 0);
                                            }

                                            $inserthistorybalance = array();
                                            $inserthistorybalance['userid'] = $value_pending->userid;
                                            $inserthistorybalance['type'] = 'IN';
                                            $inserthistorybalance['nominal'] = $balance;
                                            $inserthistorybalance['currentbalance'] = $user->balance;
                                            $inserthistorybalance['depositid'] = $value_pending->id;
                                            $inserthistorybalance['createdby'] = $value_pending->userid;
                                            $inserthistorybalance['createddate'] = getCurrentDate();

                                            $this->historybalance->insert($inserthistorybalance);

                                            $balance = $user->balance + $balance;

                                            $update = array();
                                            $update['balance'] = $balance;

                                            $this->msusers->update(array(
                                                'id' => $value_pending->userid
                                            ), $update);
                                        }

                                        $update = array();
                                        $update['status'] = 'Success';

                                        $this->deposits->update(array(
                                            'id' => $value_pending->id
                                        ), $update);

                                        $this->send_notification($value_pending->id, $value_pending->merchantid, $value_pending->phonenumber, 'deposit');

                                        $found = true;
                                    }
                                }
                            }
                        }
                    } else {
                        if ($mutasi != null) {
                            log_message_user('error', '[GOPAY CRON TRANSACTION TOPUP] Response: ' . json_encode($mutasi), $value->id);
                        }
                    }
                }
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Error');
            }

            $this->db->trans_commit();
        } catch (Exception $ex) {
            $this->db->trans_rollback();
        }
    }

    public function member_topup_transaction_bca()
    {
        try {
            $this->db->trans_begin();

            $users = $this->msusers->result(array(
                'merchantid' => null,
                'bca_bot' => 1,
                "(bca_failed_login_count IS NULL OR bca_failed_login_count < 2) =" => true,
                'licenseid !=' => null,
                'expireddate >' => getCurrentDate(),
            ));

            foreach ($users as $key => $value) {
                $pending = $this->deposits->select('a.*')
                    ->join('msusers b', 'b.id = a.userid')
                    ->result(array(
                        'a.merchantid' => $value->id,
                        'a.paymenttype' => 'Otomatis',
                        'a.status' => 'Pending',
                        'a.payment' => 'Bank BCA'
                    ));

                if (count($pending) == 0) continue;

                $paymentgateway = $this->mspaymentgateway->get(array(
                    'userid' => $value->id,
                    'type' => 'Bank BCA',
                ));

                if ($paymentgateway->num_rows() == 0) continue;
                $paymentgatewayRow = $paymentgateway->row();
                $detailPaymentGateway = json_decode(stringEncryption('decrypt', $paymentgatewayRow->detail));

                if ($detailPaymentGateway == null) continue;

                $bca = new BCAV2($detailPaymentGateway->username, $detailPaymentGateway->password);
                $mutasi = $bca->getMutasiRekening();

                if ($mutasi['RESULT'] == 'ERROR') {
                    $update = array();
                    $update['bca_failed_login_count'] = $value->bca_failed_login_count + 1;

                    if ($update['bca_failed_login_count'] == 2) {
                        $update['bca_bot'] = 0;
                    }

                    $this->msusers->update(array(
                        'id' => $value->id
                    ), $update);
                } else {
                    $update = array();
                    $update['bca_failed_login_count'] = 0;

                    $this->msusers->update(array(
                        'id' => $value->id
                    ), $update);

                    $mutasi_rekening = array();
                    if (isset($mutasi['DATA'])) {
                        foreach ($mutasi['DATA'] as $key_data => $value_data) {
                            if (isset($value_data['mutasi']) && $value_data['mutasi'] == 'CR') {
                                $data_mutasi = array();
                                $data_mutasi['date'] = $value_data['tanggal'];
                                $data_mutasi['nominal'] = $value_data['nominal'];

                                $mutasi_rekening[] = $data_mutasi;
                            }
                        }
                    }

                    foreach ($pending as $key_pending => $value_pending) {
                        $user = getCurrentUser($value_pending->userid);
                        if ($user == null) continue;

                        $one_days = date('Y-m-d H:i:s', strtotime("$value_pending->createddate +1 days"));

                        if (getCurrentDate() >= $one_days) {
                            $update = array();
                            $update['status'] = 'Failed';

                            $this->deposits->update(array(
                                'id' => $value_pending->id
                            ), $update);

                            $this->send_notification($value_pending->id, $value_pending->merchantid, $value_pending->phonenumber, 'deposit');
                        } else {
                            $found = false;

                            foreach ($mutasi_rekening as $key_mutasi => $value_mutasi) {
                                if ($found == true) continue;

                                $nominal = $value_mutasi['nominal'];
                                $nominal = str_replace(",", "", $nominal);
                                $nominal = str_replace(".", "", $nominal);
                                $nominal = substr($nominal, 0, -2);

                                if ($nominal == $value_pending->nominal) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_pending->userid,
                                        'type' => 'IN',
                                        'depositid' => $value_pending->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $balance = null;
                                        if ($value_pending->isbonus == 1) {
                                            $balance = $value_pending->nominal + $value_pending->nominalbonus - ($value_pending->fee ?? 0);
                                        } else {
                                            $balance = $value_pending->nominal - ($value_pending->fee ?? 0);
                                        }

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_pending->userid;
                                        $inserthistorybalance['type'] = 'IN';
                                        $inserthistorybalance['nominal'] = $balance;
                                        $inserthistorybalance['currentbalance'] = $user->balance;
                                        $inserthistorybalance['depositid'] = $value_pending->id;
                                        $inserthistorybalance['createdby'] = $value_pending->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $balance = $user->balance + $balance;

                                        $update = array();
                                        $update['balance'] = $balance;

                                        $this->msusers->update(array(
                                            'id' => $value_pending->userid
                                        ), $update);
                                    }

                                    $update = array();
                                    $update['status'] = 'Success';

                                    $this->deposits->update(array(
                                        'id' => $value_pending->id
                                    ), $update);

                                    $this->send_notification($value_pending->id, $value_pending->merchantid, $value_pending->phonenumber, 'deposit');

                                    $found = true;
                                }
                            }
                        }
                    }
                }
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Error');
            }

            $this->db->trans_commit();
        } catch (Exception $ex) {
            $this->db->trans_rollback();
        }
    }

    public function member_product_transaction()
    {
        try {
            $users = $this->msusers->select('a.id')
                ->result(array(
                    'a.merchantid' => null,
                    'a.licenseid !=' => null,
                    'a.expireddate >=' => getCurrentDate(),
                    "(a.multivendor IS NULL OR a.multivendor = 0) =" => true,
                ));

            foreach ($users as $key_user => $value_user) {
                $ppob = getCurrentAPIKeys('PPOB', $value_user->id);
                $smm = getCurrentAPIKeys('SMM', $value_user->id);

                $whereorder = array(
                    "(LOWER(a.status) = 'pending' OR LOWER(a.status) = 'in progress' OR LOWER(a.status) = 'processing') =" => true,
                    "(b.merchantid = '$value_user->id' OR a.merchantid_order = '$value_user->id') =" => true,
                    "(a.queuetransaction IS NULL OR a.queuetransaction = 0) =" => true,
                    'a.servercode !=' => null,
                    "(a.status_payment = 'sukses' OR a.status_payment IS NULL) =" => true,
                    'a.vendorid' => null
                );

                if ($ppob != null && $smm != null) {
                    $whereorder["(c.vendor = '$ppob->vendor' OR c.vendor = '$smm->vendor' OR a.vendor = '$ppob->vendor' OR a.vendor = '$smm->vendor') ="] = true;
                } else if ($ppob != null && $smm == null) {
                    $whereorder["(c.vendor = '$ppob->vendor' OR a.vendor = '$ppob->vendor') ="] = true;
                } else if ($ppob == null && $smm != null) {
                    $whereorder["(c.vendor = '$smm->vendor' OR a.vendor = '$smm->vendor') ="] = true;
                }

                $order = $this->trorder->select('a.*, c.vendor AS vendor_product, c.category_apikey AS category_apikey_product, c.subcategory_apikey AS subcategory_apikey_product, c.type AS producttype, c.category, c.code, b.phonenumber, b.merchantid')
                    ->join('msusers b', 'b.id = a.userid', 'LEFT')
                    ->join('msproduct c', 'c.id = a.serviceid', 'LEFT')
                    ->result($whereorder);

                foreach ($order as $key_order => $value_order) {
                    $this->db->trans_begin();

                    if (($value_order->category_apikey == 'PPOB' || $value_order->category_apikey_product == 'PPOB') && ($value_order->subcategory_apikey != 'PASCABAYAR' || $value_order->subcategory_apikey_product != 'PASCABAYAR')) {
                        if ($ppob == null) {
                            $this->db->trans_rollback();

                            continue;
                        }

                        if ($ppob->vendor == 'Digiflazz' && $ppob->priority_callback == 1) {
                            $this->db->trans_rollback();

                            continue;
                        }

                        if ($value_order->vendor == 'Digiflazz' || $value_order->vendor_product == 'Digiflazz') {
                            $digiflazz = new Digiflazz(stringEncryption('decrypt', $ppob->usercode), stringEncryption('decrypt', $ppob->apikey));
                            $status = $digiflazz->topup($value_order->code ?? $value_order->productcode, $value_order->target, $value_order->servercode);
                            $result = json_decode($status);

                            if (isset($result->data->status) && $result->data->status) {
                                $update = array();
                                $update['note'] = $result->data->message;
                                $update['sn'] = $result->data->sn ?? '';
                                $update['status'] = strtolower($result->data->status);
                                $update['jsonresponse'] = json_encode($result);
                                $update['updateddate'] = getCurrentDate();

                                if ($value_order->queuetransaction == 1) {
                                    $update['queuetransaction'] = 0;
                                }

                                $this->trorder->update(array(
                                    'id' => $value_order->id
                                ), $update);

                                if (strtolower($result->data->status) == 'gagal' && $value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                        } else {
                                            $inserthistorybalance['nominal'] = $value_order->price;
                                        }

                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $updateUser = array();

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $updateUser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                        } else {
                                            $updateUser['balance'] = $currentbalance + $value_order->price;
                                        }

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $updateUser);
                                    }
                                }

                                if (strtolower($result->data->status) != strtolower($value_order->status)) {
                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value_order->merchantid ?? $value_order->merchantid_order, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                }
                            } else {
                                if ($result != null) {
                                    log_message_user('error', '[DIGIFLAZZ CRON TRANSACTION PRODUCT] Response: ' . json_encode($result), $value_user->id);
                                }
                            }
                        } else if ($value_order->vendor == 'VIPayment' || $value_order->vendor_product == 'VIPayment') {
                            $vipayment = new VIPayment(stringEncryption('decrypt', $ppob->usercode), stringEncryption('decrypt', $ppob->apikey));

                            if ($value_order->subcategory_apikey == 'PRABAYAR' || $value_order->subcategory_apikey_product == 'PRABAYAR') {
                                if ($value_order->producttype != 'game') {
                                    $status = $vipayment->status_prepaid($value_order->servercode);
                                } else {
                                    $status = $vipayment->status_game($value_order->servercode);
                                }

                                if (isset($status->result) && $status->result) {
                                    $update = array();
                                    $update['status'] = $status->data[0]->status;

                                    if ($value_order->queuetransaction == 1) {
                                        $update['queuetransaction'] = 0;
                                    }

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    if (strtolower($status->data[0]->status) == 'error' && $value_order->userid != null) {
                                        $check_history_balance = $this->historybalance->total(array(
                                            'userid' => $value_order->userid,
                                            'type' => 'IN',
                                            'orderid' => $value_order->id
                                        ));

                                        if ($check_history_balance == 0) {
                                            $currentbalance = getCurrentBalance($value_order->userid, true);

                                            $inserthistorybalance = array();
                                            $inserthistorybalance['userid'] = $value_order->userid;
                                            $inserthistorybalance['type'] = 'IN';

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                            } else {
                                                $inserthistorybalance['nominal'] = $value_order->price;
                                            }

                                            $inserthistorybalance['currentbalance'] = $currentbalance;
                                            $inserthistorybalance['orderid'] = $value_order->id;
                                            $inserthistorybalance['createdby'] = $value_order->userid;
                                            $inserthistorybalance['createddate'] = getCurrentDate();

                                            $this->historybalance->insert($inserthistorybalance);

                                            $update = array();

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $update['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                            } else {
                                                $update['balance'] = $currentbalance + $value_order->price;
                                            }

                                            $this->msusers->update(array(
                                                'id' => $value_order->userid
                                            ), $update);
                                        }
                                    }

                                    if (strtolower($status->data[0]->status) != strtolower($value_order->status)) {
                                        pullTransaction($value_order->userid ?? $value_order->clientip);
                                        $this->send_notification($value_order->id, $value_order->merchantid ?? $value_order->merchantid_order, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                    }
                                } else {
                                    if ($status != null) {
                                        log_message_user('error', '[VIPAYMENT CRON TRANSACTION PRODUCT] Response: ' . json_encode($status), $value_user->id);
                                    }
                                }
                            } else if ($value_order->subcategory_apikey == 'PASCABAYAR' || $value_order->subcategory_apikey_product == 'PASCABAYAR') {
                                $status = $vipayment->status_postpaid($value_order->servercode);

                                if (isset($status->result) && $status->result) {
                                    $update = array();
                                    $update['status'] = $status->data[0]->status;

                                    if ($value_order->queuetransaction == 1) {
                                        $update['queuetransaction'] = 0;
                                    }

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    if (strtolower($status->data[0]->status) == 'error' && $value_order->userid != null) {
                                        $check_history_balance = $this->historybalance->total(array(
                                            'userid' => $value_order->userid,
                                            'type' => 'IN',
                                            'orderid' => $value_order->id
                                        ));

                                        if ($check_history_balance == 0) {
                                            $currentbalance = getCurrentBalance($value_order->userid, true);

                                            $inserthistorybalance = array();
                                            $inserthistorybalance['userid'] = $value_order->userid;
                                            $inserthistorybalance['type'] = 'IN';

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                            } else {
                                                $inserthistorybalance['nominal'] = $value_order->price;
                                            }

                                            $inserthistorybalance['currentbalance'] = $currentbalance;
                                            $inserthistorybalance['orderid'] = $value_order->id;
                                            $inserthistorybalance['createdby'] = $value_order->userid;
                                            $inserthistorybalance['createddate'] = getCurrentDate();

                                            $this->historybalance->insert($inserthistorybalance);

                                            $update = array();

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $update['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                            } else {
                                                $update['balance'] = $currentbalance + $value_order->price;
                                            }

                                            $this->msusers->update(array(
                                                'id' => $value_order->userid
                                            ), $update);
                                        }
                                    }

                                    if (strtolower($status->data[0]->status) != strtolower($value_order->status)) {
                                        pullTransaction($value_order->userid ?? $value_order->clientip);
                                        $this->send_notification($value_order->id, $value_order->merchantid ?? $value_order->merchantid_order, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                    }
                                } else {
                                    if ($status != null) {
                                        log_message_user('error', '[VIPAYMENT CRON TRANSACTION PRODUCT] Response: ' . json_encode($status), $value_user->id);
                                    }
                                }
                            }
                        }
                    } else if ($value_order->category_apikey == 'SMM' || $value_order->category_apikey_product == 'SMM') {
                        if ($smm == null) {
                            $this->db->trans_rollback();
                            continue;
                        }

                        if ($value_order->vendor == 'BuzzerPanel' || $value_order->vendor_product == 'BuzzerPanel') {
                            $buzzerpanel = new BuzzerPanel(stringEncryption('decrypt', $smm->apikey), stringEncryption('decrypt', $smm->secretkey));
                            $status = $buzzerpanel->status($value_order->servercode);

                            if (isset($status->status) && $status->status) {
                                $update = array();
                                $update['status'] = $status->data->status;
                                $update['startcount'] = $status->data->start_count;
                                $update['remain'] = $status->data->remains;

                                if ($value_order->queuetransaction == 1) {
                                    $update['queuetransaction'] = 0;
                                }

                                $this->trorder->update(array(
                                    'id' => $value_order->id
                                ), $update);

                                if (strtolower($status->data->status) == 'error' && $value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                        } else {
                                            $inserthistorybalance['nominal'] = $value_order->price;
                                        }

                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $update = array();

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $update['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                        } else {
                                            $update['balance'] = $currentbalance + $value_order->price;
                                        }

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $update);
                                    }
                                } else if (strtolower($status->data->status) == 'partial' && $value_order->userid != null) {
                                    if (isset($status->data->remains)) {
                                        $check_history_balance = $this->historybalance->total(array(
                                            'userid' => $value_order->userid,
                                            'type' => 'IN',
                                            'orderid' => $value_order->id
                                        ));

                                        if ($check_history_balance == 0) {
                                            $currentbalance = getCurrentBalance($value_order->userid, true);

                                            $remains = $status->data->remains;
                                            $qty = $value_order->qty;
                                            $price = $value_order->price;
                                            $peritem = $price / $qty;

                                            $fee = round($value_order->fee ?? 0);
                                            if ($fee > 0) {
                                                $feeitem = $fee / $qty;
                                                $feerefund = (int)($remains * $feeitem);
                                            } else {
                                                $feerefund = 0;
                                            }

                                            $refund = (int)($remains * $peritem);

                                            $inserthistorybalance = array();
                                            $inserthistorybalance['userid'] = $value_order->userid;
                                            $inserthistorybalance['type'] = 'IN';
                                            $inserthistorybalance['nominal'] = $refund - $feerefund;
                                            $inserthistorybalance['currentbalance'] = $currentbalance;
                                            $inserthistorybalance['orderid'] = $value_order->id;
                                            $inserthistorybalance['createdby'] = $value_order->userid;
                                            $inserthistorybalance['createddate'] = getCurrentDate();

                                            $this->historybalance->insert($inserthistorybalance);

                                            $update = array();
                                            $update['balance'] = $currentbalance + $refund - $feerefund;

                                            $this->msusers->update(array(
                                                'id' => $value_order->userid
                                            ), $update);
                                        }
                                    }
                                }

                                if (strtolower($status->data->status) != strtolower($value_order->status)) {
                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value_order->merchantid ?? $value_order->merchantid_order, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                }
                            } else {
                                if ($status != null) {
                                    log_message_user('error', '[BUZZERPANEL CRON TRANSACTION PRODUCT] Response: ' . json_encode($status), $value_user->id);
                                }
                            }
                        } else if ($value_order->vendor == 'MedanPedia' || $value_order->vendor_product == 'MedanPedia') {
                            $medanpedia = new MedanPedia(stringEncryption('decrypt', $smm->usercode), stringEncryption('decrypt', $smm->apikey));
                            $status = $medanpedia->status($value_order->servercode);

                            if (isset($status->status) && $status->status) {
                                $update = array();
                                $update['status'] = $status->data->status;
                                $update['startcount'] = $status->data->start_count;
                                $update['remain'] = $status->data->remains;

                                if ($value_order->queuetransaction == 1) {
                                    $update['queuetransaction'] = 0;
                                }

                                $this->trorder->update(array(
                                    'id' => $value_order->id
                                ), $update);

                                if (strtolower($status->data->status) == 'error' && $value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                        } else {
                                            $inserthistorybalance['nominal'] = $value_order->price;
                                        }

                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $update = array();

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $update['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                        } else {
                                            $update['balance'] = $currentbalance + $value_order->price;
                                        }

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $update);
                                    }
                                } else if (strtolower($status->data->status) == 'partial' && $value_order->userid != null) {
                                    if (isset($status->data->remains)) {
                                        $check_history_balance = $this->historybalance->total(array(
                                            'userid' => $value_order->userid,
                                            'type' => 'IN',
                                            'orderid' => $value_order->id
                                        ));

                                        if ($check_history_balance == 0) {
                                            $currentbalance = getCurrentBalance($value_order->userid, true);

                                            $remains = $status->data->remains;
                                            $qty = $value_order->qty;
                                            $price = $value_order->price;
                                            $peritem = $price / $qty;

                                            $fee = round($value_order->fee ?? 0);
                                            if ($fee > 0) {
                                                $feeitem = $fee / $qty;
                                                $feerefund = (int)($remains * $feeitem);
                                            } else {
                                                $feerefund = 0;
                                            }

                                            $refund = (int)($remains * $peritem);

                                            $inserthistorybalance = array();
                                            $inserthistorybalance['userid'] = $value_order->userid;
                                            $inserthistorybalance['type'] = 'IN';
                                            $inserthistorybalance['nominal'] = $refund - $feerefund;
                                            $inserthistorybalance['currentbalance'] = $currentbalance;
                                            $inserthistorybalance['orderid'] = $value_order->id;
                                            $inserthistorybalance['createdby'] = $value_order->userid;
                                            $inserthistorybalance['createddate'] = getCurrentDate();

                                            $this->historybalance->insert($inserthistorybalance);

                                            $update = array();
                                            $update['balance'] = $currentbalance + $refund - $feerefund;

                                            $this->msusers->update(array(
                                                'id' => $value_order->userid
                                            ), $update);
                                        }
                                    }
                                }

                                if (strtolower($status->data->status) != strtolower($value_order->status)) {
                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value_order->merchantid ?? $value_order->merchantid_order, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                }
                            } else {
                                if ($status != null) {
                                    log_message_user('error', '[MEDANPEDIA CRON TRANSACTION PRODUCT] Response: ' . json_encode($status), $value_user->id);
                                }
                            }
                        } else if ($value_order->vendor == 'IrvanKede' || $value_order->vendor_product == 'IrvanKede') {
                            $irvankede = new IrvanKede(stringEncryption('decrypt', $smm->usercode), stringEncryption('decrypt', $smm->apikey));
                            $status = $irvankede->status($value_order->servercode);

                            if (isset($status->status) && $status->status) {
                                $update = array();
                                $update['status'] = $status->data->status;
                                $update['startcount'] = $status->data->start_count;
                                $update['remain'] = $status->data->remains;

                                if ($value_order->queuetransaction == 1) {
                                    $update['queuetransaction'] = 0;
                                }

                                $this->trorder->update(array(
                                    'id' => $value_order->id
                                ), $update);

                                if (strtolower($status->data->status) == 'error' && $value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                        } else {
                                            $inserthistorybalance['nominal'] = $value_order->price;
                                        }

                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $update = array();

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $update['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                        } else {
                                            $update['balance'] = $currentbalance + $value_order->price;
                                        }

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $update);
                                    }
                                } else if (strtolower($status->data->status) == 'partial' && $value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $remains = $status->data->remains;
                                        $qty = $value_order->qty;
                                        $price = $value_order->price;
                                        $peritem = $price / $qty;

                                        $fee = round($value_order->fee ?? 0);
                                        if ($fee > 0) {
                                            $feeitem = $fee / $qty;
                                            $feerefund = (int)($remains * $feeitem);
                                        } else {
                                            $feerefund = 0;
                                        }

                                        $refund = (int)($remains * $peritem);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';
                                        $inserthistorybalance['nominal'] = $refund - $feerefund;
                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $update = array();
                                        $update['balance'] = $currentbalance + $refund - $feerefund;

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $update);
                                    }
                                }

                                if (strtolower($status->data->status) != strtolower($value_order->status)) {
                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value_order->merchantid ?? $value_order->merchantid_order, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                }
                            } else {
                                if ($status != null) {
                                    log_message_user('error', '[IRVANKEDE CRON TRANSACTION PRODUCT] Response: ' . json_encode($status), $value_user->id);
                                }
                            }
                        } else if ($value_order->vendor == 'DailyPanel' || $value_order->vendor_product == 'DailyPanel') {
                            $dailypanel = new DailyPanel(stringEncryption('decrypt', $smm->apikey), stringEncryption('decrypt', $smm->secretkey));
                            $status = $dailypanel->status($value_order->servercode);

                            if (isset($status->success) && $status->success) {
                                $update = array();
                                $update['status'] = $status->msg->status;
                                $update['startcount'] = $status->msg->jumlah_awal;
                                $update['remain'] = $status->msg->jumlah_kurang;

                                if ($value_order->queuetransaction == 1) {
                                    $update['queuetransaction'] = 0;
                                }

                                $this->trorder->update(array(
                                    'id' => $value_order->id
                                ), $update);

                                if (strtolower($status->msg->status) == 'error' && $value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                        } else {
                                            $inserthistorybalance['nominal'] = $value_order->price;
                                        }

                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $update = array();

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $update['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                        } else {
                                            $update['balance'] = $currentbalance + $value_order->price;
                                        }

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $update);
                                    }
                                } else if (strtolower($status->msg->status) == 'partial' && $value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $remains = $status->msg->jumlah_kurang;
                                        $qty = $value_order->qty;
                                        $price = $value_order->price;
                                        $peritem = $price / $qty;

                                        $fee = round($value_order->fee ?? 0);
                                        if ($fee > 0) {
                                            $feeitem = $fee / $qty;
                                            $feerefund = (int)($remains * $feeitem);
                                        } else {
                                            $feerefund = 0;
                                        }

                                        $refund = (int)($remains * $peritem);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';
                                        $inserthistorybalance['nominal'] = $refund - $feerefund;
                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $update = array();
                                        $update['balance'] = $currentbalance + $refund - $feerefund;

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $update);
                                    }
                                }

                                if (strtolower($status->msg->status) != strtolower($value_order->status)) {
                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value_order->merchantid ?? $value_order->merchantid_order, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                }
                            } else {
                                if ($status != null) {
                                    log_message_user('error', '[DAILYPANEL CRON TRANSACTION PRODUCT] Response: ' . json_encode($status), $value_user->id);
                                }
                            }
                        } else if ($value_order->vendor == 'WStore' || $value_order->vendor_product == 'WStore') {
                            $wstore = new WStore(stringEncryption('decrypt', $smm->usercode), stringEncryption('decrypt', $smm->apikey), stringEncryption('decrypt', $smm->secretkey));
                            $status = $wstore->status($value_order->servercode);

                            if (isset($status->response) && $status->response) {
                                $update = array();
                                $update['status'] = $status->data->status;
                                $update['startcount'] = $status->data->start_count;
                                $update['remain'] = $status->data->remains;

                                if ($value_order->queuetransaction == 1) {
                                    $update['queuetransaction'] = 0;
                                }

                                $this->trorder->update(array(
                                    'id' => $value_order->id
                                ), $update);

                                if (strtolower($status->data->status) == 'error' && $value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                        } else {
                                            $inserthistorybalance['nominal'] = $value_order->price;
                                        }

                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $update = array();

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $update['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                        } else {
                                            $update['balance'] = $currentbalance + $value_order->price;
                                        }

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $update);
                                    }
                                } else if (strtolower($status->data->status) == 'partial' && $value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $remains = $status->data->remains;
                                        $qty = $value_order->qty;
                                        $price = $value_order->price;
                                        $peritem = $price / $qty;

                                        $fee = round($value_order->fee ?? 0);
                                        if ($fee > 0) {
                                            $feeitem = $fee / $qty;
                                            $feerefund = (int)($remains * $feeitem);
                                        } else {
                                            $feerefund = 0;
                                        }

                                        $refund = (int)($remains * $peritem);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';
                                        $inserthistorybalance['nominal'] = $refund - $feerefund;
                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $update = array();
                                        $update['balance'] = $currentbalance + $refund - $feerefund;

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $update);
                                    }
                                }

                                if (strtolower($status->data->status) != strtolower($value_order->status)) {
                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value_order->merchantid ?? $value_order->merchantid_order, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                }
                            } else {
                                if ($status != null) {
                                    log_message_user('error', '[WSTORE CRON TRANSACTION PRODUCT] Response: ' . json_encode($status), $value_user->id);
                                }
                            }
                        } else if ($value_order->vendor == 'UNDRCTRL' || $value_order->vendor_product == 'UNDRCTRL') {
                            $undrctrl = new UNDRCTRL(stringEncryption('decrypt', $smm->apikey), stringEncryption('decrypt', $smm->public_key));
                            $status = $undrctrl->status($value_order->servercode);

                            if (isset($status->status) && $status->status) {
                                $update = array();
                                $update['status'] = $status->status;
                                $update['startcount'] = $status->start_count;
                                $update['remain'] = $status->remains;

                                if ($value_order->queuetransaction == 1) {
                                    $update['queuetransaction'] = 0;
                                }

                                $this->trorder->update(array(
                                    'id' => $value_order->id
                                ), $update);

                                if ((strtolower($status->status) == 'error' || strtolower($status->status) == 'canceled') && $value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                        } else {
                                            $inserthistorybalance['nominal'] = $value_order->price;
                                        }

                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $update = array();

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $update['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                        } else {
                                            $update['balance'] = $currentbalance + $value_order->price;
                                        }

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $update);
                                    }
                                } else if (strtolower($status->status) == 'partial' && $value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $remains = $status->remains;
                                        $qty = $value_order->qty;
                                        $price = $value_order->price;
                                        $peritem = $price / $qty;

                                        $fee = round($value_order->fee ?? 0);
                                        if ($fee > 0) {
                                            $feeitem = $fee / $qty;
                                            $feerefund = (int)($remains * $feeitem);
                                        } else {
                                            $feerefund = 0;
                                        }

                                        $refund = (int)($remains * $peritem);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';
                                        $inserthistorybalance['nominal'] = $refund - $feerefund;
                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $update = array();
                                        $update['balance'] = $currentbalance + $refund - $feerefund;

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $update);
                                    }
                                }

                                if (strtolower($status->status) != strtolower($value_order->status)) {
                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value_order->merchantid ?? $value_order->merchantid_order, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                }
                            } else {
                                if ($status != null) {
                                    log_message_user('error', '[UNDRCTRL CRON TRANSACTION PRODUCT] Response: ' . json_encode($status), $value_user->id);
                                }
                            }
                        } else if ($value_order->vendor == 'SosmedOnline' || $value_order->vendor_product == 'SosmedOnline') {
                            $sosmedonline = new SosmedOnline(stringEncryption('decrypt', $smm->usercode), stringEncryption('decrypt', $smm->apikey));
                            $status = $sosmedonline->status($value_order->servercode);

                            if (isset($status->status) && $status->status) {
                                $update = array();
                                $update['status'] = $status->data->status;
                                $update['startcount'] = $status->data->start_count;
                                $update['remain'] = $status->data->remains;

                                if ($value_order->queuetransaction == 1) {
                                    $update['queuetransaction'] = 0;
                                }

                                $this->trorder->update(array(
                                    'id' => $value_order->id
                                ), $update);

                                if (strtolower($status->data->status) == 'error' && $value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                        } else {
                                            $inserthistorybalance['nominal'] = $value_order->price;
                                        }

                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $update = array();

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $update['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                        } else {
                                            $update['balance'] = $currentbalance + $value_order->price;
                                        }

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $update);
                                    }
                                } else if (strtolower($status->data->status) == 'partial' && $value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $remains = $status->data->remains;
                                        $qty = $value_order->qty;
                                        $price = $value_order->price;
                                        $peritem = $price / $qty;

                                        $fee = round($value_order->fee ?? 0);
                                        if ($fee > 0) {
                                            $feeitem = $fee / $qty;
                                            $feerefund = (int)($remains * $feeitem);
                                        } else {
                                            $feerefund = 0;
                                        }

                                        $refund = (int)($remains * $peritem);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';
                                        $inserthistorybalance['nominal'] = $refund - $feerefund;
                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $update = array();
                                        $update['balance'] = $currentbalance + $refund - $feerefund;

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $update);
                                    }
                                }

                                if (strtolower($status->data->status) != strtolower($value_order->status)) {
                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value_order->merchantid ?? $value_order->merchantid_order, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                }
                            } else {
                                if ($status != null) {
                                    log_message_user('error', '[SOSMEDONLINE CRON TRANSACTION PRODUCT] Response: ' . json_encode($status), $value_user->id);
                                }
                            }
                        } else if ($value_order->vendor == 'SosmedOnlineVIP' || $value_order->vendor_product == 'SosmedOnlineVIP') {
                            $sosmedonlinevip = new SosmedOnlineVIP(stringEncryption('decrypt', $smm->usercode), stringEncryption('decrypt', $smm->apikey));
                            $status = $sosmedonlinevip->status($value_order->servercode);

                            if (isset($status->status) && $status->status) {
                                $update = array();
                                $update['status'] = $status->data->status;
                                $update['startcount'] = $status->data->start_count;
                                $update['remain'] = $status->data->remains;

                                if ($value_order->queuetransaction == 1) {
                                    $update['queuetransaction'] = 0;
                                }

                                $this->trorder->update(array(
                                    'id' => $value_order->id
                                ), $update);

                                if (strtolower($status->data->status) == 'error' && $value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                        } else {
                                            $inserthistorybalance['nominal'] = $value_order->price;
                                        }

                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $update = array();

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $update['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                        } else {
                                            $update['balance'] = $currentbalance + $value_order->price;
                                        }

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $update);
                                    }
                                } else if (strtolower($status->data->status) == 'partial' && $value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $remains = $status->data->remains;
                                        $qty = $value_order->qty;
                                        $price = $value_order->price;
                                        $peritem = $price / $qty;

                                        $fee = round($value_order->fee ?? 0);
                                        if ($fee > 0) {
                                            $feeitem = $fee / $qty;
                                            $feerefund = (int)($remains * $feeitem);
                                        } else {
                                            $feerefund = 0;
                                        }

                                        $refund = (int)($remains * $peritem);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';
                                        $inserthistorybalance['nominal'] = $refund - $feerefund;
                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $update = array();
                                        $update['balance'] = $currentbalance + $refund - $feerefund;

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $update);
                                    }
                                }

                                if (strtolower($status->data->status) != strtolower($value_order->status)) {
                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value_order->merchantid ?? $value_order->merchantid_order, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                }
                            } else {
                                if ($status != null) {
                                    log_message_user('error', '[SOSMEDONLINEVIP CRON TRANSACTION PRODUCT] Response: ' . json_encode($status), $value_user->id);
                                }
                            }
                        } else if ($value_order->vendor == 'DjuraganSosmed' || $value_order->vendor_product == 'DjuraganSosmed') {
                            $djuragansosmed = new DjuraganSosmed(stringEncryption('decrypt', $smm->apikey));
                            $status = $djuragansosmed->status($value_order->servercode);

                            if (isset($status->status) && $status->status) {
                                $update = array();
                                $update['status'] = $status->status;
                                $update['startcount'] = $status->start_count;
                                $update['remain'] = $status->remains;

                                if ($value_order->queuetransaction == 1) {
                                    $update['queuetransaction'] = 0;
                                }

                                $this->trorder->update(array(
                                    'id' => $value_order->id
                                ), $update);

                                if ((strtolower($status->status) == 'error' || strtolower($status->status) == 'canceled') && $value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                        } else {
                                            $inserthistorybalance['nominal'] = $value_order->price;
                                        }

                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $update = array();

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $update['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                        } else {
                                            $update['balance'] = $currentbalance + $value_order->price;
                                        }

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $update);
                                    }
                                } else if (strtolower($status->status) == 'partial' && $value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $remains = $status->remains;
                                        $qty = $value_order->qty;
                                        $price = $value_order->price;
                                        $peritem = $price / $qty;

                                        $fee = round($value_order->fee ?? 0);
                                        if ($fee > 0) {
                                            $feeitem = $fee / $qty;
                                            $feerefund = (int)($remains * $feeitem);
                                        } else {
                                            $feerefund = 0;
                                        }

                                        $refund = (int)($remains * $peritem);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';
                                        $inserthistorybalance['nominal'] = $refund - $feerefund;
                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $update = array();
                                        $update['balance'] = $currentbalance + $refund - $feerefund;

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $update);
                                    }
                                }

                                if (strtolower($status->status) != strtolower($value_order->status)) {
                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value_order->merchantid ?? $value_order->merchantid_order, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                }
                            } else {
                                if ($status != null) {
                                    log_message_user('error', '[DJURAGANSOSMED CRON TRANSACTION PRODUCT] Response: ' . json_encode($status), $value_user->id);
                                }
                            }
                        } else if ($value_order->vendor == 'SMMRaja' || $value_order->vendor_product == 'SMMRaja') {
                            $smmraja = new SMMRaja(stringEncryption('decrypt', $smm->apikey));
                            $status = $smmraja->status($value_order->servercode);

                            if (isset($status->status)) {
                                $update = array();
                                $update['status'] = $status->status;
                                $update['startcount'] = $status->start_count;
                                $update['remain'] = $status->remains;

                                if ($value_order->queuetransaction == 1) {
                                    $update['queuetransaction'] = 0;
                                }

                                $this->trorder->update(array(
                                    'id' => $value_order->id
                                ), $update);

                                if ((strtolower($status->status) == 'canceled' || strtolower($status->status) == 'refunded') && $value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                        } else {
                                            $inserthistorybalance['nominal'] = $value_order->price;
                                        }

                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $update = array();

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $update['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                        } else {
                                            $update['balance'] = $currentbalance + $value_order->price;
                                        }

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $update);
                                    }
                                } else if (strtolower($status->status) == 'partial' && $value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $remains = $status->remains;
                                        $qty = $value_order->qty;
                                        $price = $value_order->price;
                                        $peritem = $price / $qty;

                                        $fee = round($value_order->fee ?? 0);
                                        if ($fee > 0) {
                                            $feeitem = $fee / $qty;
                                            $feerefund = (int)($remains * $feeitem);
                                        } else {
                                            $feerefund = 0;
                                        }

                                        $refund = (int)($remains * $peritem);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';
                                        $inserthistorybalance['nominal'] = $refund - $feerefund;
                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $update = array();
                                        $update['balance'] = $currentbalance + $refund - $feerefund;

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $update);
                                    }
                                }

                                if (strtolower($status->status) != strtolower($value_order->status)) {
                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value_order->merchantid ?? $value_order->merchantid_order, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                }
                            } else {
                                if ($status != null) {
                                    log_message_user('error', '[SMMRAJA CRON TRANSACTION PRODUCT] Response: ' . json_encode($status), $value_user->id);
                                }
                            }
                        } else if ($value_order->vendor == 'SMMIllusion' || $value_order->vendor_product == 'SMMIllusion') {
                            $smmillusion = new SMMIllusion(stringEncryption('decrypt', $smm->apikey));
                            $status = $smmillusion->status($value_order->servercode);

                            if (isset($status->status)) {
                                $update = array();
                                $update['status'] = $status->status;
                                $update['startcount'] = $status->start_count;
                                $update['remain'] = $status->remains;

                                if ($value_order->queuetransaction == 1) {
                                    $update['queuetransaction'] = 0;
                                }

                                $this->trorder->update(array(
                                    'id' => $value_order->id
                                ), $update);

                                if (strtolower($status->status) == 'error' && $value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                        } else {
                                            $inserthistorybalance['nominal'] = $value_order->price;
                                        }

                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $update = array();

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $update['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                        } else {
                                            $update['balance'] = $currentbalance + $value_order->price;
                                        }

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $update);
                                    }
                                } else if (strtolower($status->status) == 'partial' && $value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $remains = $status->remains;
                                        $qty = $value_order->qty;
                                        $price = $value_order->price;
                                        $peritem = $price / $qty;

                                        $fee = round($value_order->fee ?? 0);
                                        if ($fee > 0) {
                                            $feeitem = $fee / $qty;
                                            $feerefund = (int)($remains * $feeitem);
                                        } else {
                                            $feerefund = 0;
                                        }

                                        $refund = (int)($remains * $peritem);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';
                                        $inserthistorybalance['nominal'] = $refund - $feerefund;
                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $update = array();
                                        $update['balance'] = $currentbalance + $refund - $feerefund;

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $update);
                                    }
                                }

                                if (strtolower($status->status) != strtolower($value_order->status)) {
                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value_order->merchantid ?? $value_order->merchantid_order, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                }
                            } else {
                                if ($status != null) {
                                    log_message_user('error', '[SMMILLUSION CRON TRANSACTION PRODUCT] Response: ' . json_encode($status), $value_user->id);
                                }
                            }
                        } else if ($value_order->vendor == 'V1Pedia' || $value_order->vendor_product == 'V1Pedia') {
                            $v1pedia = new V1Pedia(stringEncryption('decrypt', $smm->usercode), stringEncryption('decrypt', $smm->apikey), stringEncryption('decrypt', $smm->secretkey));
                            $status = $v1pedia->status($value_order->servercode);

                            if (isset($status->response) && $status->response) {
                                $update = array();
                                $update['status'] = $status->data->status;
                                $update['startcount'] = $status->data->start_count;
                                $update['remain'] = $status->data->remains;

                                if ($value_order->queuetransaction == 1) {
                                    $update['queuetransaction'] = 0;
                                }

                                $this->trorder->update(array(
                                    'id' => $value_order->id
                                ), $update);

                                if (strtolower($status->data->status) == 'error' && $value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                        } else {
                                            $inserthistorybalance['nominal'] = $value_order->price;
                                        }

                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $update = array();

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $update['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                        } else {
                                            $update['balance'] = $currentbalance + $value_order->price;
                                        }

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $update);
                                    }
                                } else if (strtolower($status->data->status) == 'partial' && $value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $remains = $status->data->remains;
                                        $qty = $value_order->qty;
                                        $price = $value_order->price;
                                        $peritem = $price / $qty;

                                        $fee = round($value_order->fee ?? 0);
                                        if ($fee > 0) {
                                            $feeitem = $fee / $qty;
                                            $feerefund = (int)($remains * $feeitem);
                                        } else {
                                            $feerefund = 0;
                                        }

                                        $refund = (int)($remains * $peritem);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';
                                        $inserthistorybalance['nominal'] = $refund - $feerefund;
                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $update = array();
                                        $update['balance'] = $currentbalance + $refund - $feerefund;

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $update);
                                    }
                                }

                                if (strtolower($status->data->status) != strtolower($value_order->status)) {
                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value_order->merchantid ?? $value_order->merchantid_order, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                }
                            } else {
                                if ($status != null) {
                                    log_message_user('error', '[V1PEDIA CRON TRANSACTION PRODUCT] Response: ' . json_encode($status), $value_user->id);
                                }
                            }
                        }
                    } else if (($value_order->category_apikey == 'PPOB' || $value_order->category_apikey_product == 'PPOB') && ($value_order->subcategory_apikey == 'PASCABAYAR' || $value_order->subcategory_apikey_product == 'PASCABAYAR')) {
                        if ($ppob == null) {
                            $this->db->trans_rollback();

                            continue;
                        }

                        if ($ppob->vendor == 'Digiflazz' && $ppob->priority_callback == 1) {
                            $this->db->trans_rollback();

                            continue;
                        }

                        if ($value_order->vendor == 'Digiflazz' || $value_order->vendor_product == 'Digiflazz') {
                            $digiflazz = new Digiflazz(stringEncryption('decrypt', $ppob->usercode), stringEncryption('decrypt', $ppob->apikey));
                            $status = $digiflazz->status_pasca($value_order->code ?? $value_order->productcode, $value_order->target, $value_order->servercode);
                            $result = json_decode($status);

                            if (isset($result->data->status) && $result->data->status) {
                                $update = array();
                                $update['note'] = $result->data->message;
                                $update['sn'] = $result->data->sn ?? '';
                                $update['status'] = strtolower($result->data->status);
                                $update['jsonresponse'] = json_encode($result);
                                $update['updateddate'] = getCurrentDate();

                                if ($value_order->queuetransaction == 1) {
                                    $update['queuetransaction'] = 0;
                                }

                                $this->trorder->update(array(
                                    'id' => $value_order->id
                                ), $update);

                                if (strtolower($result->data->status) == 'gagal' && $value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                        } else {
                                            $inserthistorybalance['nominal'] = $value_order->price;
                                        }

                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $updateUser = array();

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $updateUser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                        } else {
                                            $updateUser['balance'] = $currentbalance + $value_order->price;
                                        }

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $updateUser);
                                    }
                                }

                                if (strtolower($result->data->status) != strtolower($value_order->status)) {
                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value_order->merchantid ?? $value_order->merchantid_order, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                }
                            }
                        }
                    }

                    if ($this->db->trans_status() === FALSE) {
                        $this->db->trans_rollback();
                    }

                    $this->db->trans_commit();
                }
            }
        } catch (Exception $ex) {
            $this->db->trans_rollback();
        }
    }

    public function guest_product_transaction_queue_expired()
    {
        $result = $this->trorder->result(array(
            'a.servercode' => null,
            'a.status_payment' => 'pending',
            'a.status' => 'pending'
        ));

        foreach ($result as $key => $value) {
            $one_days = date('Y-m-d H:i:s', strtotime("$value->createddate +1 days"));

            if (getCurrentDate() >= $one_days) {
                $update = array();
                $update['status'] = 'gagal';
                $update['status_payment'] = 'failed';

                $this->trorder->update(array(
                    'id' => $value->id
                ), $update);

                pullTransaction($value->userid ?? $value->clientip);
            }
        }
    }

    public function member_topup_transaction_expired()
    {
        $result = $this->deposits->select('a.id, a.createddate, a.phonenumber, a.merchantid')
            ->join('msusers b', 'b.id = a.userid')
            ->result(array(
                'a.status' => 'Pending'
            ));

        foreach ($result as $key => $value) {
            $one_days = date('Y-m-d H:i:s', strtotime("$value->createddate +1 days"));

            if (getCurrentDate() >= $one_days) {
                $update = array();
                $update['status'] = 'Failed';

                $this->deposits->update(array(
                    'id' => $value->id
                ), $update);

                if ($value->merchantid != null) {
                    $this->send_notification($value->id, $value->merchantid, $value->phonenumber, 'deposit');
                }
            }
        }
    }

    public function merchant_balance()
    {
        $apikeys = $this->apikeys->select('a.id, a.vendor, a.usercode, a.apikey, a.secretkey, a.currency_rate')
            ->join('msusers b', 'b.id = a.userid')
            ->result(array(
                'b.licenseid !=' => null
            ));

        foreach ($apikeys as $key => $value) {
            if ($value->vendor == 'Digiflazz') {
                if (empty($value->usercode) || empty($value->apikey))  continue;

                $digiflazz = new Digiflazz(stringEncryption('decrypt', $value->usercode), stringEncryption('decrypt', $value->apikey));
                $saldo = $digiflazz->check_balance();

                if (isset($saldo->data->deposit)) {
                    $balance = $saldo->data->deposit;

                    $update = array();
                    $update['balance'] = $balance;

                    $this->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            } else if ($value->vendor == 'VIPayment') {
                if (empty($value->usercode) || empty($value->apikey))  continue;

                $vipayment = new VIPayment(stringEncryption('decrypt', $value->usercode), stringEncryption('decrypt', $value->apikey));
                $profile = $vipayment->profile();

                if (isset($profile->data->balance)) {
                    $balance = $profile->data->balance;

                    $update = array();
                    $update['balance'] = $balance;

                    $this->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            } else if ($value->vendor == 'BuzzerPanel') {
                if (empty($value->apikey) || empty($value->secretkey)) continue;

                $buzzerpanel = new BuzzerPanel(stringEncryption('decrypt', $value->apikey), stringEncryption('decrypt', $value->secretkey));
                $profile = $buzzerpanel->profile();

                if (isset($profile->data->balance)) {
                    $balance = $profile->data->balance;

                    $update = array();
                    $update['balance'] = $balance;

                    $this->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            } else if ($value->vendor == 'MedanPedia') {
                if (empty($value->usercode) || empty($value->apikey)) continue;

                $medanpedia = new MedanPedia(stringEncryption('decrypt', $value->usercode), stringEncryption('decrypt', $value->apikey));
                $profile = $medanpedia->profile();

                if (isset($profile->status) && $profile->status) {
                    $balance = $profile->data->balance;

                    $update = array();
                    $update['balance'] = $balance;

                    $this->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            } else if ($value->vendor == 'IrvanKede') {
                if (empty($value->usercode) || empty($value->apikey)) continue;

                $irvankede = new IrvanKede(stringEncryption('decrypt', $value->usercode), stringEncryption('decrypt', $value->apikey));
                $profile = $irvankede->profile();

                if (isset($profile->status) && $profile->status) {
                    $balance = $profile->data->balance;

                    $update = array();
                    $update['balance'] = $balance;

                    $this->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            } else if ($value->vendor == 'DailyPanel') {
                if (empty($value->apikey) || empty($value->secretkey)) continue;

                $dailypanel = new DailyPanel(stringEncryption('decrypt', $value->apikey), stringEncryption('decrypt', $value->secretkey));
                $profile = $dailypanel->profile();

                if (isset($profile->msg->balance)) {
                    $balance = $profile->msg->balance;

                    $update = array();
                    $update['balance'] = $balance;

                    $this->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            } else if ($value->vendor == 'WStore') {
                if (empty($value->usercode) || empty($value->apikey) || empty($value->secretkey)) continue;

                $wstore = new WStore(stringEncryption('decrypt', $value->usercode), stringEncryption('decrypt', $value->apikey), stringEncryption('decrypt', $value->secretkey));
                $profile = $wstore->profile();

                if (isset($profile->data->balance)) {
                    $balance = $profile->data->balance;

                    $update = array();
                    $update['balance'] = $balance;

                    $this->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            } else if ($value->vendor == 'UNDRCTRL') {
                if (empty($value->apikey)) continue;

                $undrctrl = new UNDRCTRL(stringEncryption('decrypt', $value->apikey));
                $profile = $undrctrl->profile();

                if (isset($profile->balance)) {
                    $balance = $profile->balance;

                    $update = array();
                    $update['balance'] = $balance;

                    $this->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            } else if ($value->vendor == 'SosmedOnline') {
                if (empty($value->apikey) || empty($value->usercode)) continue;

                $sosmedonline = new SosmedOnline(stringEncryption('decrypt', $value->usercode), stringEncryption('decrypt', $value->apikey));
                $profile = $sosmedonline->profile();

                if (isset($profile->status) && $profile->status) {
                    $balance = $profile->data->balance;

                    $update = array();
                    $update['balance'] = $balance;

                    $this->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            } else if ($value->vendor == 'SosmedOnlineVIP') {
                if (empty($value->apikey) || empty($value->usercode)) continue;

                $sosmedonlinevip = new SosmedOnlineVIP(stringEncryption('decrypt', $value->usercode), stringEncryption('decrypt', $value->apikey));
                $profile = $sosmedonlinevip->profile();

                if (isset($profile->status) && $profile->status) {
                    $balance = $profile->data->balance;

                    $update = array();
                    $update['balance'] = $balance;

                    $this->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            } else if ($value->vendor == 'DjuraganSosmed') {
                if (empty($value->apikey)) continue;

                $djuragansosmed = new DjuraganSosmed(stringEncryption('decrypt', $value->apikey));
                $profile = $djuragansosmed->profile();

                if (isset($profile->balance)) {
                    $balance = $profile->balance;

                    $update = array();
                    $update['balance'] = $balance;

                    $this->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            } else if ($value->vendor == 'SMMRaja') {
                if (empty($value->apikey)) continue;

                $smmraja = new SMMRaja(stringEncryption('decrypt', $value->apikey));
                $profile = $smmraja->profile();

                if (isset($profile->balance)) {
                    $balance = $profile->balance * $value->currency_rate;

                    $update = array();
                    $update['balance'] = $balance;

                    $this->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            } else if ($value->vendor == 'SMMIllusion') {
                if (empty($value->apikey)) continue;

                $smmillusion = new SMMIllusion(stringEncryption('decrypt', $value->apikey));
                $profile = $smmillusion->profile();

                if (isset($profile->balance)) {
                    $balance = $profile->balance * $value->currency_rate;

                    $update = array();
                    $update['balance'] = $balance;

                    $this->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            } else if ($value->vendor == 'V1Pedia') {
                if (empty($value->usercode) || empty($value->apikey) || empty($value->secretkey)) continue;

                $v1pedia = new V1Pedia(stringEncryption('decrypt', $value->usercode), stringEncryption('decrypt', $value->apikey), stringEncryption('decrypt', $value->secretkey));
                $profile = $v1pedia->profile();

                if (isset($profile->data->balance)) {
                    $balance = $profile->data->balance;

                    $update = array();
                    $update['balance'] = $balance;

                    $this->apikeys->update(array(
                        'id' => $value->id
                    ), $update);
                }
            }
        }
    }

    public function multivendor_transaction_queue()
    {
        try {
            $users = $this->msusers->select('a.*, b.id AS vendorid, b.default_config, b.balance')
                ->join('msvendor b', 'b.userid = a.id')
                ->result(array(
                    'a.merchantid' => null,
                    'a.licenseid !=' => null,
                    'a.expireddate >' => getCurrentDate(),
                    'a.multivendor' => 1
                ));

            foreach ($users as $key => $value) {
                $whereorder = array(
                    "(b.merchantid = '$value->id' OR a.merchantid_order = '$value->id') =" => true,
                    "(LOWER(a.status) = 'pending') =" => true,
                    'a.queuetransaction' => 1,
                    'a.status_payment' => 'sukses',
                    'a.vendorid' => $value->vendorid,
                    'a.servercode' => null,
                );

                $transaction_queue = $this->trorder->select('a.*, c.brand, c.type AS producttype, c.category, b.phonenumber, c.vendorprice')
                    ->join('msusers b', 'b.id = a.userid', 'LEFT')
                    ->join('msproduct c', 'c.id = a.serviceid', 'LEFT')
                    ->result($whereorder);

                if (count($transaction_queue) == 0) continue;

                $vendordetail = $this->msvendordetail->get(array(
                    'vendorid' => $value->vendorid,
                    'apitype' => 'Order'
                ));

                if ($vendordetail->num_rows() == 0) continue;

                $vendor_order = $vendordetail->row();

                $response_indicator = json_decode($vendor_order->response_indicator);
                $response_setting = json_decode($vendor_order->response_setting);

                $dynamicvendor = new DynamicVendor($value->vendorid, json_decode($value->default_config, true));

                foreach ($transaction_queue as $key_order => $value_order) {
                    $this->db->trans_begin();

                    $one_days = date('Y-m-d H:i:s', strtotime("$value_order->createddate +1 days"));

                    if (getCurrentDate() >= $one_days) {
                        if ($value_order->userid != null) {
                            $check_history_balance = $this->historybalance->total(array(
                                'userid' => $value_order->userid,
                                'type' => 'IN',
                                'orderid' => $value_order->id
                            ));

                            if ($check_history_balance == 0) {
                                $currentbalance = getCurrentBalance($value_order->userid, true);

                                $inserthistorybalance = array();
                                $inserthistorybalance['userid'] = $value_order->userid;
                                $inserthistorybalance['type'] = 'IN';

                                if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                    $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                } else {
                                    $inserthistorybalance['nominal'] = $value_order->price;
                                }

                                $inserthistorybalance['currentbalance'] = $currentbalance;
                                $inserthistorybalance['orderid'] = $value_order->id;
                                $inserthistorybalance['createdby'] = $value_order->userid;
                                $inserthistorybalance['createddate'] = getCurrentDate();

                                $this->historybalance->insert($inserthistorybalance);

                                $update = array();

                                if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                    $update['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                } else {
                                    $update['balance'] = $currentbalance + $value_order->price;
                                }

                                $this->msusers->update(array(
                                    'id' => $value_order->userid
                                ), $update);
                            }
                        }

                        $update = array();
                        $update['status'] = 'gagal';
                        $update['queuetransaction'] = 0;

                        $this->trorder->update(array(
                            'id' => $value_order->id
                        ), $update);

                        pullTransaction($value_order->userid ?? $value_order->clientip);
                        $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);
                    } else {
                        if ($value_order->type == 'SMM') {
                            $vendorprice = $value_order->vendorprice;
                            $priceperitem = $vendorprice / 1000;

                            $vendorprice_total = $priceperitem * $value_order->qty;
                        } else {
                            $vendorprice_total = $value_order->vendorprice;
                        }

                        if ($value->balance < $vendorprice_total) {
                            $this->db->trans_rollback();
                            continue;
                        }

                        $order = $dynamicvendor->order($value_order->id);

                        if (
                            ($response_indicator->index == null && isset($order[$response_indicator->key]) && (
                                $response_indicator->datatype == 'string' && $order[$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'number' && $order[$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'boolean' && $order[$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'exists' && isset($order[$response_indicator->key])
                            ))
                            || ($response_indicator->index != null && isset($order[$response_indicator->index][$response_indicator->key]) && (
                                $response_indicator->datatype == 'string' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'number' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'boolean' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'exists' && isset($order[$response_indicator->index][$response_indicator->key])
                            ))
                        ) {
                            $var_referenceid = $response_setting->referenceid ?? null;
                            $var_price = $response_setting->price ?? null;
                            $var_status = $response_setting->status ?? null;
                            $var_note = $response_setting->note ?? null;
                            $var_sn = $response_setting->sn ?? null;
                            $var_errorrefund = $response_setting->errorrefund;

                            $exploding_errorefund = explode('[#]', strtolower($var_errorrefund ?? ''));

                            if ($response_setting->index != null) {
                                $order = $order[$response_setting->index] ?? null;
                            }

                            $referenceid = $var_referenceid != null ? ($order[$var_referenceid] ?? null) : null;
                            $price = $var_price != null ? ($order[$var_price] ?? null) : null;
                            $status = $var_status != null ? ($order[$var_status] ?? null) : null;
                            $note = $var_note != null ? ($order[$var_note] ?? null) : null;
                            $sn = $var_sn != null ? ($order[$var_sn] ?? null) : null;

                            if ($status != null) {
                                if (in_array($status, $exploding_errorefund)) {
                                    log_message_user('error', '[MULTIVENDOR ORDER CRON QUEUE TRANSACTION] Failed to parsing error refund, response: ' . json_encode($order), $this->merchant->id);

                                    if ($value_order->userid != null) {
                                        $check_history_balance = $this->historybalance->total(array(
                                            'userid' => $value_order->userid,
                                            'type' => 'IN',
                                            'orderid' => $value_order->id
                                        ));

                                        if ($check_history_balance == 0) {
                                            $currentbalance = getCurrentBalance($value_order->userid, true);

                                            $inserthistorybalance = array();
                                            $inserthistorybalance['userid'] = $value_order->userid;
                                            $inserthistorybalance['type'] = 'IN';

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                            } else {
                                                $inserthistorybalance['nominal'] = $value_order->price;
                                            }

                                            $inserthistorybalance['currentbalance'] = $currentbalance;
                                            $inserthistorybalance['orderid'] = $value_order->id;
                                            $inserthistorybalance['createdby'] = $value_order->userid;
                                            $inserthistorybalance['createddate'] = getCurrentDate();

                                            $this->historybalance->insert($inserthistorybalance);

                                            $update = array();

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $update['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                            } else {
                                                $update['balance'] = $currentbalance + $value_order->price;
                                            }

                                            $this->msusers->update(array(
                                                'id' => $value_order->userid
                                            ), $update);
                                        }
                                    }

                                    $update = array();
                                    $update['status'] = 'gagal';
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);
                                }
                            } else {
                                if ($var_status == null) {
                                    $status = 'pending';
                                } else if ($var_status != null && $status == null) {
                                    log_message_user('error', '[MULTIVENDOR ORDER CRON QUEUE TRANSACTION] Failed to parsing status, response: ' . json_encode($order), $this->merchant->id);

                                    if ($value_order->userid != null) {
                                        $check_history_balance = $this->historybalance->total(array(
                                            'userid' => $value_order->userid,
                                            'type' => 'IN',
                                            'orderid' => $value_order->id
                                        ));

                                        if ($check_history_balance == 0) {
                                            $currentbalance = getCurrentBalance($value_order->userid, true);

                                            $inserthistorybalance = array();
                                            $inserthistorybalance['userid'] = $value_order->userid;
                                            $inserthistorybalance['type'] = 'IN';

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                            } else {
                                                $inserthistorybalance['nominal'] = $value_order->price;
                                            }

                                            $inserthistorybalance['currentbalance'] = $currentbalance;
                                            $inserthistorybalance['orderid'] = $value_order->id;
                                            $inserthistorybalance['createdby'] = $value_order->userid;
                                            $inserthistorybalance['createddate'] = getCurrentDate();

                                            $this->historybalance->insert($inserthistorybalance);

                                            $update = array();

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $update['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                            } else {
                                                $update['balance'] = $currentbalance + $value_order->price;
                                            }

                                            $this->msusers->update(array(
                                                'id' => $value_order->userid
                                            ), $update);
                                        }
                                    }

                                    $update = array();
                                    $update['status'] = 'gagal';
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);
                                }
                            }

                            $update = array();
                            $update['jsonresponse'] = json_encode($order);
                            $update['queuetransaction'] = 0;

                            if ($referenceid != null) {
                                $update['servercode'] = $referenceid;
                            }

                            if ($price != null) {
                                $update['price'] = $price;
                            }

                            if ($status != null) {
                                $update['status'] = $status;
                            }

                            if ($note != null) {
                                $update['note'] = $note;
                            }

                            if ($sn != null) {
                                $update['sn'] = $sn;
                            }

                            $this->trorder->update(array(
                                'id' => $value_order->id,
                            ), $update);

                            pullTransaction($value_order->userid ?? $value_order->clientip);
                            $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);
                        } else {
                            if ($value_order->userid != null) {
                                $check_history_balance = $this->historybalance->total(array(
                                    'userid' => $value_order->userid,
                                    'type' => 'IN',
                                    'orderid' => $value_order->id
                                ));

                                if ($check_history_balance == 0) {
                                    $currentbalance = getCurrentBalance($value_order->userid, true);

                                    $inserthistorybalance = array();
                                    $inserthistorybalance['userid'] = $value_order->userid;
                                    $inserthistorybalance['type'] = 'IN';

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                    } else {
                                        $inserthistorybalance['nominal'] = $value_order->price;
                                    }

                                    $inserthistorybalance['currentbalance'] = $currentbalance;
                                    $inserthistorybalance['orderid'] = $value_order->id;
                                    $inserthistorybalance['createdby'] = $value_order->userid;
                                    $inserthistorybalance['createddate'] = getCurrentDate();

                                    $this->historybalance->insert($inserthistorybalance);

                                    $update = array();

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $update['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                    } else {
                                        $update['balance'] = $currentbalance + $value_order->price;
                                    }

                                    $this->msusers->update(array(
                                        'id' => $value_order->userid
                                    ), $update);
                                }
                            }

                            $update = array();
                            $update['status'] = 'gagal';
                            $update['queuetransaction'] = 0;

                            $this->trorder->update(array(
                                'id' => $value_order->id
                            ), $update);

                            pullTransaction($value_order->userid ?? $value_order->clientip);
                            $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);

                            if ($order != null) {
                                log_message_user('error', '[MULTIVENDOR ORDER CRON QUEUE TRANSACTION] Failed to parse response, response: ' . json_encode($order), $value->id);
                            }
                        }
                    }

                    if ($this->db->trans_status() === FALSE) {
                        $this->db->trans_rollback();
                    }

                    $this->db->trans_commit();
                }
            }
        } catch (Exception $ex) {
            $this->db->trans_rollback();
        }
    }

    public function member_product_transaction_queue()
    {
        try {
            $users = $this->msusers->select('a.id')
                ->result(array(
                    'a.merchantid' => null,
                    'a.licenseid !=' => null,
                    'a.expireddate >' => getCurrentDate(),
                    "(a.multivendor IS NULL OR a.multivendor = '0') =" => true
                ));

            foreach ($users as $key => $value) {
                $apikeysppob = getCurrentAPIKeys('PPOB', $value->id);
                $apikeyssmm = getCurrentAPIKeys('SMM', $value->id);

                if ($apikeysppob == null && $apikeyssmm == null) continue;

                $whereorder = array(
                    "(b.merchantid = '$value->id' OR a.merchantid_order = '$value->id') =" => true,
                    "(LOWER(a.status) = 'pending') =" => true,
                    'a.queuetransaction' => 1,
                    'a.status_payment' => 'sukses',
                    'a.vendorid' => null,
                    'a.servercode' => null,
                );

                if ($apikeysppob != null && $apikeyssmm != null) {
                    $whereorder["(a.vendor = '$apikeysppob->vendor' OR a.vendor = '$apikeyssmm->vendor') ="] = true;
                } else if ($apikeysppob != null && $apikeyssmm == null) {
                    $whereorder['a.vendor'] = $apikeysppob->vendor;
                } else if ($apikeysppob == null && $apikeyssmm != null) {
                    $whereorder['a.vendor'] = $apikeyssmm->vendor;
                }

                $transaction_queue = $this->trorder->select('a.*, c.brand, c.type AS producttype, c.category, b.phonenumber, c.vendorprice')
                    ->join('msusers b', 'b.id = a.userid', 'LEFT')
                    ->join('msproduct c', 'c.id = a.serviceid', 'LEFT')
                    ->result($whereorder);

                if (count($transaction_queue) == 0) continue;

                if ($apikeysppob != null) {
                    if ($apikeysppob->vendor == 'Digiflazz') {
                        $ppobobject = new Digiflazz(stringEncryption('decrypt', $apikeysppob->usercode), stringEncryption('decrypt', $apikeysppob->apikey));
                    } else if ($apikeysppob->vendor == 'VIPayment') {
                        $ppobobject = new VIPayment(stringEncryption('decrypt', $apikeysppob->usercode), stringEncryption('decrypt', $apikeysppob->apikey));
                    }
                }

                if ($apikeyssmm != null) {
                    if ($apikeyssmm->vendor == 'BuzzerPanel') {
                        $smmobject = new BuzzerPanel(stringEncryption('decrypt', $apikeyssmm->apikey), stringEncryption('decrypt', $apikeyssmm->secretkey));
                    } else if ($apikeyssmm->vendor == 'MedanPedia') {
                        $smmobject = new MedanPedia(stringEncryption('decrypt', $apikeyssmm->usercode), stringEncryption('decrypt', $apikeyssmm->apikey));
                    } else if ($apikeyssmm->vendor == 'IrvanKede') {
                        $smmobject = new IrvanKede(stringEncryption('decrypt', $apikeyssmm->usercode), stringEncryption('decrypt', $apikeyssmm->apikey));
                    } else if ($apikeyssmm->vendor == 'DailyPanel') {
                        $smmobject = new DailyPanel(stringEncryption('decrypt', $apikeyssmm->apikey), stringEncryption('decrypt', $apikeyssmm->secretkey));
                    } else if ($apikeyssmm->vendor == 'WStore') {
                        $smmobject = new WStore(stringEncryption('decrypt', $apikeyssmm->usercode), stringEncryption('decrypt', $apikeyssmm->apikey), stringEncryption('decrypt', $apikeyssmm->secretkey));
                    } else if ($apikeyssmm->vendor == 'UNDRCTRL') {
                        $smmobject = new UNDRCTRL(stringEncryption('decrypt', $apikeyssmm->apikey));
                    } else if ($apikeyssmm->vendor == 'SosmedOnline') {
                        $smmobject = new SosmedOnline(stringEncryption('decrypt', $apikeyssmm->usercode), stringEncryption('decrypt', $apikeyssmm->apikey));
                    } else if ($apikeyssmm->vendor == 'SosmedOnlineVIP') {
                        $smmobject = new SosmedOnlineVIP(stringEncryption('decrypt', $apikeyssmm->usercode), stringEncryption('decrypt', $apikeyssmm->apikey));
                    } else if ($apikeyssmm->vendor == 'DjuraganSosmed') {
                        $smmobject = new DjuraganSosmed(stringEncryption('decrypt', $apikeyssmm->apikey));
                    } else if ($apikeyssmm->vendor == 'SMMRaja') {
                        $smmobject = new SMMRaja(stringEncryption('decrypt', $apikeyssmm->apikey));
                    } else if ($apikeyssmm->vendor == 'SMMIllusion') {
                        $smmobject = new SMMIllusion(stringEncryption('decrypt', $apikeyssmm->apikey));
                    } else if ($apikeyssmm->vendor == 'V1Pedia') {
                        $smmobject = new V1Pedia(stringEncryption('decrypt', $apikeyssmm->usercode), stringEncryption('decrypt', $apikeyssmm->apikey), stringEncryption('decrypt', $apikeyssmm->secretkey));
                    }
                }

                foreach ($transaction_queue as $key_order => $value_order) {
                    $this->db->trans_begin();

                    $one_days = date('Y-m-d H:i:s', strtotime("$value_order->createddate +1days"));

                    if (getCurrentDate() >= $one_days) {
                        if ($value_order->userid != null) {
                            $check_history_balance = $this->historybalance->total(array(
                                'userid' => $value_order->userid,
                                'type' => 'IN',
                                'orderid' => $value_order->id
                            ));

                            if ($check_history_balance == 0) {
                                $currentbalance = getCurrentBalance($value_order->userid, true);

                                $inserthistorybalance = array();
                                $inserthistorybalance['userid'] = $value_order->userid;
                                $inserthistorybalance['type'] = 'IN';

                                if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                    $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                } else {
                                    $inserthistorybalance['nominal'] = $value_order->price;
                                }

                                $inserthistorybalance['currentbalance'] = $currentbalance;
                                $inserthistorybalance['orderid'] = $value_order->id;
                                $inserthistorybalance['createdby'] = $value_order->userid;
                                $inserthistorybalance['createddate'] = getCurrentDate();

                                $this->historybalance->insert($inserthistorybalance);

                                $updateUser = array();

                                if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                    $updateUser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                } else {
                                    $updateUser['balance'] = $currentbalance + $value_order->price;
                                }

                                $this->msusers->update(array(
                                    'id' => $value_order->userid
                                ), $updateUser);
                            }
                        }

                        $update = array();
                        $update['status'] = 'gagal';
                        $update['queuetransaction'] = 0;

                        $this->trorder->update(array(
                            'id' => $value_order->id
                        ), $update);

                        pullTransaction($value_order->userid ?? $value_order->clientip);
                        $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);
                    } else {
                        if ($value_order->type == 'PPOB' && $apikeysppob != null) {
                            if ($apikeysppob->balance < $value_order->vendorprice) {
                                $this->db->trans_rollback();
                                continue;
                            }

                            if ($value_order->vendor == 'Digiflazz') {
                                $order = $ppobobject->topup($value_order->productcode, $value_order->target, $value_order->servercode);

                                $result = json_decode($order);

                                if (isset($result->data->status) && $result->data->status != 'Gagal') {
                                    $update = array();
                                    $update['note'] = $result->data->message;
                                    $update['sn'] = $result->data->sn;
                                    $update['status'] = strtolower($result->data->status);
                                    $update['queuetransaction'] = 0;
                                    $update['jsonresponse'] = json_encode($result);

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                } else {
                                    if ($value_order->userid != null) {
                                        $check_history_balance = $this->historybalance->total(array(
                                            'userid' => $value_order->userid,
                                            'type' => 'IN',
                                            'orderid' => $value_order->id
                                        ));

                                        if ($check_history_balance == 0) {
                                            $currentbalance = getCurrentBalance($value_order->userid, true);

                                            $inserthistorybalance = array();
                                            $inserthistorybalance['userid'] = $value_order->userid;
                                            $inserthistorybalance['type'] = 'IN';

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                            } else {
                                                $inserthistorybalance['nominal'] = $value_order->price;
                                            }

                                            $inserthistorybalance['currentbalance'] = $currentbalance;
                                            $inserthistorybalance['orderid'] = $value_order->id;
                                            $inserthistorybalance['createdby'] = $value_order->userid;
                                            $inserthistorybalance['createddate'] = getCurrentDate();

                                            $this->historybalance->insert($inserthistorybalance);

                                            $updateUser = array();

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $updateUser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                            } else {
                                                $updateUser['balance'] = $currentbalance + $value_order->price;
                                            }

                                            $this->msusers->update(array(
                                                'id' => $value_order->userid
                                            ), $updateUser);
                                        }
                                    }

                                    $update = array();
                                    $update['status'] = 'gagal';
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);

                                    if ($order != null) {
                                        log_message_user('error', '[DIGIFLAZZ CRON QUEUE TRANSACTION] Response: ' . json_encode($result), $value->id);
                                    }
                                }
                            } else if ($value_order->vendor == 'VIPayment') {
                                if ($value_order->producttype == 'game') {
                                    if (preg_match('/(mobile legends)|(mobilelegends)|(mobile legend)|(mobilelegend)/', strtolower($value_order->brand))) {
                                        $order = $ppobobject->order_game($value_order->productcode, $value_order->target, $value_order->zoneid);
                                    } else {
                                        $order = $ppobobject->order_game($value_order->productcode, $value_order->target, null);
                                    }
                                } else {
                                    $order = $ppobobject->order_prepaid($value_order->productcode, $value_order->target);
                                }

                                if (isset($order->result) && $order->result) {
                                    $update = array();
                                    $update['servercode'] = $order->data->trxid;
                                    $update['jsonresponse'] = json_encode($order);
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                } else {
                                    if ($value_order->userid != null) {
                                        $check_history_balance = $this->historybalance->total(array(
                                            'userid' => $value_order->userid,
                                            'type' => 'IN',
                                            'orderid' => $value_order->id
                                        ));

                                        if ($check_history_balance == 0) {
                                            $currentbalance = getCurrentBalance($value_order->userid, true);

                                            $inserthistorybalance = array();
                                            $inserthistorybalance['userid'] = $value_order->userid;
                                            $inserthistorybalance['type'] = 'IN';

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                            } else {
                                                $inserthistorybalance['nominal'] = $value_order->price;
                                            }

                                            $inserthistorybalance['currentbalance'] = $currentbalance;
                                            $inserthistorybalance['orderid'] = $value_order->id;
                                            $inserthistorybalance['createdby'] = $value_order->userid;
                                            $inserthistorybalance['createddate'] = getCurrentDate();

                                            $this->historybalance->insert($inserthistorybalance);

                                            $updateUser = array();

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $updateUser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                            } else {
                                                $updateUser['balance'] = $currentbalance + $value_order->price;
                                            }

                                            $this->msusers->update(array(
                                                'id' => $value_order->userid
                                            ), $updateUser);
                                        }
                                    }

                                    $update = array();
                                    $update['status'] = 'gagal';
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);

                                    if ($order != null) {
                                        log_message_user('error', '[VIPAYMENT CRON QUEUE TRANSACTION] Response: ' . json_encode($order), $value->id);
                                    }
                                }
                            }
                        } else if ($value_order->type == 'SMM' && $apikeyssmm != null) {
                            $vendorprice = $value_order->vendorprice;
                            $priceperitem = $vendorprice / 1000;

                            $vendorprice_total = $priceperitem * $value_order->qty;

                            if ($apikeyssmm->balance < $vendorprice_total) {
                                $this->db->trans_rollback();
                                continue;
                            }

                            if ($value_order->vendor == 'BuzzerPanel') {
                                $order = $smmobject->order($value_order->productcode, $value_order->target, $value_order->qty);

                                if (isset($order->status) && $order->status) {
                                    $update = array();
                                    $update['servercode'] = $order->data->id;
                                    $update['jsonresponse'] = json_encode($order);
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                } else {
                                    if ($value_order->userid != null) {
                                        $check_history_balance = $this->historybalance->total(array(
                                            'userid' => $value_order->userid,
                                            'type' => 'IN',
                                            'orderid' => $value_order->id
                                        ));

                                        if ($check_history_balance == 0) {
                                            $currentbalance = getCurrentBalance($value_order->userid, true);

                                            $inserthistorybalance = array();
                                            $inserthistorybalance['userid'] = $value_order->userid;
                                            $inserthistorybalance['type'] = 'IN';

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                            } else {
                                                $inserthistorybalance['nominal'] = $value_order->price;
                                            }

                                            $inserthistorybalance['currentbalance'] = $currentbalance;
                                            $inserthistorybalance['orderid'] = $value_order->id;
                                            $inserthistorybalance['createdby'] = $value_order->userid;
                                            $inserthistorybalance['createddate'] = getCurrentDate();

                                            $this->historybalance->insert($inserthistorybalance);

                                            $updateUser = array();

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $updateUser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                            } else {
                                                $updateUser['balance'] = $currentbalance + $value_order->price;
                                            }

                                            $this->msusers->update(array(
                                                'id' => $value_order->userid
                                            ), $updateUser);
                                        }
                                    }

                                    $update = array();
                                    $update['status'] = 'gagal';
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);

                                    if ($order != null) {
                                        log_message_user('error', '[BUZZERPANEL CRON QUEUE TRANSACTION] Response: ' . json_encode($order), $value->id);
                                    }
                                }
                            } else if ($value_order->vendor == 'MedanPedia') {
                                $order = $smmobject->order($value_order->productcode, $value_order->target, $value_order->qty, $value_order->additional ? $value_order->additional : '');

                                if (isset($order->status) && $order->status) {
                                    $update = array();
                                    $update['servercode'] = $order->data->id;
                                    $update['jsonresponse'] = json_encode($order);
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                } else {
                                    if ($value_order->userid != null) {
                                        $check_history_balance = $this->historybalance->total(array(
                                            'userid' => $value_order->userid,
                                            'type' => 'IN',
                                            'orderid' => $value_order->id
                                        ));

                                        if ($check_history_balance == 0) {
                                            $currentbalance = getCurrentBalance($value_order->userid, true);

                                            $inserthistorybalance = array();
                                            $inserthistorybalance['userid'] = $value_order->userid;
                                            $inserthistorybalance['type'] = 'IN';

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                            } else {
                                                $inserthistorybalance['nominal'] = $value_order->price;
                                            }

                                            $inserthistorybalance['currentbalance'] = $currentbalance;
                                            $inserthistorybalance['orderid'] = $value_order->id;
                                            $inserthistorybalance['createdby'] = $value_order->userid;
                                            $inserthistorybalance['createddate'] = getCurrentDate();

                                            $this->historybalance->insert($inserthistorybalance);

                                            $updateUser = array();

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $updateUser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                            } else {
                                                $updateUser['balance'] = $currentbalance + $value_order->price;
                                            }

                                            $this->msusers->update(array(
                                                'id' => $value_order->userid
                                            ), $updateUser);
                                        }
                                    }

                                    $update = array();
                                    $update['status'] = 'gagal';
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);

                                    if ($order != null) {
                                        log_message_user('error', '[MEDANPEDIA CRON QUEUE TRANSACTION] Response: ' . json_encode($order), $value->id);
                                    }
                                }
                            } else if ($value_order->vendor == 'IrvanKede') {
                                $order = $smmobject->order(array(
                                    'service' => $value_order->productcode,
                                    'target' => $value_order->target,
                                    'quantity' => $value_order->qty,
                                    'custom_comments' => $value_order->additional ? $value_order->additional : '',
                                    'custom_link' => $value_order->additional ? $value_order->additional : ''
                                ));

                                if (isset($order->status) && $order->status) {
                                    $update = array();
                                    $update['servercode'] = $order->data->id;
                                    $update['jsonresponse'] = json_encode($order);
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                } else {
                                    if ($value_order->userid != null) {
                                        $check_history_balance = $this->historybalance->total(array(
                                            'userid' => $value_order->userid,
                                            'type' => 'IN',
                                            'orderid' => $value_order->id
                                        ));

                                        if ($check_history_balance == 0) {
                                            $currentbalance = getCurrentBalance($value_order->userid, true);

                                            $inserthistorybalance = array();
                                            $inserthistorybalance['userid'] = $value_order->userid;
                                            $inserthistorybalance['type'] = 'IN';

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                            } else {
                                                $inserthistorybalance['nominal'] = $value_order->price;
                                            }

                                            $inserthistorybalance['currentbalance'] = $currentbalance;
                                            $inserthistorybalance['orderid'] = $value_order->id;
                                            $inserthistorybalance['createdby'] = $value_order->userid;
                                            $inserthistorybalance['createddate'] = getCurrentDate();

                                            $this->historybalance->insert($inserthistorybalance);

                                            $updateUser = array();

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $updateUser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                            } else {
                                                $updateUser['balance'] = $currentbalance + $value_order->price;
                                            }

                                            $this->msusers->update(array(
                                                'id' => $value_order->userid
                                            ), $updateUser);
                                        }
                                    }

                                    $update = array();
                                    $update['status'] = 'gagal';
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);

                                    if ($order != null) {
                                        log_message_user('error', '[IRVANKEDE CRON QUEUE TRANSACTION] Response: ' . json_encode($order), $value->id);
                                    }
                                }
                            } else if ($value_order->vendor == 'DailyPanel') {
                                $order = $smmobject->order($value_order->productcode, $value_order->target, $value_order->qty, $value_order->additional ? $value_order->additional : '');

                                if (isset($order->success) && $order->success) {
                                    $update = array();
                                    $update['servercode'] = $order->msg->order_id;
                                    $update['jsonresponse'] = json_encode($order);
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                } else {
                                    if ($value_order->userid != null) {
                                        $check_history_balance = $this->historybalance->total(array(
                                            'userid' => $value_order->userid,
                                            'type' => 'IN',
                                            'orderid' => $value_order->id
                                        ));

                                        if ($check_history_balance == 0) {
                                            $currentbalance = getCurrentBalance($value_order->userid, true);

                                            $inserthistorybalance = array();
                                            $inserthistorybalance['userid'] = $value_order->userid;
                                            $inserthistorybalance['type'] = 'IN';

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                            } else {
                                                $inserthistorybalance['nominal'] = $value_order->price;
                                            }

                                            $inserthistorybalance['currentbalance'] = $currentbalance;
                                            $inserthistorybalance['orderid'] = $value_order->id;
                                            $inserthistorybalance['createdby'] = $value_order->userid;
                                            $inserthistorybalance['createddate'] = getCurrentDate();

                                            $this->historybalance->insert($inserthistorybalance);

                                            $updateUser = array();

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $updateUser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                            } else {
                                                $updateUser['balance'] = $currentbalance + $value_order->price;
                                            }

                                            $this->msusers->update(array(
                                                'id' => $value_order->userid
                                            ), $updateUser);
                                        }
                                    }

                                    $update = array();
                                    $update['status'] = 'gagal';
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);

                                    if ($order != null) {
                                        log_message_user('error', '[DAILYPANEL CRON QUEUE TRANSACTION] Response: ' . json_encode($order), $value->id);
                                    }
                                }
                            } else if ($value_order->vendor == 'WStore') {
                                $order = $smmobject->order($value_order->productcode, $value_order->target, $value_order->qty);

                                if (isset($order->response) && $order->response) {
                                    $update = array();
                                    $update['servercode'] = $order->data->id;
                                    $update['jsonresponse'] = json_encode($order);
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                } else {
                                    if ($value_order->userid != null) {
                                        $check_history_balance = $this->historybalance->total(array(
                                            'userid' => $value_order->userid,
                                            'type' => 'IN',
                                            'orderid' => $value_order->id
                                        ));

                                        if ($check_history_balance == 0) {
                                            $currentbalance = getCurrentBalance($value_order->userid, true);

                                            $inserthistorybalance = array();
                                            $inserthistorybalance['userid'] = $value_order->userid;
                                            $inserthistorybalance['type'] = 'IN';

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $inserthistorybalance['nominal'] = $value_order->price - round($value->fee);
                                            } else {
                                                $inserthistorybalance['nominal'] = $value_order->price;
                                            }

                                            $inserthistorybalance['currentbalance'] = $currentbalance;
                                            $inserthistorybalance['orderid'] = $value_order->id;
                                            $inserthistorybalance['createdby'] = $value_order->userid;
                                            $inserthistorybalance['createddate'] = getCurrentDate();

                                            $this->historybalance->insert($inserthistorybalance);

                                            $updateUser = array();

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $updateUser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                            } else {
                                                $updateUser['balance'] = $currentbalance + $value_order->price;
                                            }

                                            $this->msusers->update(array(
                                                'id' => $value_order->userid
                                            ), $updateUser);
                                        }
                                    }

                                    $update = array();
                                    $update['status'] = 'gagal';
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);

                                    if ($order != null) {
                                        log_message_user('error', '[WSTORE CRON QUEUE TRANSACTION] Response: ' . json_encode($order), $value->id);
                                    }
                                }
                            } else if ($value_order->vendor == 'UNDRCTRL') {
                                $order = $smmobject->order($value_order->productcode, $value_order->target, $value_order->qty);

                                if (isset($order->order) && $order->order) {
                                    $update = array();
                                    $update['servercode'] = $order->order;
                                    $update['jsonresponse'] = json_encode($order);
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                } else {
                                    if ($value_order->userid != null) {
                                        $check_history_balance = $this->historybalance->total(array(
                                            'userid' => $value_order->userid,
                                            'type' => 'IN',
                                            'orderid' => $value_order->id
                                        ));

                                        if ($check_history_balance == 0) {
                                            $currentbalance = getCurrentBalance($value_order->userid, true);

                                            $inserthistorybalance = array();
                                            $inserthistorybalance['userid'] = $value_order->userid;
                                            $inserthistorybalance['type'] = 'IN';

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                            } else {
                                                $inserthistorybalance['nominal'] = $value_order->price;
                                            }

                                            $inserthistorybalance['currentbalance'] = $currentbalance;
                                            $inserthistorybalance['orderid'] = $value_order->id;
                                            $inserthistorybalance['createdby'] = $value_order->userid;
                                            $inserthistorybalance['createddate'] = getCurrentDate();

                                            $this->historybalance->insert($inserthistorybalance);

                                            $updateUser = array();

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $updateUser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                            } else {
                                                $updateUser['balance'] = $currentbalance + $value_order->price;
                                            }

                                            $this->msusers->update(array(
                                                'id' => $value_order->userid
                                            ), $updateUser);
                                        }
                                    }

                                    $update = array();
                                    $update['status'] = 'gagal';
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);

                                    if ($order != null) {
                                        log_message_user('error', '[UNDRCTRL CRON QUEUE TRANSACTION] Response: ' . json_encode($order), $value->id);
                                    }
                                }
                            } else if ($value_order->vendor == 'SosmedOnline') {
                                $order = $smmobject->order($value_order->productcode, $value_order->target, $value_order->qty, $value_order->additional ? $value_order->additional : '');

                                if (isset($order->status) && $order->status) {
                                    $update = array();
                                    $update['servercode'] = $order->data->id;
                                    $update['jsonresponse'] = json_encode($order);
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                } else {
                                    if ($value_order->userid != null) {
                                        $check_history_balance = $this->historybalance->total(array(
                                            'userid' => $value_order->userid,
                                            'type' => 'IN',
                                            'orderid' => $value_order->id
                                        ));

                                        if ($check_history_balance == 0) {
                                            $currentbalance = getCurrentBalance($value_order->userid, true);

                                            $inserthistorybalance = array();
                                            $inserthistorybalance['userid'] = $value_order->userid;
                                            $inserthistorybalance['type'] = 'IN';

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                            } else {
                                                $inserthistorybalance['nominal'] = $value_order->price;
                                            }

                                            $inserthistorybalance['currentbalance'] = $currentbalance;
                                            $inserthistorybalance['orderid'] = $value_order->id;
                                            $inserthistorybalance['createdby'] = $value_order->userid;
                                            $inserthistorybalance['createddate'] = getCurrentDate();

                                            $this->historybalance->insert($inserthistorybalance);

                                            $updateUser = array();

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $updateUser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                            } else {
                                                $updateUser['balance'] = $currentbalance + $value_order->price;
                                            }

                                            $this->msusers->update(array(
                                                'id' => $value_order->userid
                                            ), $updateUser);
                                        }
                                    }

                                    $update = array();
                                    $update['status'] = 'gagal';
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);

                                    if ($order != null) {
                                        log_message_user('error', '[SOSMEDONLINE CRON QUEUE TRANSACTION] Response: ' . json_encode($order), $value->id);
                                    }
                                }
                            } else if ($value_order->vendor == 'SosmedOnlineVIP') {
                                $order = $smmobject->order($value_order->productcode, $value_order->target, $value_order->qty, $value_order->additional ? $value_order->additional : '');

                                if (isset($order->status) && $order->status) {
                                    $update = array();
                                    $update['servercode'] = $order->data->id;
                                    $update['jsonresponse'] = json_encode($order);
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                } else {
                                    if ($value_order->userid != null) {
                                        $check_history_balance = $this->historybalance->total(array(
                                            'userid' => $value_order->userid,
                                            'type' => 'IN',
                                            'orderid' => $value_order->id
                                        ));

                                        if ($check_history_balance == 0) {
                                            $currentbalance = getCurrentBalance($value_order->userid, true);

                                            $inserthistorybalance = array();
                                            $inserthistorybalance['userid'] = $value_order->userid;
                                            $inserthistorybalance['type'] = 'IN';

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                            } else {
                                                $inserthistorybalance['nominal'] = $value_order->price;
                                            }

                                            $inserthistorybalance['currentbalance'] = $currentbalance;
                                            $inserthistorybalance['orderid'] = $value_order->id;
                                            $inserthistorybalance['createdby'] = $value_order->userid;
                                            $inserthistorybalance['createddate'] = getCurrentDate();

                                            $this->historybalance->insert($inserthistorybalance);

                                            $updateUser = array();

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $updateUser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                            } else {
                                                $updateUser['balance'] = $currentbalance + $value_order->price;
                                            }

                                            $this->msusers->update(array(
                                                'id' => $value_order->userid
                                            ), $updateUser);
                                        }
                                    }

                                    $update = array();
                                    $update['status'] = 'gagal';
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);

                                    if ($order != null) {
                                        log_message_user('error', '[SOSMEDONLINEVIP CRON QUEUE TRANSACTION] Response: ' . json_encode($order), $value->id);
                                    }
                                }
                            } else if ($value_order->vendor == 'DjuraganSosmed') {
                                $order = $smmobject->order($value_order->productcode, $value_order->target, $value_order->qty);

                                if (isset($order->order) && $order->order) {
                                    $update = array();
                                    $update['servercode'] = $order->order;
                                    $update['jsonresponse'] = json_encode($order);
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                } else {
                                    if ($value_order->userid != null) {
                                        $check_history_balance = $this->historybalance->total(array(
                                            'userid' => $value_order->userid,
                                            'type' => 'IN',
                                            'orderid' => $value_order->id
                                        ));

                                        if ($check_history_balance == 0) {
                                            $currentbalance = getCurrentBalance($value_order->userid, true);

                                            $inserthistorybalance = array();
                                            $inserthistorybalance['userid'] = $value_order->userid;
                                            $inserthistorybalance['type'] = 'IN';

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                            } else {
                                                $inserthistorybalance['nominal'] = $value_order->price;
                                            }

                                            $inserthistorybalance['currentbalance'] = $currentbalance;
                                            $inserthistorybalance['orderid'] = $value_order->id;
                                            $inserthistorybalance['createdby'] = $value_order->userid;
                                            $inserthistorybalance['createddate'] = getCurrentDate();

                                            $this->historybalance->insert($inserthistorybalance);

                                            $updateUser = array();

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $updateUser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                            } else {
                                                $updateUser['balance'] = $currentbalance + $value_order->price;
                                            }

                                            $this->msusers->update(array(
                                                'id' => $value_order->userid
                                            ), $updateUser);
                                        }
                                    }

                                    $update = array();
                                    $update['status'] = 'gagal';
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);

                                    if ($order != null) {
                                        log_message_user('error', '[DJURAGANSOSMED CRON QUEUE TRANSACTION] Response: ' . json_encode($order), $value->id);
                                    }
                                }
                            } else if ($value_order->vendor == 'SMMRaja') {
                                $order = $smmobject->order($value_order->productcode, $value_order->target, $value_order->qty);

                                if (isset($order->order) || $order->order) {
                                    $update = array();
                                    $update['servercode'] = $order->order;
                                    $update['jsonresponse'] = json_encode($order);
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                } else {
                                    if ($value_order->userid != null) {
                                        $check_history_balance = $this->historybalance->total(array(
                                            'userid' => $value_order->userid,
                                            'type' => 'IN',
                                            'orderid' => $value_order->id
                                        ));

                                        if ($check_history_balance == 0) {
                                            $currentbalance = getCurrentBalance($value_order->userid, true);

                                            $inserthistorybalance = array();
                                            $inserthistorybalance['userid'] = $value_order->userid;
                                            $inserthistorybalance['type'] = 'IN';

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                            } else {
                                                $inserthistorybalance['nominal'] = $value_order->price;
                                            }

                                            $inserthistorybalance['currentbalance'] = $currentbalance;
                                            $inserthistorybalance['orderid'] = $value_order->id;
                                            $inserthistorybalance['createdby'] = $value_order->userid;
                                            $inserthistorybalance['createddate'] = getCurrentDate();

                                            $this->historybalance->insert($inserthistorybalance);

                                            $updateUser = array();

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $updateUser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                            } else {
                                                $updateUser['balance'] = $currentbalance + $value_order->price;
                                            }

                                            $this->msusers->update(array(
                                                'id' => $value_order->userid
                                            ), $updateUser);
                                        }
                                    }

                                    $update = array();
                                    $update['status'] = 'gagal';
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);

                                    if ($order != null) {
                                        log_message_user('error', '[SMMRAJA CRON QUEUE TRANSACTION] Response: ' . json_encode($order), $value->id);
                                    }
                                }
                            } else if ($value_order->vendor == 'SMMIllusion') {
                                $order = $smmobject->order($value_order->productcode, $value_order->target, $value_order->qty);

                                if (isset($order->order) && $order->order) {
                                    $update = array();
                                    $update['servercode'] = $order->order;
                                    $update['jsonresponse'] = json_encode($order);
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                } else {
                                    if ($value_order->userid != null) {
                                        $check_history_balance = $this->historybalance->total(array(
                                            'userid' => $value_order->userid,
                                            'type' => 'IN',
                                            'orderid' => $value_order->id
                                        ));

                                        if ($check_history_balance == 0) {
                                            $currentbalance = getCurrentBalance($value_order->userid, true);

                                            $inserthistorybalance = array();
                                            $inserthistorybalance['userid'] = $value_order->userid;
                                            $inserthistorybalance['type'] = 'IN';

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                            } else {
                                                $inserthistorybalance['nominal'] = $value_order->price;
                                            }

                                            $inserthistorybalance['currentbalance'] = $currentbalance;
                                            $inserthistorybalance['orderid'] = $value_order->id;
                                            $inserthistorybalance['createdby'] = $value_order->userid;
                                            $inserthistorybalance['createddate'] = getCurrentDate();

                                            $this->historybalance->insert($inserthistorybalance);

                                            $updateUser = array();

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $updateUser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                            } else {
                                                $updateUser['balance'] = $currentbalance + $value_order->price;
                                            }

                                            $this->msusers->update(array(
                                                'id' => $value_order->userid
                                            ), $updateUser);
                                        }
                                    }

                                    $update = array();
                                    $update['status'] = 'gagal';
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);

                                    if ($order != null) {
                                        log_message_user('error', '[SMMILLUSION CRON QUEUE TRANSACTION] Response: ' . json_encode($order), $value->id);
                                    }
                                }
                            } else if ($value_order->vendor == 'V1Pedia') {
                                $order = $smmobject->order($value_order->productcode, $value_order->target, $value_order->qty);

                                if (isset($order->response) && $order->response) {
                                    $update = array();
                                    $update['servercode'] = $order->data->id;
                                    $update['jsonresponse'] = json_encode($order);
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                } else {
                                    if ($value_order->userid != null) {
                                        $check_history_balance = $this->historybalance->total(array(
                                            'userid' => $value_order->userid,
                                            'type' => 'IN',
                                            'orderid' => $value_order->id
                                        ));

                                        if ($check_history_balance == 0) {
                                            $currentbalance = getCurrentBalance($value_order->userid, true);

                                            $inserthistorybalance = array();
                                            $inserthistorybalance['userid'] = $value_order->userid;
                                            $inserthistorybalance['type'] = 'IN';

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                            } else {
                                                $inserthistorybalance['nominal'] = $value_order->price;
                                            }

                                            $inserthistorybalance['currentbalance'] = $currentbalance;
                                            $inserthistorybalance['orderid'] = $value_order->id;
                                            $inserthistorybalance['createdby'] = $value_order->userid;
                                            $inserthistorybalance['createddate'] = getCurrentDate();

                                            $this->historybalance->insert($inserthistorybalance);

                                            $updateUser = array();

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $updateUser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                            } else {
                                                $updateUser['balance'] = $currentbalance + $value_order->price;
                                            }

                                            $this->msusers->update(array(
                                                'id' => $value_order->userid
                                            ), $updateUser);
                                        }
                                    }

                                    $update = array();
                                    $update['status'] = 'gagal';
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);

                                    if ($order != null) {
                                        log_message_user('error', '[V1PEDIA CRON QUEUE TRANSACTION] Response: ' . json_encode($order), $value->id);
                                    }
                                }
                            } else {
                                $order = $smmobject->order($value_order->productcode, 'SMM', $value_order->target, null, $value_order->additional ? $value_order->additional : '', $value_order->qty);

                                if (isset($order->status) && $order->status) {
                                    $update = array();
                                    $update['servercode'] = $order->data->orderid;
                                    $update['jsonresponse'] = json_encode($order);
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);
                                } else {
                                    if ($value_order->userid != null) {
                                        $check_history_balance = $this->historybalance->total(array(
                                            'userid' => $value_order->userid,
                                            'type' => 'IN',
                                            'orderid' => $value_order->id
                                        ));

                                        if ($check_history_balance == 0) {
                                            $currentbalance = getCurrentBalance($value_order->userid, true);

                                            $inserthistorybalance = array();
                                            $inserthistorybalance['userid'] = $value_order->userid;
                                            $inserthistorybalance['type'] = 'IN';

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                            } else {
                                                $inserthistorybalance['nominal'] = $value_order->price;
                                            }

                                            $inserthistorybalance['currentbalance'] = $currentbalance;
                                            $inserthistorybalance['orderid'] = $value_order->id;
                                            $inserthistorybalance['createdby'] = $value_order->userid;
                                            $inserthistorybalance['createddate'] = getCurrentDate();

                                            $this->historybalance->insert($inserthistorybalance);

                                            $updateUser = array();

                                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                                $updateUser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                            } else {
                                                $updateUser['balance'] = $currentbalance + $value_order->price;
                                            }

                                            $this->msusers->update(array(
                                                'id' => $value_order->userid
                                            ), $updateUser);
                                        }
                                    }

                                    $update = array();
                                    $update['status'] = 'gagal';
                                    $update['queuetransaction'] = 0;

                                    $this->trorder->update(array(
                                        'id' => $value_order->id
                                    ), $update);

                                    pullTransaction($value_order->userid ?? $value_order->clientip);
                                    $this->send_notification($value_order->id, $value->id, $value_order->phonenumber ?? $value_order->phonenumber_order);

                                    if ($order != null) {
                                        log_message_user('error', '[MEMBER CRON QUEUE TRANSACTION] Response: ' . json_encode($order), $value->id);
                                    }
                                }
                            }
                        }
                    }

                    if ($this->db->trans_status() === FALSE) {
                        $this->db->trans_rollback();
                    }

                    $this->db->trans_commit();
                }
            }
        } catch (Exception $ex) {
            $this->db->trans_rollback();
        }
    }

    public function product_data()
    {
        ini_set('max_execution_time', 0);

        $users = $this->msusers->select('a.id, a.companycategory, a.domain')
            ->result(array(
                'a.merchantid' => null,
                'a.licenseid !=' => null,
                'a.expireddate >=' => getCurrentDate(),
                '(a.multivendor IS NULL OR a.multivendor = 0) =' => true,
            ));

        foreach ($users as $kusers => $vusers) {
            $ppob = getCurrentAPIKeys('PPOB', $vusers->id);

            if (($vusers->companycategory == 'PPOB & SMM' || $vusers->companycategory == 'PPOB') && $ppob != null) {
                syncProduct($ppob->vendor, 'PPOB', $ppob->usercode, $ppob->apikey, $ppob->secretkey, '', $vusers->id, null, "https://$vusers->domain/", $ppob->dontadd_product);
            }
        }
    }

    public function product_data_smm()
    {
        ini_set('max_execution_time', 0);

        $users = $this->msusers->select('a.id, a.companycategory, a.domain')
            ->result(array(
                'a.merchantid' => null,
                'a.licenseid !=' => null,
                'a.expireddate >=' => getCurrentDate(),
                '(a.multivendor IS NULL OR a.multivendor = 0) =' => true,
            ));

        foreach ($users as $kusers => $vusers) {
            $smm = getCurrentAPIKeys('SMM', $vusers->id);

            if (($vusers->companycategory == 'PPOB & SMM' || $vusers->companycategory == 'SMM') && $smm != null) {
                syncProduct($smm->vendor, 'SMM', $smm->usercode, $smm->apikey, $smm->secretkey, $smm->currency_rate, $vusers->id, $smm->public_key, "https://$vusers->domain/", $smm->dontadd_product);
            }
        }
    }

    public function member_product_transaction_queue_expired()
    {
        try {
            $queue = $this->trorder->select('a.id, a.userid, a.price, a.createddate, b.merchantid, b.phonenumber, a.clientip, a.paymenttype, a.fee')
                ->join('msusers b', 'b.id = a.userid', 'LEFT')
                ->result(array(
                    'a.queuetransaction' => 1,
                    'LOWER(a.status) =' => 'pending',
                    'a.servercode' => null
                ));

            foreach ($queue as $key => $value) {
                $one_days = date('Y-m-d H:i:s', strtotime("$value->createddate +1 days"));

                if (getCurrentDate() >= $one_days) {
                    $this->db->trans_begin();

                    $update = array();
                    $update['status'] = 'gagal';
                    $update['queuetransaction'] = 0;

                    $this->trorder->update(array(
                        'id' => $value->id
                    ), $update);

                    pullTransaction($value->userid ?? $value->clientip);

                    if ($value->userid != null) {
                        $check_history_balance = $this->historybalance->total(array(
                            'userid' => $value->userid,
                            'type' => 'IN',
                            'orderid' => $value->id
                        ));

                        if ($check_history_balance == 0) {
                            $currentbalance = getCurrentBalance($value->userid, true);

                            $inserthistorybalance = array();
                            $inserthistorybalance['userid'] = $value->userid;
                            $inserthistorybalance['type'] = 'IN';

                            if ($value->paymenttype == 'Otomatis' && $value->fee != null) {
                                $inserthistorybalance['nominal'] = $value->price - round($value->fee);
                            } else {
                                $inserthistorybalance['nominal'] = $value->price;
                            }

                            $inserthistorybalance['currentbalance'] = $currentbalance;
                            $inserthistorybalance['orderid'] = $value->id;
                            $inserthistorybalance['createdby'] = $value->userid;
                            $inserthistorybalance['createddate'] = getCurrentDate();

                            $this->historybalance->insert($inserthistorybalance);

                            $updateUser = array();

                            if ($value->paymenttype == 'Otomatis' && $value->fee != null) {
                                $updateUser['balance'] = $currentbalance + ($value->price - round($value->fee));
                            } else {
                                $updateUser['balance'] = $currentbalance + $value->price;
                            }

                            $this->msusers->update(array(
                                'id' => $value->userid
                            ), $updateUser);
                        }
                    }

                    $this->send_notification($value->id, $value->merchantid ?? $value->merchantid_order, $value->phonenumber ?? $value->phonenumber_order);

                    if ($this->db->trans_status() === FALSE) {
                        throw new Exception('Error');
                    }

                    $this->db->trans_commit();
                }
            }
        } catch (Exception $ex) {
            $this->db->trans_rollback();
        }
    }

    public function member_topup_transaction_paymentgateway()
    {
        try {
            $users = $this->msusers->select('id')
                ->result(array(
                    'merchantid' => null,
                    'licenseid !=' => null
                ));

            foreach ($users as $key_user => $value_user) {
                $deposit = $this->deposits->select('a.*, c.apps_id')
                    ->join('userbuynotificationhandler b', "b.id = a.paymentmethodid AND a.gatewayvendor = 'Notification Handler Services'", 'LEFT')
                    ->join('msnotificationhandlerpackage c', 'c.id = b.notificationid', 'LEFT')
                    ->join('msusers d', 'd.id = a.userid')
                    ->result(array(
                        'a.merchantid' => $value_user->id,
                        'a.paymenttype' => 'Otomatis',
                        'a.status' => 'Pending',
                        'a.gatewayvendor !=' => null
                    ));

                $transaction = $this->trorder->select('a.*, c.apps_id')
                    ->join('userbuynotificationhandler b', "b.id = a.paymentmethodid AND a.gatewayvendor = 'Notification Handler Services'", 'LEFT')
                    ->join('msnotificationhandlerpackage c', 'c.id = b.notificationid', 'LEFT')
                    ->result(array(
                        'a.merchantid_order' => $value_user->id,
                        'a.paymenttype' => 'Otomatis',
                        'a.status_payment' => 'pending',
                        'a.status' => 'pending'
                    ));

                if (count($deposit) > 0) {
                    $tripay = $this->mspaymentgateway->get(array(
                        'userid' => $value_user->id,
                        'type' => 'Payment Gateway',
                        'vendor' => 'Tripay',
                        "(isdisabled IS NULL OR isdisabled = '0') =" => true
                    ))->row();

                    if ($tripay != null) {
                        $detail = json_decode(stringEncryption('decrypt', $tripay->detail));
                        if ($detail == null) continue;

                        $tripay = new Tripay($detail->merchantcode, $detail->apikey, $detail->privatekey);

                        foreach ($deposit as $key => $value) {
                            $this->db->trans_begin();

                            if ($value->gatewayvendor != 'Tripay') {
                                $this->db->trans_rollback();

                                continue;
                            }

                            $detailtransaction = $tripay->detailTransaction($value->servercode);

                            if (isset($detailtransaction->success) && $detailtransaction->success) {
                                if ($detailtransaction->data->status == 'PAID') {
                                    $update = array();
                                    $update['status'] = 'Success';

                                    $this->deposits->update(array(
                                        'id' => $value->id
                                    ), $update);

                                    $this->send_notification($value->id, $value->merchantid, $value->phonenumber, 'deposit');

                                    $totalharga = 0;
                                    if ($value->isbonus == 1) {
                                        $totalharga =  $value->nominal + $value->nominalbonus - ($value->fee ?? 0);
                                    } else {
                                        $totalharga =  $value->nominal - ($value->fee ?? 0);
                                    }

                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value->userid,
                                        'type' => 'IN',
                                        'depositid' => $value->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value->userid, true);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value->userid;
                                        $inserthistorybalance['type'] = 'IN';
                                        $inserthistorybalance['nominal'] = $totalharga;
                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['depositid'] = $value->id;
                                        $inserthistorybalance['createdby'] = $value->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $updateUser = array();
                                        $updateUser['balance'] = $currentbalance + $totalharga;

                                        $this->msusers->update(array(
                                            'id' => $value->userid
                                        ), $updateUser);
                                    }
                                } elseif ($detailtransaction->data->status != 'UNPAID') {
                                    $update = array();
                                    $update['status'] = ucfirst(strtolower($detailtransaction->data->status));

                                    $this->deposits->update(array(
                                        'id' => $value->id
                                    ), $update);

                                    $this->send_notification($value->id, $value->merchantid, $value->phonenumber, 'deposit');
                                }
                            } else {
                                if ($detailtransaction != null) {
                                    log_message_user('error', '[TRIPAY CRON TRANSACTION TOPUP] Response: ' . json_encode($detailtransaction), $value_user->id);
                                }
                            }

                            if ($this->db->trans_status() === FALSE) {
                                throw new Exception('Error');
                            }

                            $this->db->trans_commit();
                        }
                    }

                    $midtrans = $this->mspaymentgateway->get(array(
                        'userid' => $value_user->id,
                        'type' => 'Payment Gateway',
                        'vendor' => 'Midtrans',
                        "(isdisabled IS NULL OR isdisabled = '0') =" => true
                    ))->row();

                    if ($midtrans != null) {
                        $detail = json_decode(stringEncryption('decrypt', $midtrans->detail));
                        if ($detail == null) continue;

                        $midtrans = new MidtransHelper($detail->serverkey, true);

                        foreach ($deposit as $key => $value) {
                            $this->db->trans_begin();

                            if ($value->gatewayvendor != 'Midtrans') {
                                $this->db->trans_rollback();

                                continue;
                            }

                            $detailtransaction = $midtrans->status($value->code);

                            if (isset($detailtransaction->transaction_status) && $detailtransaction->transaction_status == 'settlement') {
                                $update = array();
                                $update['status'] = 'Success';

                                $this->deposits->update(array(
                                    'id' => $value->id
                                ), $update);

                                $this->send_notification($value->id, $value->merchantid, $value->phonenumber, 'deposit');

                                $totalharga = 0;
                                if ($value->isbonus == 1) {
                                    $totalharga = $value->nominal + $value->nominalbonus - ($value->fee ?? 0);
                                } else {
                                    $totalharga = $value->nominal - ($value->fee ?? 0);
                                }

                                $check_history_balance = $this->historybalance->total(array(
                                    'userid' => $value->userid,
                                    'type' => 'IN',
                                    'depositid' => $value->id
                                ));

                                if ($check_history_balance == 0) {
                                    $currentbalance = getCurrentBalance($value->userid, true);

                                    $inserthistorybalance = array();
                                    $inserthistorybalance['userid'] = $value->userid;
                                    $inserthistorybalance['type'] = 'IN';
                                    $inserthistorybalance['nominal'] = $totalharga;
                                    $inserthistorybalance['currentbalance'] = $currentbalance;
                                    $inserthistorybalance['depositid'] = $value->id;
                                    $inserthistorybalance['createdby'] = $value->userid;
                                    $inserthistorybalance['createddate'] = getCurrentDate();

                                    $this->historybalance->insert($inserthistorybalance);

                                    $updateUser = array();
                                    $updateUser['balance'] = $currentbalance + $totalharga;

                                    $this->msusers->update(array(
                                        'id' => $value->userid
                                    ), $updateUser);
                                }
                            }

                            if ($this->db->trans_status() === FALSE) {
                                throw new Exception('Error');
                            }

                            $this->db->trans_commit();
                        }
                    }

                    $paydisini = $this->mspaymentgateway->get(array(
                        'userid' => $value_user->id,
                        'type' => 'Payment Gateway',
                        'vendor' => 'PayDisini',
                        "(isdisabled IS NULL OR isdisabled = '0') =" => true
                    ))->row();

                    if ($paydisini != null) {
                        $detail = json_decode(stringEncryption('decrypt', $paydisini->detail));
                        if ($detail == null) continue;

                        $paydisini = new PayDisini($detail->apikey);

                        foreach ($deposit as $key => $value) {
                            $this->db->trans_begin();

                            if ($value->gatewayvendor != 'PayDisini') {
                                $this->db->trans_rollback();

                                continue;
                            }

                            $detailtransaction = $paydisini->status($value->paydisinicode);

                            if (isset($detailtransaction->success) && $detailtransaction->success) {
                                $transaction_data = $detailtransaction->data;

                                if ($transaction_data->status == 'Success') {
                                    $update = array();
                                    $update['status'] = 'Success';

                                    $this->deposits->update(array(
                                        'id' => $value->id
                                    ), $update);

                                    $this->send_notification($value->id, $value->merchantid, $value->phonenumber, 'deposit');

                                    $totalharga = 0;
                                    if ($value->isbonus == 1) {
                                        $totalharga = $value->nominal + $value->nominalbonus - ($value->fee ?? 0);
                                    } else {
                                        $totalharga = $value->nominal - ($value->fee ?? 0);
                                    }

                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value->userid,
                                        'type' => 'IN',
                                        'depositid' => $value->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value->userid, true);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value->userid;
                                        $inserthistorybalance['type'] = 'IN';
                                        $inserthistorybalance['nominal'] = $totalharga;
                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['depositid'] = $value->id;
                                        $inserthistorybalance['createdby'] = $value->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $updateUser = array();
                                        $updateUser['balance'] = $currentbalance + $totalharga;

                                        $this->msusers->update(array(
                                            'id' => $value->userid
                                        ), $updateUser);
                                    }
                                }
                            }

                            if ($this->db->trans_status() === FALSE) {
                                throw new Exception('Error');
                            }

                            $this->db->trans_commit();
                        }
                    }

                    $mobilenotification = $this->mobilenotification->get(array(
                        'a.userid' => $value_user->id,
                        'a.is_used' => null,
                        'DATE(a.createddate) =' => getCurrentDate('Y-m-d')
                    ));

                    if ($mobilenotification->num_rows() > 0) {
                        foreach ($deposit as $key => $value) {
                            $this->db->trans_begin();

                            if ($value->gatewayvendor != 'Notification Handler Services') {
                                $this->db->trans_rollback();

                                continue;
                            }

                            $found = false;
                            foreach ($mobilenotification->result() as $k_notification => $v_notification) {
                                if ($found) continue;
                                if ($v_notification->packagename != $value->apps_id) continue;

                                if ($v_notification->packagename == 'ovo.id') {
                                    $description = $v_notification->description;

                                    $startNominal = strpos($description, "sebesar ") + strlen("sebesar ");
                                    $endNominal = strpos($description, " melalui aplikasi");

                                    $nominal = substr($description, $startNominal, $endNominal - $startNominal);

                                    preg_match('/Rp (\d+(\.\d{3})*)/', $nominal, $matches);

                                    if (isset($matches[1])) {
                                        $nominal = str_replace('.', '', $matches[1]);

                                        if ($value->nominal == $nominal) {
                                            $found = true;

                                            $update = array();
                                            $update['is_used'] = 1;

                                            $this->mobilenotification->update(array(
                                                'id' => $v_notification->id
                                            ), $update);
                                        }
                                    }
                                } else if ($v_notification->packagename == 'id.dana') {
                                    $description = $v_notification->description;

                                    $startNominal = strpos($description, 'Rp') + 2;
                                    $endNominal = strpos($description, ' !');

                                    $nominal = substr($description, $startNominal, $endNominal - $startNominal);
                                    $nominal = str_replace('.', '', $nominal);

                                    if (!is_numeric($nominal)) {
                                        $endNominal = strpos($description, ' via');

                                        $nominal = substr($description, $startNominal, $endNominal - $startNominal);
                                        $nominal = str_replace('.', '', $nominal);
                                    }

                                    if ($value->nominal == $nominal) {
                                        $found = true;

                                        $update = array();
                                        $update['is_used'] = 1;

                                        $this->mobilenotification->update(array(
                                            'id' => $v_notification->id
                                        ), $update);
                                    }
                                } else if ($v_notification->packagename == 'com.gojek.resto') {
                                    $description = $v_notification->description;

                                    $patternNominal = '/Rp ([\d.]+)/';
                                    $patternTransactionId = '/ID transaksi: ([a-zA-Z0-9]+)/';

                                    if (preg_match($patternNominal, $description, $matchesNominal) && preg_match($patternTransactionId, $description, $matchesTransactionId)) {
                                        $nominal = $matchesNominal[1];
                                        $transactionid = $matchesTransactionId[1];

                                        $nominal = str_replace('.', '', $nominal);

                                        if ($value->nominal == $nominal) {
                                            $found = true;

                                            $update = array();
                                            $update['is_used'] = 1;

                                            $this->mobilenotification->update(array(
                                                'id' => $v_notification->id
                                            ), $update);
                                        }
                                    }
                                } else if ($v_notification->packagename == 'com.gojek.gopay') {
                                    $description = $v_notification->description;

                                    $pattern = '/([A-Za-z\s]+) transfer Rp([0-9\.,]+) ke kamu\..*/';

                                    if (preg_match($pattern, $description, $matches)) {
                                        $nama = $matches[1];
                                        $nominal = $matches[2];
                                        $nominal = preg_replace('/[^0-9]/', '', $nominal);

                                        if ($value->nominal == $nominal) {
                                            $found = true;

                                            $update = array();
                                            $update['is_used'] = 1;

                                            $this->mobilenotification->update(array(
                                                'id' => $v_notification->id
                                            ), $update);
                                        }
                                    }
                                }
                            }

                            if ($found) {
                                $update = array();
                                $update['status'] = 'Success';

                                $this->deposits->update(array(
                                    'id' => $value->id
                                ), $update);

                                $this->send_notification($value->id, $value->merchantid, $value->phonenumber, 'deposit');

                                $check_history_balance = $this->historybalance->total(array(
                                    'userid' => $value->userid,
                                    'type' => 'IN',
                                    'depositid' => $value->id
                                ));

                                if ($check_history_balance == 0) {
                                    $currentbalance = getCurrentBalance($value->userid, true);

                                    $balance = 0;
                                    if ($value->isbonus == 1) {
                                        $balance = $value->nominal + $value->nominalbonus - ($value->fee ?? 0);
                                    } else {
                                        $balance = $value->nominal - ($value->fee ?? 0);
                                    }

                                    $inserthistorybalance = array();
                                    $inserthistorybalance['userid'] = $value->userid;
                                    $inserthistorybalance['type'] = 'IN';
                                    $inserthistorybalance['nominal'] = $balance;
                                    $inserthistorybalance['currentbalance'] = $currentbalance;
                                    $inserthistorybalance['depositid'] = $value->id;
                                    $inserthistorybalance['createdby'] = $value->userid;
                                    $inserthistorybalance['createddate'] = getCurrentDate();

                                    $this->historybalance->insert($inserthistorybalance);

                                    $updateUser = array();
                                    $updateUser['balance'] = $currentbalance + $balance;

                                    $this->msusers->update(array(
                                        'id' => $value->userid
                                    ), $updateUser);
                                }
                            }

                            if ($this->db->trans_status() === FALSE) {
                                throw new Exception('Error');
                            }

                            $this->db->trans_commit();
                        }
                    }
                }

                if (count($transaction) > 0) {
                    $mobilenotification = $this->mobilenotification->get(array(
                        'a.userid' => $value_user->id,
                        'a.is_used' => null,
                        'DATE(a.createddate) =' => getCurrentDate('Y-m-d')
                    ));

                    if ($mobilenotification->num_rows() > 0) {
                        foreach ($transaction as $key => $value) {
                            $this->db->trans_begin();

                            if ($value->gatewayvendor != 'Notification Handler Services') {
                                $this->db->trans_rollback();

                                continue;
                            }

                            $found = false;
                            foreach ($mobilenotification->result() as $k_notification => $v_notification) {
                                if ($found) continue;
                                if ($v_notification->packagename != $value->apps_id) continue;

                                if ($v_notification->packagename == 'ovo.id') {
                                    $description = $v_notification->description;

                                    $startNominal = strpos($description, "sebesar ") + strlen("sebesar ");
                                    $endNominal = strpos($description, " melalui aplikasi");

                                    $nominal = substr($description, $startNominal, $endNominal - $startNominal);

                                    preg_match('/Rp (\d+(\.\d{3})*)/', $nominal, $matches);

                                    if (isset($matches[1])) {
                                        $nominal = str_replace('.', '', $matches[1]);

                                        if ($value->price == $nominal) {
                                            $found = true;

                                            $update = array();
                                            $update['is_used'] = 1;

                                            $this->mobilenotification->update(array(
                                                'id' => $v_notification->id
                                            ), $update);
                                        }
                                    }
                                } else if ($v_notification->packagename == 'id.dana') {
                                    $description = $v_notification->description;

                                    $startNominal = strpos($description, 'Rp') + 2;
                                    $endNominal = strpos($description, ' !');

                                    $nominal = substr($description, $startNominal, $endNominal - $startNominal);
                                    $nominal = str_replace('.', '', $nominal);

                                    if (!is_numeric($nominal)) {
                                        $endNominal = strpos($description, ' via');

                                        $nominal = substr($description, $startNominal, $endNominal - $startNominal);
                                        $nominal = str_replace('.', '', $nominal);
                                    }

                                    if ($value->price == $nominal) {
                                        $found = true;

                                        $update = array();
                                        $update['is_used'] = 1;

                                        $this->mobilenotification->update(array(
                                            'id' => $v_notification->id
                                        ), $update);
                                    }
                                } else if ($v_notification->packagename == 'com.gojek.resto') {
                                    $description = $v_notification->description;

                                    $patternNominal = '/Rp ([\d.]+)/';
                                    $patternTransactionId = '/ID transaksi: ([a-zA-Z0-9]+)/';

                                    if (preg_match($patternNominal, $description, $matchesNominal) && preg_match($patternTransactionId, $description, $matchesTransactionId)) {
                                        $nominal = $matchesNominal[1];
                                        $transactionid = $matchesTransactionId[1];

                                        $nominal = str_replace('.', '', $nominal);

                                        if ($value->price == $nominal) {
                                            $found = true;

                                            $update = array();
                                            $update['is_used'] = 1;

                                            $this->mobilenotification->update(array(
                                                'id' => $v_notification->id
                                            ), $update);
                                        }
                                    }
                                } else if ($v_notification->packagename == 'com.gojek.gopay') {
                                    $description = $v_notification->description;

                                    $pattern = '/([A-Za-z\s]+) transfer Rp([0-9\.,]+) ke kamu\..*/';

                                    if (preg_match($pattern, $description, $matches)) {
                                        $nama = $matches[1];
                                        $nominal = $matches[2];
                                        $nominal = preg_replace('/[^0-9]/', '', $nominal);

                                        if ($value->price == $nominal) {
                                            $found = true;

                                            $update = array();
                                            $update['is_used'] = 1;

                                            $this->mobilenotification->update(array(
                                                'id' => $v_notification->id
                                            ), $update);
                                        }
                                    }
                                }
                            }

                            if ($found) {
                                $update = array();
                                $update['status_payment'] = 'sukses';

                                $this->trorder->update(array(
                                    'id' => $value->id
                                ), $update);

                                pullTransaction($value->userid ?? $value->clientip);
                            }

                            if ($this->db->trans_status() === FALSE) {
                                throw new Exception('Error');
                            }

                            $this->db->trans_commit();
                        }
                    }
                }
            }
        } catch (Exception $ex) {
            $this->db->trans_rollback();
        }
    }

    public function product_information()
    {
        $threedaysago = date('Y-m-d H:i:s', strtotime("-3 days"));

        $this->productpricelog->delete(array(
            'createddate <=' => $threedaysago
        ));
    }

    public function member_history_login()
    {
        $sevendaysago = date('Y-m-d H:i:s', strtotime("-7 days"));

        $this->loginactivity->delete(array(
            'createddate <=' => $sevendaysago
        ));
    }

    public function account_errorlog()
    {
        $sevendaysago = date('Y-m-d H:i:s', strtotime("-7 days"));

        $this->errorlogger->delete(array(
            'createddate <=' => $sevendaysago
        ));
    }

    public function member_invoice_expired()
    {
        $invoice = $this->invoices->select('id, userid, createddate')
            ->result(array(
                'status' => 'Pending'
            ));

        foreach ($invoice as $key => $value) {
            $fourteen_days = date('Y-m-d H:i:s', strtotime("$value->createddate +14 days"));

            if (getCurrentDate() >= $fourteen_days) {
                $update = array();
                $update['status'] = 'Cancel';

                $this->invoices->update(array(
                    'id' => $value->id
                ), $update);

                $this->msusers->update(array(
                    'id' => $value->userid
                ), array(
                    'licenseid' => null
                ));
            }
        }

        $users = $this->msusers->result(array(
            'expireddate <' => getCurrentDate(),
            'licenseid !=' => null,
        ));

        foreach ($users as $key => $value) {
            $this->msusers->update(array(
                'id' => $value->id
            ), array(
                'licenseid' => null
            ));
        }
    }

    public function member_invoice()
    {
        $users = $this->msusers->select('a.id, a.expireddate, a.licenseid')
            ->result(array(
                'a.merchantid' => null,
                'a.licenseid !=' => null,
            ));

        foreach ($users as $key => $value) {
            $sevendaysago = date('Y-m-d H:i:s', strtotime("$value->expireddate -7 days"));

            if (getCurrentDate() >= $sevendaysago) {
                $get_invoice = $this->invoices->total(array(
                    'userid' => $value->id,
                    'invoicetype' => 'Extend License',
                    'status' => 'Pending'
                ));

                $license = $this->mslicense->get(array(
                    'a.id' => $value->licenseid
                ));

                if ($get_invoice == 0 && $license->num_rows() > 0) {
                    $license_row = $license->row();

                    $invoice = array();
                    $invoice['userid'] = $value->id;
                    $invoice['invoicecode'] = generateTransactionNumber('INV');
                    $invoice['description'] = 'Perpanjangan Paket ' . $license_row->name . ' hingga ' . date('d F Y', strtotime($value->expireddate . ' +1 month'));
                    $invoice['nominal'] = $license_row->price;
                    $invoice['note'] = "Silahkan transfer sebesar Rp " . IDR($invoice['nominal']) . ",- Ke Rekening: BCA, no. **********, a.n. MOCH ARIZAL FAUZI";
                    $invoice['invoicetype'] = 'Extend License';
                    $invoice['targetuserid'] = $value->id;
                    $invoice['packageid'] = $license_row->id;
                    $invoice['status'] = 'Pending';
                    $invoice['createddate'] = getCurrentDate();
                    $invoice['createdby'] = $value->id;

                    $this->invoices->insert($invoice);
                }
            }
        }
    }

    public function member_domain_request()
    {
        $request = $this->domainrequestns->result(array(
            'status' => 'Pending'
        ));

        $cloudflare = new Cloudflare(String_Helper::CLOUDLFARE_ACCOUNT, String_Helper::CLOUDFLARE_TOKEN);

        foreach ($request as $key => $value) {
            $zones = $cloudflare->zones(array('name' => $value->domain));

            if (count($zones->result) > 0) {
                $update = array();
                $update['status'] = 'Success';
                $update['updateddate'] = getCurrentDate();
                $update['updatedby'] = $value->userid;

                $this->domainrequestns->update(array(
                    'id' => $value->id
                ), $update);
            } else {
                $create = $cloudflare->create_zone($value->domain);

                if ($create->success) {
                    $zoneid = $create->result->id;

                    // create A record
                    $cloudflare->create_dns_record($zoneid, "A", $value->domain, "36.50.77.84", 1, false);

                    // create CNAME www
                    $cloudflare->create_dns_record($zoneid, "CNAME", "www." . $value->domain, $value->domain, 1, false);

                    // change SSL settings to Full (Strict)
                    $cloudflare->change_ssl_setting($zoneid, "strict");

                    // Allow API URL
                    // $cloudflare->create_firewall_rules($zoneid, "Allow API", "allow", array(
                    //     'expression' => '(http.request.uri eq "/sitemap") or (http.request.uri eq "/callback/tripay") or (http.request.uri eq "/callback/digiflazz")',
                    // ), 1);

                    // Managed Challenge
                    // $cloudflare->create_firewall_rules($zoneid, "Main", "managed_challenge", array(
                    //     'expression' => '(http.host eq "' . $value->domain . '" and not cf.client.bot)',
                    // ), 2);

                    $update = array();
                    $update['status'] = 'Success';
                    $update['updateddate'] = getCurrentDate();
                    $update['updatedby'] = $value->userid;

                    $this->domainrequestns->update(array(
                        'id' => $value->id
                    ), $update);
                } else {
                    log_message('error', 'Domain Request Error: ' . $value->domain . ' - ' . $create->errors[0]->message);
                }
            }
        }
    }

    private function send_notification($orderid, $userid, $phonenumber, $type = 'order')
    {
        try {
            // Kirim Firebase notification untuk order
            if ($type == 'order') {
                sendFirebaseNotificationOrder($orderid, $userid);
            }

            $apikeys_whatsapp = $this->apikeys_whatsapp->get(array(
                'userid' => $userid,
            ));

            if ($apikeys_whatsapp->num_rows() == 0) {
                return false;
            }

            $row = $apikeys_whatsapp->row();

            $wablasingateway = new WABlasinGateway(stringEncryption('decrypt', $row->apikey), stringEncryption('decrypt', $row->devicecode));

            if ($type == 'order') {
                $messagenotification = replaceParameterNotification($orderid, $userid);
            } else {
                $messagenotification = replaceParameterNotificationDeposit($orderid, $userid);
            }

            if ($messagenotification != null && $phonenumber != null) {
                $phonenumber = changePrefixPhone($phonenumber);

                $send = $wablasingateway->sendMessage($phonenumber, $messagenotification);

                if (!isset($send->RESULT) || (isset($send->RESULT) && $send->RESULT != 'OK')) {
                    log_message_user('error', '[WHATSAPP SEND MESSAGE] Send notification failed: ' . json_encode($send), $userid);
                }

                return true;
            }

            return false;
        } catch (Exception $ex) {
            return false;
        }
    }

    public function product_revalidate()
    {
        $merchant = $this->msusers->result(array(
            'merchantid' => null,
            'licenseid !=' => null,
            'expireddate >' => getCurrentDate(),
            '(multivendor IS NULL OR multivendor = 0) =' => true
        ));

        foreach ($merchant as $key => $value) {
            $currentvendor_ppob = getCurrentVendor('PPOB', $value->id);
            $currentvendor_smm = getCurrentVendor('SMM', $value->id);

            if ($currentvendor_ppob != null) {
                $product = $this->msproduct->select('code')
                    ->group_by('code')
                    ->having('count(code) > 1')
                    ->result(array(
                        'userid' => $value->id,
                        'vendor' => $currentvendor_ppob,
                    ));

                foreach ($product as $k_product => $v_product) {
                    $duplicate_product = $this->msproduct->result(array(
                        'userid' => $value->id,
                        'vendor' => $currentvendor_ppob,
                        'code' => $v_product->code
                    ));

                    $productid = array();
                    foreach ($duplicate_product as $k_duplicate_product => $v_duplicate_product) {
                        if ($k_duplicate_product == 0) continue;

                        $productid[] = $v_duplicate_product->id;
                    }

                    if (count($productid) > 0) {
                        $this->msproduct->where_in('id', $productid)->delete();
                    }
                }
            }

            if ($currentvendor_smm != null) {
                $product = $this->msproduct->select('code')
                    ->group_by('code')
                    ->having('count(code) > 1')
                    ->result(array(
                        'userid' => $value->id,
                        'vendor' => $currentvendor_smm,
                    ));

                foreach ($product as $k_product => $v_product) {
                    $duplicate_product = $this->msproduct->result(array(
                        'userid' => $value->id,
                        'vendor' => $currentvendor_smm,
                        'code' => $v_product->code
                    ));

                    $productid = array();
                    foreach ($duplicate_product as $k_duplicate_product => $v_duplicate_product) {
                        if ($k_duplicate_product == 0) continue;

                        $productid[] = $v_duplicate_product->id;
                    }

                    if (count($productid) > 0) {
                        $this->msproduct->where_in('id', $productid)->delete();
                    }
                }
            }
        }
    }

    public function member_role_default()
    {
        $member = $this->msusers->result(array(
            'merchantid' => null
        ));

        foreach ($member as $key => $value) {
            $role = $this->msrole->total(array(
                'createdby' => $value->id,
                'isdefault' => 1
            ));

            if ($role == 0) {
                $insert = array();
                $insert['rolename'] = 'User';
                $insert['isdefault'] = 1;
                $insert['createddate'] = getCurrentDate();
                $insert['createdby'] = $value->id;
                $insert['updateddate'] = getCurrentDate();
                $insert['updatedby'] = $value->id;
                $insert['discounttype'] = 'Simple';

                $this->msrole->insert($insert);
            }
        }
    }

    public function product_delete()
    {
        $member = $this->msusers->result(array(
            'merchantid' => null
        ));

        foreach ($member as $key => $value) {
            $expireddate = date('Y-m-d H:i:s', strtotime("-1 month"));

            if (($value->expireddate == null) || ($value->expireddate != null && $value->expireddate < $expireddate)) {
                $this->msproduct->delete(array(
                    'userid' => $value->id
                ));
            }
        }
    }

    public function build_application()
    {
        $queue = $this->queuebuildapp->select('a.id AS queueid, b.*, c.color, c.appname, c.keystore_alias, c.packagename, c.id AS customappid, a.applicationtype')
            ->join('msusers b', 'b.id = a.userid')
            ->join('customapp c', 'c.userid = b.id')
            ->result(array(
                'status' => 'Pending',
            ));

        foreach ($queue as $key => $value) {
            // get company icon name from url
            $companyicon = explode('/', $value->companyicon ?? '');
            $companyicon = end($companyicon);

            if ($value->companyicon == null) {
                $update = array();
                $update['status'] = 'Failed';
                $update['reason'] = 'Company Icon is null';

                $this->queuebuildapp->update(array(
                    'id' => $value->queueid
                ), $update);
            } else if (!file_exists('./uploads/' . $companyicon)) {
                $update = array();
                $update['status'] = 'Failed';
                $update['reason'] = 'Company Icon not found';

                $this->queuebuildapp->update(array(
                    'id' => $value->queueid
                ), $update);
            }

            $img_path = "images/company/$companyicon";
            $img_url = $value->companyicon;
            $color = strtoupper(str_replace('#', '', $value->color));
            $company_primary_color = "0xFF$color";
            $company_name = $value->appname;
            $company_email = $value->email;
            $company_phone = $value->phonenumber;
            $company_address = $value->companyaddress;
            $company_base_address = "https://$value->domain/";
            $keystore_path = "$value->keystore_alias.jks";
            $keystore_alias = $value->keystore_alias;
            $app_package_name = "com.serverppobsmm.$value->packagename";
            $customappid = $value->customappid;
            $applicationtype = $value->applicationtype;

            $buildApp = buildApp($img_path, $img_url, $company_primary_color, $company_name, $company_email, $company_phone, $company_address, $company_base_address, $keystore_path, $keystore_alias, $app_package_name, $customappid, $applicationtype);

            if (isset($buildApp->success) && $buildApp->success) {
                $update = array();
                $update['status'] = 'Success';
                $update['reason'] = null;

                $this->queuebuildapp->update(array(
                    'id' => $value->queueid
                ), $update);
            } else {
                $update = array();
                $update['status'] = 'Failed';
                $update['reason'] = $buildApp->error ?? 'Unknown error';

                $this->queuebuildapp->update(array(
                    'id' => $value->queueid
                ), $update);
            }
        }
    }

    public function multivendor_transaction_queue_processing()
    {
        $users = $this->msusers->select('a.*, b.id AS vendorid, b.default_config, b.balance')
            ->join('msvendor b', 'b.id = a.vendorid')
            ->result(array(
                'a.merchantid' => null,
                'a.expireddate >=' => getCurrentDate(),
                'a.multivendor =' => 1
            ));

        foreach ($users as $key => $value) {
            $vendordetail = $this->msvendordetail->get(array(
                'vendorid' => $value->vendorid,
                'apitype' => 'Status'
            ));

            if ($vendordetail->num_rows() == 0) continue;

            $vendor_order = $vendordetail->row();

            $response_indicator = json_decode($vendor_order->response_indicator);
            $response_setting = json_decode($vendor_order->response_setting);

            $dynamicvendor = new DynamicVendor($value->vendorid, json_decode($value->default_config, true));

            $transaction = $this->trorder->select('a.*, b.code, b.vendorprice, b.category, b.oldcategoryname, b.brand, b.type AS producttype, b.ismanualadd')
                ->join('msproduct b', 'b.id = a.serviceid')
                ->result(array(
                    'a.merchantid_order' => $value->id,
                    'a.servercode' => null,
                    'a.paymenttype !=' => null,
                    'a.status_payment' => 'sukses',
                    'a.status' => 'pending',
                    "(a.queuetransaction IS NULL OR a.queuetransaction = 0) =" => true,
                    'a.vendorid' => $value->vendorid
                ));

            foreach ($transaction as $key_order => $value_order) {
                $this->db->trans_begin();

                if ($value_order->type == 'SMM') {
                    $vendorprice = $value_order->vendorprice;
                    $priceperitem = $vendorprice / 1000;

                    $vendorprice_total = $priceperitem * $value_order->qty;
                } else {
                    $vendorprice_total = $value_order->vendorprice;
                }

                if ($value_order->balance < $vendorprice_total) {
                    $this->db->trans_rollback();
                    continue;
                }

                $order = $dynamicvendor->order($value_order->id);

                if (
                    ($response_indicator->index == null && isset($order[$response_indicator->key]) && (
                        $response_indicator->datatype == 'string' && $order[$response_indicator->key] == $response_indicator->value
                        || $response_indicator->datatype == 'number' && $order[$response_indicator->key] == $response_indicator->value
                        || $response_indicator->datatype == 'boolean' && $order[$response_indicator->key] == $response_indicator->value
                        || $response_indicator->datatype == 'exists' && isset($order[$response_indicator->key])
                    ))
                    || ($response_indicator->index != null && isset($order[$response_indicator->index][$response_indicator->key]) && (
                        $response_indicator->datatype == 'string' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                        || $response_indicator->datatype == 'number' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                        || $response_indicator->datatype == 'boolean' && $order[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                        || $response_indicator->datatype == 'exists' && isset($order[$response_indicator->index][$response_indicator->key])
                    ))
                ) {
                    $var_referenceid = $response_setting->referenceid ?? null;
                    $var_price = $response_setting->price ?? null;
                    $var_status = $response_setting->status ?? null;
                    $var_note = $response_setting->note ?? null;
                    $var_sn = $response_setting->sn ?? null;
                    $var_errorrefund = $response_setting->errorrefund ?? null;

                    $exploding_errorrefund = explode('[#]', strtolower($var_errorrefund ?? ''));

                    if ($response_setting->index != null) {
                        $order = $order[$response_setting->index] ?? null;
                    }

                    $referenceid = $var_referenceid != null ? ($order[$var_referenceid] ?? null) : null;
                    $price = $var_price != null ? ($order[$var_price] ?? null) : null;
                    $status = $var_status != null ? ($order[$var_status] ?? null) : null;
                    $note = $var_note != null ? ($order[$var_note] ?? null) : null;
                    $sn = $var_sn != null ? ($order[$var_sn] ?? null) : null;

                    if ($status != null) {
                        if (in_array($status, $exploding_errorrefund)) {
                            log_message_user('error', '[MULTIVENDOR TRANSACTION QUEUE PROCESSING] Failed to parsing error refund, response: ' . json_encode($order), $value->id);

                            if ($value_order->userid != null) {
                                $check_history_balance = $this->historybalance->total(array(
                                    'userid' => $value_order->userid,
                                    'type' => 'IN',
                                    'orderid' => $value_order->id
                                ));

                                if ($check_history_balance == 0) {
                                    $currentbalance = getCurrentBalance($value_order->userid, true);

                                    $inserthistorybalance = array();
                                    $inserthistorybalance['userid'] = $value_order->userid;
                                    $inserthistorybalance['type'] = 'IN';

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                    } else {
                                        $inserthistorybalance['nominal'] = $value_order->price;
                                    }

                                    $inserthistorybalance['currentbalance'] = $currentbalance;
                                    $inserthistorybalance['orderid'] = $value_order->id;
                                    $inserthistorybalance['createdby'] = $value_order->userid;
                                    $inserthistorybalance['createddate'] = getCurrentDate();

                                    $this->historybalance->insert($inserthistorybalance);

                                    $updateUser = array();

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $updateUser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                    } else {
                                        $updateUser['balance'] = $currentbalance + $value_order->price;
                                    }

                                    $this->msusers->update(
                                        array(
                                            'id' => $value_order->userid
                                        ),
                                        $updateUser
                                    );
                                }
                            }

                            $update = array();
                            $update['status'] = 'gagal';
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id
                            ), $update);
                        }
                    } else {
                        if ($var_status == null) {
                            $status = 'pending';
                        } else if ($var_status != null && $status == null) {
                            log_message_user('error', '[MULTIVENDOR TRANSACTION QUEUE PROCESSING] Failed to parsing status, response : ' . json_encode($order), $value->id);

                            if ($value_order->userid != null) {
                                $check_history_balance = $this->historybalance->total(array(
                                    'userid' => $value_order->userid,
                                    'type' => 'IN',
                                    'orderid' => $value_order->id
                                ));

                                if ($check_history_balance == 0) {
                                    $currentbalance = getCurrentBalance($value_order->userid, true);

                                    $inserthistorybalance = array();
                                    $inserthistorybalance['userid'] = $value_order->userid;
                                    $inserthistorybalance['type'] = 'IN';

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                    } else {
                                        $inserthistorybalance['nominal'] = $value_order->price;
                                    }

                                    $inserthistorybalance['currentbalance'] = $currentbalance;
                                    $inserthistorybalance['orderid'] = $value_order->id;
                                    $inserthistorybalance['createdby'] = $value_order->userid;
                                    $inserthistorybalance['createddate'] = getCurrentDate();

                                    $this->historybalance->insert($inserthistorybalance);

                                    $updateUser = array();

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $updateUser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                    } else {
                                        $updateUser['balance'] = $currentbalance + $value_order->price;
                                    }

                                    $this->msusers->update(
                                        array(
                                            'id' => $value_order->userid
                                        ),
                                        $updateUser
                                    );
                                }
                            }

                            $update = array();
                            $update['status'] = 'gagal';
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id
                            ), $update);
                        }
                    }

                    $update = array();
                    $update['jsonresponse'] = json_encode($order);

                    if ($referenceid != null) {
                        $update['servercode'] = $referenceid;
                    }

                    if ($price != null) {
                        $update['price'] = $price;
                    }

                    if ($status != null) {
                        $update['status'] = $status;
                    }

                    if ($note != null) {
                        $update['note'] = $note;
                    }

                    if ($sn != null) {
                        $update['sn'] = $sn;
                    }

                    $this->trorder->update(array(
                        'id' => $value_order->id
                    ), $update);
                } else {
                    if ($value_order->userid != null) {
                        $check_history_balance = $this->historybalance->total(array(
                            'userid' => $value_order->userid,
                            'type' => 'IN',
                            'orderid' => $value_order->id
                        ));

                        if ($check_history_balance == 0) {
                            $currentbalance = getCurrentBalance($value_order->userid, true);

                            $inserthistorybalance = array();
                            $inserthistorybalance['userid'] = $value_order->userid;
                            $inserthistorybalance['type'] = 'IN';

                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                            } else {
                                $inserthistorybalance['nominal'] = $value_order->price;
                            }

                            $inserthistorybalance['currentbalance'] = $currentbalance;
                            $inserthistorybalance['orderid'] = $value_order->id;
                            $inserthistorybalance['createdby'] = $value_order->userid;
                            $inserthistorybalance['createddate'] = getCurrentDate();

                            $this->historybalance->insert($inserthistorybalance);

                            $updateUser = array();

                            if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                $updateUser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                            } else {
                                $updateUser['balance'] = $currentbalance + $value_order->price;
                            }

                            $this->msusers->update(array(
                                'id' => $value_order->userid
                            ), $updateUser);
                        }
                    }

                    $update = array();
                    $update['status'] = 'gagal';
                    $update['updateddate'] = getCurrentDate();

                    $this->trorder->update(array(
                        'id' => $value_order->id
                    ), $update);

                    pullTransaction($value_order->userid ?? $value_order->clientip);

                    if ($order != null) {
                        log_message_user('error', '[MULTIVENDOR TRANSACTION QUEUE PROCESSING] Order failed: ' . json_encode($order), $value->id);
                    }
                }

                if ($this->db->trans_status() === FALSE) {
                    $this->db->trans_rollback();
                }

                $this->db->trans_commit();
            }
        }
    }

    public function member_product_transaction_queue_processing()
    {
        $member = $this->msusers->select('a.*')
            ->result(array(
                'a.merchantid' => null,
                'a.expireddate >=' => getCurrentDate(),
                '(a.multivendor IS NULL OR a.multivendor = 0) =' => true
            ));

        foreach ($member as $key => $value) {
            $apikeysppob = getCurrentAPIKeys('PPOB', $value->id);
            $apikeyssmm = getCurrentAPIKeys('SMM', $value->id);

            if ($apikeysppob == null && $apikeyssmm == null) continue;

            $transaction = $this->trorder->select('a.*, b.code, b.vendorprice, b.category, b.oldcategoryname, b.brand, b.type AS producttype, b.ismanualadd')
                ->join('msproduct b', 'b.id = a.serviceid')
                ->result(array(
                    'a.merchantid_order' => $value->id,
                    'a.servercode' => null,
                    'a.paymenttype !=' => null,
                    'a.status_payment' => 'sukses',
                    'a.status' => 'pending',
                    "(a.queuetransaction IS NULL OR a.queuetransaction = 0) =" => true,
                    'a.vendorid' => null
                ));

            if (count($transaction) == 0) continue;

            if ($apikeysppob != null) {
                if ($apikeysppob->vendor == 'Digiflazz') {
                    $ppobobject = new Digiflazz(stringEncryption('decrypt', $apikeysppob->usercode), stringEncryption('decrypt', $apikeysppob->apikey));
                } else if ($apikeysppob->vendor == 'VIPayment') {
                    $ppobobject = new VIPayment(stringEncryption('decrypt', $apikeysppob->usercode), stringEncryption('decrypt', $apikeysppob->apikey));
                }
            }

            if ($apikeyssmm != null) {
                if ($apikeyssmm->vendor == 'BuzzerPanel') {
                    $smmobject = new BuzzerPanel(stringEncryption('decrypt', $apikeyssmm->apikey), stringEncryption('decrypt', $apikeyssmm->secretkey));
                } else if ($apikeyssmm->vendor == 'MedanPedia') {
                    $smmobject = new MedanPedia(stringEncryption('decrypt', $apikeyssmm->usercode), stringEncryption('decrypt', $apikeyssmm->apikey));
                } else if ($apikeyssmm->vendor == 'IrvanKede') {
                    $smmobject = new IrvanKede(stringEncryption('decrypt', $apikeyssmm->usercode), stringEncryption('decrypt', $apikeyssmm->apikey));
                } else if ($apikeyssmm->vendor == 'DailyPanel') {
                    $smmobject = new DailyPanel(stringEncryption('decrypt', $apikeyssmm->apikey), stringEncryption('decrypt', $apikeyssmm->secretkey));
                } else if ($apikeyssmm->vendor == 'WStore') {
                    $smmobject = new WStore(stringEncryption('decrypt', $apikeyssmm->usercode), stringEncryption('decrypt', $apikeyssmm->apikey), stringEncryption('decrypt', $apikeyssmm->secretkey));
                } else if ($apikeyssmm->vendor == 'UNDRCTRL') {
                    $smmobject = new UNDRCTRL(stringEncryption('decrypt', $apikeyssmm->apikey));
                } else if ($apikeyssmm->vendor == 'SosmedOnline') {
                    $smmobject = new SosmedOnline(stringEncryption('decrypt', $apikeyssmm->usercode), stringEncryption('decrypt', $apikeyssmm->apikey));
                } else if ($apikeyssmm->vendor == 'SosmedOnlineVIP') {
                    $smmobject = new SosmedOnlineVIP(stringEncryption('decrypt', $apikeyssmm->usercode), stringEncryption('decrypt', $apikeyssmm->apikey));
                } else if ($apikeyssmm->vendor == 'DjuraganSosmed') {
                    $smmobject = new DjuraganSosmed(stringEncryption('decrypt', $apikeyssmm->apikey));
                } else if ($apikeyssmm->vendor == 'SMMRaja') {
                    $smmobject = new SMMRaja(stringEncryption('decrypt', $apikeyssmm->apikey));
                } else if ($apikeyssmm->vendor == 'SMMIllusion') {
                    $smmobject = new SMMIllusion(stringEncryption('decrypt', $apikeyssmm->apikey));
                } else if ($apikeyssmm->vendor == 'V1Pedia') {
                    $smmobject = new V1Pedia(stringEncryption('decrypt', $apikeyssmm->usercode), stringEncryption('decrypt', $apikeyssmm->apikey), stringEncryption('decrypt', $apikeyssmm->secretkey));
                }
            }

            foreach ($transaction as $key_order => $value_order) {
                if ($value_order->ismanualadd == 1 && $value_order->code == null) continue;

                $this->db->trans_begin();

                if ($value_order->type == 'PPOB' && $apikeysppob != null) {
                    if ($apikeysppob->balance < $value_order->vendorprice) {
                        $this->db->trans_rollback();
                        continue;
                    }

                    if ($value_order->vendor == 'Digiflazz') {
                        // Untuk mencegah duplicate order, maka disini kita generate servercode tanpa melakukan request ke API (Request API digabung pada saat proses pengecekan status transaksi)
                        $servercode = generateTransactionNumber('ORDER');
                        $update = array();
                        $update['servercode'] = $servercode;
                        $update['updateddate'] = getCurrentDate();

                        $this->trorder->update(array(
                            'id' => $value_order->id,
                        ), $update);
                    } else if ($value_order->vendor == 'VIPayment') {
                        if ($value_order->producttype == 'game') {
                            if (preg_match('/(mobile legends)|(mobilelegends)|(mobile legend)|(mobilelegend)/', strtolower($value_order->brand))) {
                                $order = $ppobobject->order_game($value_order->code, $value_order->target, $value_order->zoneid);
                            } else {
                                $order = $ppobobject->order_game($value_order->code, $value_order->target, null);
                            }
                        } else {
                            $order = $ppobobject->order_prepaid($value_order->code, $value_order->target);
                        }

                        if (isset($order->result) && $order->result) {
                            $update = array();
                            $update['servercode'] = $order->data->trxid;
                            $update['jsonresponse'] = json_encode($order);
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id
                            ), $update);
                        } else {
                            if ($value_order->userid != null) {
                                $check_history_balance = $this->historybalance->total(array(
                                    'userid' => $value_order->userid,
                                    'type' => 'IN',
                                    'orderid' => $value_order->id,
                                ));

                                if ($check_history_balance == 0) {
                                    $currentbalance = getCurrentBalance($value_order->userid, true);

                                    $inserthistorybalance = array();
                                    $inserthistorybalance['userid'] = $value_order->userid;
                                    $inserthistorybalance['type'] = 'IN';

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                    } else {
                                        $inserthistorybalance['nominal'] = $value_order->price;
                                    }

                                    $inserthistorybalance['currentbalance'] = $currentbalance;
                                    $inserthistorybalance['orderid'] = $value_order->id;
                                    $inserthistorybalance['createdby'] = $value_order->userid;
                                    $inserthistorybalance['createddate'] = getCurrentDate();

                                    $this->historybalance->insert($inserthistorybalance);

                                    $updateuser = array();

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $updateuser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                    } else {
                                        $updateuser['balance'] = $currentbalance + $value_order->price;
                                    }

                                    $this->msusers->update(array(
                                        'id' => $value_order->userid
                                    ), $updateuser);
                                }
                            }

                            $update = array();
                            $update['status'] = 'gagal';
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id
                            ), $update);

                            pullTransaction($value_order->userid ?? $value_order->clientip);

                            if ($order != null) {
                                log_message_user('error', '[VIPAYMENT ORDER PRODUCT] Response: ' . json_encode($order), $value->id);
                            }
                        }
                    }
                } else if ($value_order->type == 'SMM' && $apikeyssmm != null) {
                    $vendorprice = $value_order->vendorprice;
                    $priceperitem = $vendorprice / 1000;

                    $vendorprice_total = $priceperitem * $value_order->qty;

                    if ($apikeyssmm->balance < $vendorprice_total) {
                        $this->db->trans_rollback();
                        continue;
                    }

                    if ($value_order->vendor == 'BuzzerPanel') {
                        $order = $smmobject->order($value_order->code, $value_order->target, $value_order->qty);

                        if (isset($order->status) && $order->status) {
                            $update = array();
                            $update['servercode'] = $order->data->id;
                            $update['jsonresponse'] = json_encode($order);
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id
                            ), $update);
                        } else {
                            if ($value_order->userid != null) {
                                $check_history_balance = $this->historybalance->total(array(
                                    'userid' => $value_order->userid,
                                    'type' => 'IN',
                                    'orderid' => $value_order->id,
                                ));

                                if ($check_history_balance == 0) {
                                    $currentbalance = getCurrentBalance($value_order->userid, true);

                                    $inserthistorybalance = array();
                                    $inserthistorybalance['userid'] = $value_order->userid;
                                    $inserthistorybalance['type'] = 'IN';

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                    } else {
                                        $inserthistorybalance['nominal'] = $value_order->price;
                                    }

                                    $inserthistorybalance['currentbalance'] = $currentbalance;
                                    $inserthistorybalance['orderid'] = $value_order->id;
                                    $inserthistorybalance['createdby'] = $value_order->userid;
                                    $inserthistorybalance['createddate'] = getCurrentDate();

                                    $this->historybalance->insert($inserthistorybalance);

                                    $updateuser = array();

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $updateuser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                    } else {
                                        $updateuser['balance'] = $currentbalance + $value_order->price;
                                    }

                                    $this->msusers->update(array(
                                        'id' => $value_order->userid
                                    ), $updateuser);
                                }
                            }

                            $update = array();
                            $update['status'] = 'gagal';
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id
                            ), $update);

                            pullTransaction($value_order->userid ?? $value_order->clientip);

                            if ($order != null) {
                                log_message_user('error', '[BUZZERPANEL ORDER PRODUCT] Response: ' . json_encode($order), $value->id);
                            }
                        }
                    } else if ($value_order->vendor == 'MedanPedia') {
                        $order = $smmobject->order($value_order->code, $value_order->target, $value_order->qty, $value_order->additional ? $value_order->additional : '');

                        if (isset($order->status) && $order->status) {
                            $update = array();
                            $update['servercode'] = $order->data->id;
                            $update['jsonresponse'] = json_encode($order);
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id
                            ), $update);
                        } else {
                            if ($value_order->userid != null) {
                                $check_history_balance = $this->historybalance->total(array(
                                    'userid' => $value_order->userid,
                                    'type' => 'IN',
                                    'orderid' => $value_order->id,
                                ));

                                if ($check_history_balance == 0) {
                                    $currentbalance = getCurrentBalance($value_order->userid, true);

                                    $inserthistorybalance = array();
                                    $inserthistorybalance['userid'] = $value_order->userid;
                                    $inserthistorybalance['type'] = 'IN';

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                    } else {
                                        $inserthistorybalance['nominal'] = $value_order->price;
                                    }

                                    $inserthistorybalance['currentbalance'] = $currentbalance;
                                    $inserthistorybalance['orderid'] = $value_order->id;
                                    $inserthistorybalance['createdby'] = $value_order->userid;
                                    $inserthistorybalance['createddate'] = getCurrentDate();

                                    $this->historybalance->insert($inserthistorybalance);

                                    $updateuser = array();

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $updateuser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                    } else {
                                        $updateuser['balance'] = $currentbalance + $value_order->price;
                                    }

                                    $this->msusers->update(array(
                                        'id' => $value_order->userid
                                    ), $updateuser);
                                }
                            }

                            $update = array();
                            $update['status'] = 'gagal';
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id
                            ), $update);

                            pullTransaction($value_order->userid ?? $value_order->clientip);

                            if ($order != null) {
                                log_message_user('error', '[MEDANPEDIA ORDER PRODUCT] Response: ' . json_encode($order), $value->id);
                            }
                        }
                    } else if ($value_order->vendor == 'IrvanKede') {
                        $order = $smmobject->order(array(
                            'service' => $value_order->code,
                            'target' => $value_order->target,
                            'quantity' => $value_order->qty,
                            'custom_comments' => $value_order->additional ? $value_order->additional : '',
                            'custom_link' => $value_order->additional ? $value_order->additional : '',
                        ));

                        if (isset($order->status) && $order->status) {
                            $update = array();
                            $update['servercode'] = $order->data->id;
                            $update['jsonresponse'] = json_encode($order);
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id
                            ), $update);
                        } else {
                            if ($value_order->userid != null) {
                                $check_history_balance = $this->historybalance->total(array(
                                    'userid' => $value_order->userid,
                                    'type' => 'IN',
                                    'orderid' => $value_order->id,
                                ));

                                if ($check_history_balance == 0) {
                                    $currentbalance = getCurrentBalance($value_order->userid, true);

                                    $inserthistorybalance = array();
                                    $inserthistorybalance['userid'] = $value_order->userid;
                                    $inserthistorybalance['type'] = 'IN';

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                    } else {
                                        $inserthistorybalance['nominal'] = $value_order->price;
                                    }

                                    $inserthistorybalance['currentbalance'] = $currentbalance;
                                    $inserthistorybalance['orderid'] = $value_order->id;
                                    $inserthistorybalance['createdby'] = $value_order->userid;
                                    $inserthistorybalance['createddate'] = getCurrentDate();

                                    $this->historybalance->insert($inserthistorybalance);

                                    $updateuser = array();

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $updateuser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                    } else {
                                        $updateuser['balance'] = $currentbalance + $value_order->price;
                                    }

                                    $this->msusers->update(array(
                                        'id' => $value_order->userid
                                    ), $updateuser);
                                }
                            }

                            $update = array();
                            $update['status'] = 'gagal';
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id
                            ), $update);

                            pullTransaction($value_order->userid ?? $value_order->clientip);

                            if ($order != null) {
                                log_message_user('error', '[IRVANKEDE ORDER PRODUCT] Response: ' . json_encode($order), $value->id);
                            }
                        }
                    } else if ($value_order->vendor == 'DailyPanel') {
                        $order = $smmobject->order($value_order->code, $value_order->target, $value_order->qty, $value_order->additional ? $value_order->additional : '');

                        if (isset($order->success) && $order->success) {
                            $update = array();
                            $update['servercode'] = $order->msg->order_id;
                            $update['jsonresponse'] = json_encode($order);
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id
                            ), $update);
                        } else {
                            if ($value_order->userid != null) {
                                $check_history_balance = $this->historybalance->total(array(
                                    'userid' => $value_order->userid,
                                    'type' => 'IN',
                                    'orderid' => $value_order->id,
                                ));

                                if ($check_history_balance == 0) {
                                    $currentbalance = getCurrentBalance($value_order->userid, true);

                                    $inserthistorybalance = array();
                                    $inserthistorybalance['userid'] = $value_order->userid;
                                    $inserthistorybalance['type'] = 'IN';

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                    } else {
                                        $inserthistorybalance['nominal'] = $value_order->price;
                                    }

                                    $inserthistorybalance['currentbalance'] = $currentbalance;
                                    $inserthistorybalance['orderid'] = $value_order->id;
                                    $inserthistorybalance['createdby'] = $value_order->userid;
                                    $inserthistorybalance['createddate'] = getCurrentDate();

                                    $this->historybalance->insert($inserthistorybalance);

                                    $updateuser = array();

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $updateuser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                    } else {
                                        $updateuser['balance'] = $currentbalance + $value_order->price;
                                    }

                                    $this->msusers->update(array(
                                        'id' => $value_order->userid
                                    ), $updateuser);
                                }
                            }

                            $update = array();
                            $update['status'] = 'gagal';
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id
                            ), $update);

                            pullTransaction($value_order->userid ?? $value_order->clientip);

                            if ($order != null) {
                                log_message_user('error', '[DAILYPANEL ORDER PRODUCT] Response: ' . json_encode($order), $value->id);
                            }
                        }
                    } else if ($value_order->vendor == 'WStore') {
                        $order = $smmobject->order($value_order->code, $value_order->target, $value_order->qty);

                        if (isset($order->response) && $order->response) {
                            $update = array();
                            $update['servercode'] = $order->data->id;
                            $update['jsonresponse'] = json_encode($order);
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id
                            ), $update);
                        } else {
                            if ($value_order->userid != null) {
                                $check_history_balance = $this->historybalance->total(array(
                                    'userid' => $value_order->userid,
                                    'type' => 'IN',
                                    'orderid' => $value_order->id,
                                ));

                                if ($check_history_balance == 0) {
                                    $currentbalance = getCurrentBalance($value_order->userid, true);

                                    $inserthistorybalance = array();
                                    $inserthistorybalance['userid'] = $value_order->userid;
                                    $inserthistorybalance['type'] = 'IN';

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                    } else {
                                        $inserthistorybalance['nominal'] = $value_order->price;
                                    }

                                    $inserthistorybalance['currentbalance'] = $currentbalance;
                                    $inserthistorybalance['orderid'] = $value_order->id;
                                    $inserthistorybalance['createdby'] = $value_order->userid;
                                    $inserthistorybalance['createddate'] = getCurrentDate();

                                    $this->historybalance->insert($inserthistorybalance);

                                    $updateuser = array();

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $updateuser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                    } else {
                                        $updateuser['balance'] = $currentbalance + $value_order->price;
                                    }

                                    $this->msusers->update(array(
                                        'id' => $value_order->userid
                                    ), $updateuser);
                                }
                            }

                            $update = array();
                            $update['status'] = 'gagal';
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id
                            ), $update);

                            pullTransaction($value_order->userid ?? $value_order->clientip);

                            if ($order != null) {
                                log_message_user('error', '[WSTORE ORDER PRODUCT] Response: ' . json_encode($order), $value->id);
                            }
                        }
                    } else if ($value_order->vendor == 'UNDRCTRL') {
                        $order = $smmobject->order($value_order->code, $value_order->target, $value_order->qty);

                        if (isset($order->order) && $order->order) {
                            $update = array();
                            $update['servercode'] = $order->order;
                            $update['jsonresponse'] = json_encode($order);
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id,
                            ), $update);
                        } else {
                            if ($value_order->userid != null) {
                                $check_history_balance = $this->historybalance->total(array(
                                    'userid' => $value_order->userid,
                                    'type' => 'IN',
                                    'orderid' => $value_order->id,
                                ));

                                if ($check_history_balance == 0) {
                                    $currentbalance = getCurrentBalance($value_order->userid, true);

                                    $inserthistorybalance = array();
                                    $inserthistorybalance['userid'] = $value_order->userid;
                                    $inserthistorybalance['type'] = 'IN';

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                    } else {
                                        $inserthistorybalance['nominal'] = $value_order->price;
                                    }

                                    $inserthistorybalance['currentbalance'] = $currentbalance;
                                    $inserthistorybalance['orderid'] = $value_order->id;
                                    $inserthistorybalance['createdby'] = $value_order->userid;
                                    $inserthistorybalance['createddate'] = getCurrentDate();

                                    $this->historybalance->insert($inserthistorybalance);

                                    $updateuser = array();

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $updateuser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                    } else {
                                        $updateuser['balance'] = $currentbalance + $value_order->price;
                                    }

                                    $this->msusers->update(array(
                                        'id' => $value_order->userid
                                    ), $updateuser);
                                }
                            }

                            $update = array();
                            $update['status'] = 'gagal';
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id,
                            ), $update);

                            pullTransaction($value_order->userid ?? $value_order->clientip);

                            if ($order != null) {
                                log_message_user('error', '[UNDRCTRL ORDER PRODUCT] Response: ' . json_encode($order), $value->id);
                            }
                        }
                    } else if ($value_order->vendor == 'SosmedOnline') {
                        $order = $smmobject->order($value_order->code, $value_order->target, $value_order->qty, $value_order->additional ? $value_order->additional : '');

                        if (isset($order->status) && $order->status) {
                            $update = array();
                            $update['servercode'] = $order->data->id;
                            $update['jsonresponse'] = json_encode($order);
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id,
                            ), $update);
                        } else {
                            if ($value_order->userid != null) {
                                $check_history_balance = $this->historybalance->total(array(
                                    'userid' => $value_order->userid,
                                    'type' => 'IN',
                                    'orderid' => $value_order->id,
                                ));

                                if ($check_history_balance == 0) {
                                    $currentbalance = getCurrentBalance($value_order->userid, true);

                                    $inserthistorybalance = array();
                                    $inserthistorybalance['userid'] = $value_order->userid;
                                    $inserthistorybalance['type'] = 'IN';

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                    } else {
                                        $inserthistorybalance['nominal'] = $value_order->price;
                                    }

                                    $inserthistorybalance['currentbalance'] = $currentbalance;
                                    $inserthistorybalance['orderid'] = $value_order->id;
                                    $inserthistorybalance['createdby'] = $value_order->userid;
                                    $inserthistorybalance['createddate'] = getCurrentDate();

                                    $this->historybalance->insert($inserthistorybalance);

                                    $updateuser = array();

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $updateuser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                    } else {
                                        $updateuser['balance'] = $currentbalance + $value_order->price;
                                    }

                                    $this->msusers->update(array(
                                        'id' => $value_order->userid
                                    ), $updateuser);
                                }
                            }

                            $update = array();
                            $update['status'] = 'gagal';
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id,
                            ), $update);

                            pullTransaction($value_order->userid ?? $value_order->clientip);

                            if ($order != null) {
                                log_message_user('error', '[SOSMEDONLINE ORDER PRODUCT] Response: ' . json_encode($order), $value->id);
                            }
                        }
                    } else if ($value_order->vendor == 'SosmedOnlineVIP') {
                        $order = $smmobject->order($value_order->code, $value_order->target, $value_order->qty, $value_order->additional ? $value_order->additional : '');

                        if (isset($order->status) && $order->status) {
                            $update = array();
                            $update['servercode'] = $order->data->id;
                            $update['jsonresponse'] = json_encode($order);
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id,
                            ), $update);
                        } else {
                            if ($value_order->userid != null) {
                                $check_history_balance = $this->historybalance->total(array(
                                    'userid' => $value_order->userid,
                                    'type' => 'IN',
                                    'orderid' => $value_order->id,
                                ));

                                if ($check_history_balance == 0) {
                                    $currentbalance = getCurrentBalance($value_order->userid, true);

                                    $inserthistorybalance = array();
                                    $inserthistorybalance['userid'] = $value_order->userid;
                                    $inserthistorybalance['type'] = 'IN';

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                    } else {
                                        $inserthistorybalance['nominal'] = $value_order->price;
                                    }

                                    $inserthistorybalance['currentbalance'] = $currentbalance;
                                    $inserthistorybalance['orderid'] = $value_order->id;
                                    $inserthistorybalance['createdby'] = $value_order->userid;
                                    $inserthistorybalance['createddate'] = getCurrentDate();

                                    $this->historybalance->insert($inserthistorybalance);

                                    $updateuser = array();

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $updateuser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                    } else {
                                        $updateuser['balance'] = $currentbalance + $value_order->price;
                                    }

                                    $this->msusers->update(array(
                                        'id' => $value_order->userid
                                    ), $updateuser);
                                }
                            }

                            $update = array();
                            $update['status'] = 'gagal';
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id,
                            ), $update);

                            pullTransaction($value_order->userid ?? $value_order->clientip);

                            if ($order != null) {
                                log_message_user('error', '[SOSMEDONLINEVIP ORDER PRODUCT] Response: ' . json_encode($order), $value->id);
                            }
                        }
                    } else if ($value_order->vendor == 'DjuraganSosmed') {
                        $order = $smmobject->order($value_order->code, $value_order->target, $value_order->qty);

                        if (isset($order->order) && $order->order) {
                            $update = array();
                            $update['servercode'] = $order->order;
                            $update['jsonresponse'] = json_encode($order);
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id,
                            ), $update);
                        } else {
                            if ($value_order->userid != null) {
                                $check_history_balance = $this->historybalance->total(array(
                                    'userid' => $value_order->userid,
                                    'type' => 'IN',
                                    'orderid' => $value_order->id,
                                ));

                                if ($check_history_balance == 0) {
                                    $currentbalance = getCurrentBalance($value_order->userid, true);

                                    $inserthistorybalance = array();
                                    $inserthistorybalance['userid'] = $value_order->userid;
                                    $inserthistorybalance['type'] = 'IN';

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                    } else {
                                        $inserthistorybalance['nominal'] = $value_order->price;
                                    }

                                    $inserthistorybalance['currentbalance'] = $currentbalance;
                                    $inserthistorybalance['orderid'] = $value_order->id;
                                    $inserthistorybalance['createdby'] = $value_order->userid;
                                    $inserthistorybalance['createddate'] = getCurrentDate();

                                    $this->historybalance->insert($inserthistorybalance);

                                    $updateuser = array();

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $updateuser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                    } else {
                                        $updateuser['balance'] = $currentbalance + $value_order->price;
                                    }

                                    $this->msusers->update(array(
                                        'id' => $value_order->userid
                                    ), $updateuser);
                                }
                            }

                            $update = array();
                            $update['status'] = 'gagal';
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id,
                            ), $update);

                            pullTransaction($value_order->userid ?? $value_order->clientip);

                            if ($order != null) {
                                log_message_user('error', '[DJURAGANSOSMED ORDER PRODUCT] Response: ' . json_encode($order), $value->id);
                            }
                        }
                    } else if ($value_order->vendor == 'SMMRaja') {
                        $order = $smmobject->order($value_order->code, $value_order->target, $value_order->qty);

                        if (isset($order->order) && $order->order) {
                            $update = array();
                            $update['servercode'] = $order->order;
                            $update['jsonresponse'] = json_encode($order);
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id,
                            ), $update);
                        } else {
                            if ($value_order->userid != null) {
                                $check_history_balance = $this->historybalance->total(array(
                                    'userid' => $value_order->userid,
                                    'type' => 'IN',
                                    'orderid' => $value_order->id,
                                ));

                                if ($check_history_balance == 0) {
                                    $currentbalance = getCurrentBalance($value_order->userid, true);

                                    $inserthistorybalance = array();
                                    $inserthistorybalance['userid'] = $value_order->userid;
                                    $inserthistorybalance['type'] = 'IN';

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                    } else {
                                        $inserthistorybalance['nominal'] = $value_order->price;
                                    }

                                    $inserthistorybalance['currentbalance'] = $currentbalance;
                                    $inserthistorybalance['orderid'] = $value_order->id;
                                    $inserthistorybalance['createdby'] = $value_order->userid;
                                    $inserthistorybalance['createddate'] = getCurrentDate();

                                    $this->historybalance->insert($inserthistorybalance);

                                    $updateuser = array();

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $updateuser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                    } else {
                                        $updateuser['balance'] = $currentbalance + $value_order->price;
                                    }

                                    $this->msusers->update(array(
                                        'id' => $value_order->userid
                                    ), $updateuser);
                                }
                            }

                            $update = array();
                            $update['status'] = 'gagal';
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id,
                            ), $update);

                            pullTransaction($value_order->userid ?? $value_order->clientip);

                            if ($order != null) {
                                log_message_user('error', '[SMMRAJA ORDER PRODUCT] Response: ' . json_encode($order), $value->id);
                            }
                        }
                    } else if ($value_order->vendor == 'SMMIllusion') {
                        $order = $smmobject->order($value_order->code, $value_order->target, $value_order->qty);

                        if (isset($order->order) && $order->order) {
                            $update = array();
                            $update['servercode'] = $order->order;
                            $update['jsonresponse'] = json_encode($order);
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id,
                            ), $update);
                        } else {
                            if ($value_order->userid != null) {
                                $check_history_balance = $this->historybalance->total(array(
                                    'userid' => $value_order->userid,
                                    'type' => 'IN',
                                    'orderid' => $value_order->id,
                                ));

                                if ($check_history_balance == 0) {
                                    $currentbalance = getCurrentBalance($value_order->userid, true);

                                    $inserthistorybalance = array();
                                    $inserthistorybalance['userid'] = $value_order->userid;
                                    $inserthistorybalance['type'] = 'IN';

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                    } else {
                                        $inserthistorybalance['nominal'] = $value_order->price;
                                    }

                                    $inserthistorybalance['orderid'] = $value_order->id;
                                    $inserthistorybalance['currentbalance'] = $currentbalance;
                                    $inserthistorybalance['createdby'] = $value_order->userid;
                                    $inserthistorybalance['createddate'] = getCurrentDate();

                                    $this->historybalance->insert($inserthistorybalance);

                                    $updateuser = array();

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $updateuser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                    } else {
                                        $updateuser['balance'] = $currentbalance + $value_order->price;
                                    }

                                    $this->msusers->update(array(
                                        'id' => $value_order->userid
                                    ), $updateuser);
                                }
                            }

                            $update = array();
                            $update['status'] = 'gagal';
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id,
                            ), $update);

                            pullTransaction($value_order->userid ?? $value_order->clientip);

                            if ($order != null) {
                                log_message_user('error', '[SMMILLUSION ORDER PRODUCT] Response: ' . json_encode($order), $value->id);
                            }
                        }
                    } else if ($value_order->vendor == 'V1Pedia') {
                        $order = $smmobject->order($value_order->code, $value_order->target, $value_order->qty);

                        if (isset($order->response) && $order->response) {
                            $update = array();
                            $update['servercode'] = $order->data->id;
                            $update['jsonresponse'] = json_encode($order);
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id
                            ), $update);
                        } else {
                            if ($value_order->userid != null) {
                                $check_history_balance = $this->historybalance->total(array(
                                    'userid' => $value_order->userid,
                                    'type' => 'IN',
                                    'orderid' => $value_order->id,
                                ));

                                if ($check_history_balance == 0) {
                                    $currentbalance = getCurrentBalance($value_order->userid, true);

                                    $inserthistorybalance = array();
                                    $inserthistorybalance['userid'] = $value_order->userid;
                                    $inserthistorybalance['type'] = 'IN';

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                    } else {
                                        $inserthistorybalance['nominal'] = $value_order->price;
                                    }

                                    $inserthistorybalance['currentbalance'] = $currentbalance;
                                    $inserthistorybalance['orderid'] = $value_order->id;
                                    $inserthistorybalance['createdby'] = $value_order->userid;
                                    $inserthistorybalance['createddate'] = getCurrentDate();

                                    $this->historybalance->insert($inserthistorybalance);

                                    $updateuser = array();

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $updateuser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                    } else {
                                        $updateuser['balance'] = $currentbalance + $value_order->price;
                                    }

                                    $this->msusers->update(array(
                                        'id' => $value_order->userid
                                    ), $updateuser);
                                }
                            }

                            $update = array();
                            $update['status'] = 'gagal';
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id
                            ), $update);

                            pullTransaction($value_order->userid ?? $value_order->clientip);

                            if ($order != null) {
                                log_message_user('error', '[V1PEDIA ORDER PRODUCT] Response: ' . json_encode($order), $value->id);
                            }
                        }
                    } else {
                        $order = $smmobject->order($value_order->code, 'SMM', $value_order->target, null, $value_order->additional ? $value_order->additional : '', $value_order->qty);

                        if (isset($order->status) && $order->status) {
                            $update = array();
                            $update['servercode'] = $order->data->orderid;
                            $update['jsonresponse'] = json_encode($order);
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id
                            ), $update);
                        } else {
                            if ($value_order->userid != null) {
                                $check_history_balance = $this->historybalance->total(array(
                                    'userid' => $value_order->userid,
                                    'type' => 'IN',
                                    'orderid' => $value_order->id,
                                ));

                                if ($check_history_balance == 0) {
                                    $currentbalance = getCurrentBalance($value_order->userid, true);

                                    $inserthistorybalance = array();
                                    $inserthistorybalance['userid'] = $value_order->userid;
                                    $inserthistorybalance['type'] = 'IN';

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                    } else {
                                        $inserthistorybalance['nominal'] = $value_order->price;
                                    }

                                    $inserthistorybalance['orderid'] = $value_order->id;
                                    $inserthistorybalance['currentbalance'] = $currentbalance;
                                    $inserthistorybalance['createdby'] = $value_order->userid;
                                    $inserthistorybalance['createddate'] = getCurrentDate();

                                    $this->historybalance->insert($inserthistorybalance);

                                    $updateuser = array();

                                    if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                        $updateuser['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                    } else {
                                        $updateuser['balance'] = $currentbalance + $value_order->price;
                                    }

                                    $this->msusers->update(array(
                                        'id' => $value_order->userid
                                    ), $updateuser);
                                }
                            }

                            $update = array();
                            $update['status'] = 'gagal';
                            $update['updateddate'] = getCurrentDate();

                            $this->trorder->update(array(
                                'id' => $value_order->id,
                            ), $update);

                            pullTransaction($value_order->userid ?? $value_order->clientip);

                            if ($order != null) {
                                log_message_user('error', '[MEMBER ORDER PRODUCT] Response: ' . json_encode($order), $value->id);
                            }
                        }
                    }
                }

                if ($this->db->trans_status() === FALSE) {
                    $this->db->trans_rollback();
                }

                $this->db->trans_commit();
            }
        }
    }

    public function member_product_transaction_bca()
    {
        $users = $this->msusers->result(array(
            'merchantid' => null,
            'bca_bot' => 1,
            '(bca_failed_login_count IS NULL OR bca_failed_login_count < 2) =' => true,
            'licenseid !=' => null,
            'expireddate >=' => getCurrentDate()
        ));

        foreach ($users as $key => $value) {
            $transaction = $this->trorder->result(array(
                'a.merchantid_order' => $value->id,
                'a.paymenttype' => 'Otomatis',
                'a.status' => 'pending',
                'a.status_payment' => 'pending',
                'a.payment' => 'Bank BCA',
                'a.servercode' => null,
            ));

            if (count($transaction) == 0) continue;

            $paymentgateway = $this->mspaymentgateway->get(array(
                'a.userid' => $value->id,
                'a.type' => 'Bank BCA',
            ));

            if ($paymentgateway->num_rows() == 0) continue;

            $paymentgatewayRow = $paymentgateway->row();
            $detailPaymentGateway = json_decode(stringEncryption('decrypt', $paymentgatewayRow->detail));

            if ($detailPaymentGateway == null) continue;

            $bca = new BCAV2($detailPaymentGateway->username, $detailPaymentGateway->password);
            $mutasi = $bca->getMutasiRekening();

            if ($mutasi['RESULT'] == 'ERROR') {
                $update = array();
                $update['bca_failed_login_count'] = $value->bca_failed_login_count + 1;

                if ($update['bca_failed_login_count'] == 2) {
                    $update['bca_bot'] = 0;
                }

                $this->msusers->update(array(
                    'id' => $value->id
                ), $update);
            } else {
                $update = array();
                $update['bca_failed_login_count'] = 0;

                $this->msusers->update(array(
                    'id' => $value->id
                ), $update);

                $mutasi_rekening = array();
                if (isset($mutasi['DATA'])) {
                    foreach ($mutasi['DATA'] as $key_data => $value_data) {
                        if (isset($value_data['mutasi']) && $value_data['mutasi'] == 'CR') {
                            $data_mutasi = array();
                            $data_mutasi['date'] = $value_data['tanggal'];
                            $data_mutasi['nominal'] = $value_data['nominal'];

                            $mutasi_rekening[] = $data_mutasi;
                        }
                    }
                }

                foreach ($transaction as $key_transaction => $value_transaction) {
                    $one_days = date('Y-m-d H:i:s', strtotime("$value_transaction->createddate +1 days"));

                    if (getCurrentDate() >= $one_days) {
                        $update = array();
                        $update['status_payment'] = 'failed';
                        $update['status'] = 'gagal';

                        $this->trorder->update(array(
                            'id' => $value_transaction->id
                        ), $update);

                        pullTransaction($value_transaction->userid ?? $value_transaction->clientip);
                    } else {
                        $found = false;

                        foreach ($mutasi_rekening as $k => $v) {
                            if ($found) continue;

                            $nominal = $v['nominal'];
                            $nominal = str_replace('.', '', $nominal);
                            $nominal = str_replace(',', '.', $nominal);

                            if ($nominal == $value_transaction->price) {
                                $update = array();
                                $update['status_payment'] = 'sukses';

                                $this->trorder->update(array(
                                    'id' => $value_transaction->id
                                ), $update);

                                pullTransaction($value_transaction->userid ?? $value_transaction->clientip);

                                $found = true;
                            }
                        }
                    }
                }
            }
        }
    }

    public function member_product_transaction_paymentgateway()
    {
        $users = $this->msusers->select('id')
            ->result(array(
                'merchantid' => null,
                'licenseid !=' => null,
                'expireddate >=' => getCurrentDate()
            ));

        foreach ($users as $key_user => $value_user) {
            $paymentgateway = $this->mspaymentgateway->get(array(
                'userid' => $value_user->id,
                'type' => 'Payment Gateway',
                "(isdisabled IS NULL OR isdisabled = '0') =" => true,
            ))->row();

            if ($paymentgateway == null) continue;

            $order = $this->trorder->result(array(
                'a.merchantid_order' => $value_user->id,
                'a.paymenttype' => 'Otomatis',
                'a.status_payment' => 'pending',
                'a.status' => 'pending',
                'a.gatewayvendor' => $paymentgateway->vendor,
            ));

            foreach ($order as $key => $value) {
                $one_days = date('Y-m-d H:i:s', strtotime("$value->createddate +1 days"));

                if (getCurrentDate() >= $one_days) {
                    $update = array();
                    $update['status_payment'] = 'failed';
                    $update['status'] = 'gagal';

                    $this->trorder->update(array(
                        'id' => $value->id
                    ), $update);

                    pullTransaction($value->userid ?? $value->clientip);
                } else {
                    if ($paymentgateway->vendor == 'PayDisini') {
                        $detail = json_decode(stringEncryption('decrypt', $paymentgateway->detail));
                        if ($detail == null) continue;

                        $paydisini = new PayDisini($detail->apikey);
                        $detailtransaction = $paydisini->status($value->paydisinicode);

                        if (isset($detailtransaction->success) && $detailtransaction->success) {
                            $transaction_data = $detailtransaction->data;

                            if ($transaction_data->status == 'Success') {
                                $update = array();
                                $update['status_payment'] = 'sukses';

                                $this->trorder->update(array(
                                    'id' => $value->id,
                                ), $update);

                                pullTransaction($value->userid ?? $value->clientip);
                            }
                        }
                    }
                }
            }
        }
    }

    public function multivendor_profile()
    {
        $vendor = $this->msvendor->select('a.*')
            ->join('msusers b', 'b.id = a.userid')
            ->result(array(
                'b.licenseid !=' => null,
                'a.isactive' => '1'
            ));

        foreach ($vendor as $key => $value) {
            $profile = $this->msvendordetail->get(array('vendorid' => $value->id, 'apitype' => 'Profile'))->row();
            if ($profile == null) continue;

            $response_indicator = json_decode($profile->response_indicator);
            $response_setting = json_decode($profile->response_setting);

            $dynamicvendor = new DynamicVendor($value->id, json_decode($value->default_config, true));
            $g_profile = $dynamicvendor->profile();

            if (
                ($response_indicator->index == null && isset($g_profile[$response_indicator->key]) && (
                    $response_indicator->datatype == 'string' && $g_profile[$response_indicator->key] == $response_indicator->value
                    || $response_indicator->datatype == 'number' && $g_profile[$response_indicator->key] == $response_indicator->value
                    || $response_indicator->datatype == 'boolean' && $g_profile[$response_indicator->key] == true
                    || $response_indicator->datatype == 'exists' && isset($g_profile[$response_indicator->key])
                ))
                || ($response_indicator->index != null && isset($g_profile[$response_indicator->index][$response_indicator->key]) && (
                    $response_indicator->datatype == 'string' && $g_profile[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                    || $response_indicator->datatype == 'number' && $g_profile[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                    || $response_indicator->datatype == 'boolean' && $g_profile[$response_indicator->index][$response_indicator->key] == true
                    || $response_indicator->datatype == 'exists' && isset($g_profile[$response_indicator->index][$response_indicator->key])
                ))
            ) {
                $balance = 0;
                if ($response_setting->index == null && isset($g_profile[$response_setting->balance])) {
                    $balance = $g_profile[$response_setting->balance];
                } else if ($response_setting->index != null && isset($g_profile[$response_setting->index][$response_setting->balance])) {
                    $balance = $g_profile[$response_setting->index][$response_setting->balance];
                }

                $update = array();
                $update['balance'] = $balance;
                $update['updateddate'] = getCurrentDate();
                $update['updatedby'] = $value->userid;

                $this->msvendor->update(array(
                    'id' => $value->id
                ), $update);
            } else {
                $message = "[MULTIVENDOR PROFILE - RESPONSE INDICATOR] Failed to parse response, response: " . json_encode($g_profile);
                log_message_user('error', $message, $value->userid);
            }
        }
    }

    public function multivendor_service()
    {
        try {
            $vendor = $this->msvendor->select('a.*')
                ->join('msusers b', 'b.id = a.userid')
                ->result(array(
                    'b.licenseid !=' => null,
                    'a.isactive' => '1',
                    'b.expireddate >=' => getCurrentDate(),
                ));

            foreach ($vendor as $key => $value) {
                $service = $this->msvendordetail->get(array('vendorid' => $value->id, 'apitype' => 'Service'))->row();
                if ($service == null) continue;

                $response_indicator = json_decode($service->response_indicator);
                $response_indicator->key = ($response_indicator->key ?? 0);
                $response_setting = json_decode($service->response_setting);

                $dynamicvendor = new DynamicVendor($value->id, json_decode($value->default_config, true));
                $g_service = $dynamicvendor->service();

                $services = $this->msproduct->select('a.category, a.type, a.id, a.code, a.productname, a.price, a.admin, a.status, a.dontupdate, a.dontupdatecategory, a.oldcategoryname')
                    ->result(array(
                        'userid' => $value->userid,
                        'vendorid' => $value->id,
                    ));

                $log = $this->productpricelog->select('a.id, a.type, a.productid, a.oldprice, a.newprice')
                    ->result(array(
                        'userid' => $value->userid,
                        'DATE(createddate) =' => getCurrentDate('Y-m-d')
                    ));

                $profit = $this->db->select('category, minprice, maxprice, profit, margintype')
                    ->from('msprofit')
                    ->where('userid', $value->userid)
                    ->order_by('minprice', 'ASC')
                    ->get()
                    ->result();

                $profits = array();
                foreach ($profit as $k => $v) {
                    $profits[$v->category][] = array(
                        'minprice' => $v->minprice,
                        'maxprice' => $v->maxprice,
                        'profit' => $v->profit,
                        'margintype' => $v->margintype
                    );
                }

                $old_services = array();
                $history_log = array();
                $updated_services = array();
                $dont_updatecategory = array();

                foreach ($services as $k => $v) {
                    $old_services[$v->code] = array(
                        'productid' => $v->id,
                        'productname' => $v->productname,
                        'price' => $v->price,
                        'admin' => $v->admin,
                        'status' => $v->status,
                        'dontupdate' => $v->dontupdate,
                        'dontupdatecategory' => $v->dontupdatecategory,
                        'type' => $v->type,
                        'description_null' => empty($v->description),
                        'category' => $v->category,
                        'oldcategoryname' => $v->oldcategoryname
                    );

                    if (!empty($v->oldcategoryname) && !array_key_exists($v->oldcategoryname, $dont_updatecategory)) {
                        $dont_updatecategory[$v->oldcategoryname] = array(
                            'category' => $v->category
                        );
                    }
                }

                foreach ($log as $k => $v) {
                    $history_log[$v->productid] = array(
                        'logid' => $v->id,
                        'type' => $v->type,
                        'oldprice' => $v->oldprice,
                        'newprice' => $v->newprice
                    );
                }

                $insert_batch = array();
                $insert_log = array();
                $update_log = array();
                $update_batch = array();

                $var_code = $response_setting->code ?? null;
                $var_productname = $response_setting->productname ?? null;
                $var_description = $response_setting->description ?? null;
                $var_status = $response_setting->status ?? null;
                $var_statusdatatype = $response_setting->statusdatatype ?? null;
                $var_validstatus = $response_setting->validstatus ?? null;
                $var_price = $response_setting->price ?? null;
                $var_category = $response_setting->category ?? null;
                $var_brand = $response_setting->brand ?? null;
                $var_minorder = $response_setting->minorder ?? null;
                $var_maxorder = $response_setting->maxorder ?? null;
                $var_type = $response_setting->type ?? null;

                if (!$dynamicvendor->isMultipleRequest()) {
                    if (
                        ($response_indicator->index == null && isset($g_service[$response_indicator->key]) && (
                            $response_indicator->datatype == 'string' && $g_service[$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'number' && $g_service[$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'boolean' && $g_service[$response_indicator->key] == true
                            || $response_indicator->datatype == 'exists' && isset($g_service[$response_indicator->key])
                        ))
                        || ($response_indicator->index != null && isset($g_service[$response_indicator->index][$response_indicator->key]) && (
                            $response_indicator->datatype == 'string' && $g_service[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'number' && $g_service[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'boolean' && $g_service[$response_indicator->index][$response_indicator->key] == true
                            || $response_indicator->datatype == 'exists' && isset($g_service[$response_indicator->index][$response_indicator->key])
                        ))
                    ) {
                        if ($response_setting->index == null) {
                            $g_service_eval = $g_service;
                        } else {
                            $g_service_eval = isset($g_service[$response_setting->index]) ? $g_service[$response_setting->index] : array();
                        }

                        if (count($g_service_eval) > 0) {
                            $data = $g_service_eval;

                            foreach ($data as $k => $v) {
                                $v = (object) $v;

                                $code = $v->{$var_code} ?? null;
                                $productname = $v->{$var_productname} ?? null;
                                $description = $v->{$var_description} ?? null;
                                $status = $v->{$var_status} ?? null;
                                $price = $v->{$var_price} ?? null;
                                $category = $v->{$var_category} ?? null;
                                $brand = $v->{$var_brand} ?? null;
                                $minorder = $v->{$var_minorder} ?? null;
                                $maxorder = $v->{$var_maxorder} ?? null;
                                $type = $v->{$var_type} ?? null;

                                if ($value->category == 'PPOB') {
                                    $profit = getPriceWithProfitV2('Prabayar', $price, $profits);
                                } else if ($value->category == 'SMM') {
                                    $profit = getPriceWithProfitV2('Media Sosial', $price, $profits);
                                }

                                $price = $price + $profit;

                                if (!array_key_exists($code, $old_services)) {
                                    $insert = array();
                                    $insert['userid'] = $value->userid;
                                    $insert['vendor'] = $value->name;
                                    $insert['code'] = $code;
                                    $insert['productname'] = $productname;
                                    $insert['description'] = $description;
                                    $insert['status'] = $status == $var_validstatus;
                                    $insert['price'] = $price;

                                    if (!array_key_exists($category, $dont_updatecategory)) {
                                        $insert['category'] = $category;
                                    } else {
                                        $insert['category'] = $dont_updatecategory[$category]['category'];
                                    }

                                    $insert['brand'] = $brand;
                                    $insert['createddate'] = getCurrentDate();
                                    $insert['createdby'] = $value->userid;
                                    $insert['updateddate'] = getCurrentDate();
                                    $insert['updatedby'] = $value->userid;
                                    $insert['category_apikey'] = $value->category;

                                    if ($value->category == 'PPOB') {
                                        $insert['subcategory_apikey'] = 'PRABAYAR';
                                    } else if ($value->category == 'SMM') {
                                        $insert['subcategory_apikey'] = 'SMM';
                                    }

                                    $insert['admin'] = 0;
                                    $insert['commission'] = 0;
                                    $insert['minorder'] = $minorder;
                                    $insert['maxorder'] = $maxorder;
                                    $insert['type'] = $type;
                                    $insert['profit'] = $profit;
                                    $insert['vendorprice'] = $v->{$var_price} ?? null;
                                    $insert['iscustom'] = 0;
                                    $insert['dontupdate'] = 0;

                                    if (!array_key_exists($category, $dont_updatecategory)) {
                                        $insert['oldcategoryname'] = null;
                                        $insert['dontupdatecategory'] = 0;
                                    } else {
                                        $insert['oldcategoryname'] = $category;
                                        $insert['dontupdatecategory'] = 1;
                                    }

                                    $insert['ismanualadd'] = 0;
                                    $insert['isstock'] = 0;
                                    $insert['stock'] = 0;
                                    $insert['vendorid'] = $value->id;
                                    $insert['vendorenabled'] = 1;

                                    $insert_batch[] = $insert;
                                } else {
                                    $status = $status == $var_validstatus;

                                    if (
                                        $productname != $old_services[$code]['productname']
                                        || $status != $old_services[$code]['status']
                                        || (ceil($price) != $old_services[$code]['price'] && floor($price) != $old_services[$code]['price'])
                                        || ($old_services[$code]['oldcategoryname'] == null && array_key_exists($category, $dont_updatecategory))
                                        || $type != $old_services[$code]['type']
                                    ) {
                                        if (ceil($price) != $old_services[$code]['price'] && floor($price) != $old_services[$code]['price']) {
                                            if ($price > $old_services[$code]['price']) {
                                                $type_log = 'UP';
                                            } else {
                                                $type_log = 'DOWN';
                                            }

                                            if (!array_key_exists($old_services[$code]['productid'], $history_log)) {
                                                $insert = array();
                                                $insert['userid'] = $value->userid;
                                                $insert['productid'] = $old_services[$code]['productid'];
                                                $insert['type'] = $type_log;
                                                $insert['oldprice'] = $old_services[$code]['price'];
                                                $insert['newprice'] = $price;
                                                $insert['createddate'] = getCurrentDate();
                                                $insert['createdby'] = $value->userid;

                                                $insert_log[] = $insert;
                                            } else {
                                                $update = array();
                                                $update['id'] = $history_log[$old_services[$code]['productid']]['logid'];
                                                $update['type'] = $type_log;
                                                $update['oldprice'] = $old_services[$code]['price'];
                                                $update['newprice'] = $price;
                                                $update['updateddate'] = getCurrentDate();
                                                $update['updatedby'] = $value->userid;

                                                $update_log[] = $update;
                                            }
                                        }

                                        $update = array();
                                        $update['id'] = $old_services[$code]['productid'];

                                        if ($old_services[$code]['dontupdate'] == null || $old_services[$code]['dontupdate'] == 0) {
                                            $update['productname'] = $productname;

                                            if ($value->category == 'SMM') {
                                                $update['description'] = $description;
                                            }

                                            $update['status'] = $status;
                                            $update['price'] = $price;
                                        }

                                        if (!array_key_exists($category, $dont_updatecategory)) {
                                            $update['category'] = $category;
                                        } else {
                                            $update['category'] = $dont_updatecategory[$category]['category'];
                                        }

                                        $update['brand'] = $brand;
                                        $update['updateddate'] = getCurrentDate();
                                        $update['minorder'] = $minorder;
                                        $update['maxorder'] = $maxorder;
                                        $update['type'] = $type;

                                        if ($old_services[$code]['dontupdate'] == null || $old_services[$code]['dontupdate'] == 0) {
                                            $update['profit'] = $profit;
                                        }

                                        $update['vendorprice'] = $v->{$var_price};

                                        if (!array_key_exists($category, $dont_updatecategory)) {
                                            $update['oldcategoryname'] = null;
                                            $update['dontupdatecategory'] = 0;
                                        } else {
                                            $update['oldcategoryname'] = $category;
                                            $update['dontupdatecategory'] = 1;
                                        }

                                        $update['vendorenabled'] = 1;

                                        $update_batch[] = $update;
                                    }
                                }

                                $updated_services[$code] = $v;
                            }
                        } else {
                            log_message_user('error', '[MULTIVENDOR SERVICE - RESPONSE SETTING] Failed to parse response', $value->userid);
                        }
                    } else {
                        log_message_user('error', "[MULTIVENDOR SERVICE - RESPONSE INDICATOR] Failed to parse response", $value->userid);
                    }
                } else {
                    $backup_g_service = $g_service;

                    foreach ($backup_g_service as $g_service) {
                        if (
                            ($response_indicator->index == null && (isset($g_service[$response_indicator->key])) && (
                                $response_indicator->datatype == 'string' && $g_service[$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'number' && $g_service[$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'boolean' && $g_service[$response_indicator->key] == true
                                || $response_indicator->datatype == 'exists' && isset($g_service[$response_indicator->key])
                            ))
                            || ($response_indicator->index != null && isset($g_service[$response_indicator->index][$response_indicator->key]) && (
                                $response_indicator->datatype == 'string' && $g_service[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'number' && $g_service[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                                || $response_indicator->datatype == 'boolean' && $g_service[$response_indicator->index][$response_indicator->key] == true
                                || $response_indicator->datatype == 'exists' && isset($g_service[$response_indicator->index][$response_indicator->key])
                            ))
                        ) {
                            if ($response_setting->index == null) {
                                $g_service_eval = $g_service;
                            } else {
                                $g_service_eval = isset($g_service[$response_setting->index]) ? $g_service[$response_setting->index] : array();
                            }

                            if (count($g_service_eval) > 0) {
                                $data = $g_service_eval;

                                foreach ($data as $k => $v) {
                                    $v = (object) $v;

                                    $code = $v->{$var_code} ?? null;
                                    $productname = $v->{$var_productname} ?? null;
                                    $description = $v->{$var_description} ?? null;
                                    $status = $v->{$var_status} ?? null;
                                    $price = $v->{$var_price} ?? null;
                                    $category = $v->{$var_category} ?? null;
                                    $brand = $v->{$var_brand} ?? null;
                                    $minorder = $v->{$var_minorder} ?? null;
                                    $maxorder = $v->{$var_maxorder} ?? null;
                                    $type = $v->{$var_type} ?? null;

                                    if ($value->category == 'PPOB') {
                                        $profit = getPriceWithProfitV2('Prabayar', $price, $profits);
                                    } else if ($value->category == 'SMM') {
                                        $profit = getPriceWithProfitV2('Media Sosial', $price, $profits);
                                    }

                                    $price = $price + $profit;

                                    if (!array_key_exists($code, $old_services)) {
                                        $insert = array();
                                        $insert['userid'] = $value->userid;
                                        $insert['vendor'] = $value->name;
                                        $insert['code'] = $code;
                                        $insert['productname'] = $productname;
                                        $insert['description'] = $description;
                                        $insert['status'] = $status == $var_validstatus;
                                        $insert['price'] = $price;

                                        if (!array_key_exists($category, $dont_updatecategory)) {
                                            $insert['category'] = $category;
                                        } else {
                                            $insert['category'] = $dont_updatecategory[$category]['category'];
                                        }

                                        $insert['brand'] = $brand;
                                        $insert['createddate'] = getCurrentDate();
                                        $insert['createdby'] = $value->userid;
                                        $insert['updateddate'] = getCurrentDate();
                                        $insert['updatedby'] = $value->userid;
                                        $insert['category_apikey'] = $value->category;

                                        if ($value->category == 'PPOB') {
                                            $insert['subcategory_apikey'] = 'PRABAYAR';
                                        } else if ($value->category == 'SMM') {
                                            $insert['subcategory_apikey'] = 'SMM';
                                        }

                                        $insert['admin'] = 0;
                                        $insert['commission'] = 0;
                                        $insert['minorder'] = $minorder;
                                        $insert['maxorder'] = $maxorder;
                                        $insert['type'] = $type;
                                        $insert['profit'] = $profit;
                                        $insert['vendorprice'] = $v->{$var_price} ?? null;
                                        $insert['iscustom'] = 0;
                                        $insert['dontupdate'] = 0;

                                        if (!array_key_exists($category, $dont_updatecategory)) {
                                            $insert['oldcategoryname'] = null;
                                            $insert['dontupdatecategory'] = 0;
                                        } else {
                                            $insert['oldcategoryname'] = $category;
                                            $insert['dontupdatecategory'] = 1;
                                        }

                                        $insert['ismanualadd'] = 0;
                                        $insert['isstock'] = 0;
                                        $insert['stock'] = 0;
                                        $insert['vendorid'] = $value->id;
                                        $insert['vendorenabled'] = 1;

                                        $insert_batch[] = $insert;
                                    } else {
                                        $status = $status == $var_validstatus;

                                        if (
                                            $productname != $old_services[$code]['productname']
                                            || $status != $old_services[$code]['status']
                                            || (ceil($price) != $old_services[$code]['price'] && floor($price) != $old_services[$code]['price'])
                                            || ($old_services[$code]['oldcategoryname'] == null && array_key_exists($category, $dont_updatecategory))
                                            || $type != $old_services[$code]['type']
                                        ) {
                                            if (ceil($price) != $old_services[$code]['price'] && floor($price) != $old_services[$code]['price']) {
                                                if ($price > $old_services[$code]['price']) {
                                                    $type_log = 'UP';
                                                } else {
                                                    $type_log = 'DOWN';
                                                }

                                                if (!array_key_exists($old_services[$code]['productid'], $history_log)) {
                                                    $insert = array();
                                                    $insert['userid'] = $value->userid;
                                                    $insert['productid'] = $old_services[$code]['productid'];
                                                    $insert['type'] = $type_log;
                                                    $insert['oldprice'] = $old_services[$code]['price'];
                                                    $insert['newprice'] = $price;
                                                    $insert['createddate'] = getCurrentDate();
                                                    $insert['createdby'] = $value->userid;

                                                    $insert_log[] = $insert;
                                                } else {
                                                    $update = array();
                                                    $update['id'] = $history_log[$old_services[$code]['productid']]['logid'];
                                                    $update['type'] = $type_log;
                                                    $update['oldprice'] = $old_services[$code]['price'];
                                                    $update['newprice'] = $price;
                                                    $update['updateddate'] = getCurrentDate();
                                                    $update['updatedby'] = $value->userid;

                                                    $update_log[] = $update;
                                                }
                                            }

                                            $update = array();
                                            $update['id'] = $old_services[$code]['productid'];

                                            if ($old_services[$code]['dontupdate'] == null || $old_services[$code]['dontupdate'] == 0) {
                                                $update['productname'] = $productname;

                                                if ($value->category == 'SMM') {
                                                    $update['description'] = $description;
                                                }

                                                $update['status'] = $status;
                                                $update['price'] = $price;
                                            }

                                            if (!array_key_exists($category, $dont_updatecategory)) {
                                                $update['category'] = $category;
                                            } else {
                                                $update['category'] = $dont_updatecategory[$category]['category'];
                                            }

                                            $update['brand'] = $brand;
                                            $update['updateddate'] = getCurrentDate();
                                            $update['minorder'] = $minorder;
                                            $update['maxorder'] = $maxorder;
                                            $update['type'] = $type;

                                            if ($old_services[$code]['dontupdate'] == null || $old_services[$code]['dontupdate'] == 0) {
                                                $update['profit'] = $profit;
                                            }

                                            $update['vendorprice'] = $v->{$var_price};

                                            if (!array_key_exists($category, $dont_updatecategory)) {
                                                $update['oldcategoryname'] = null;
                                                $update['dontupdatecategory'] = 0;
                                            } else {
                                                $update['oldcategoryname'] = $category;
                                                $update['dontupdatecategory'] = 1;
                                            }

                                            $update['vendorenabled'] = 1;

                                            $update_batch[] = $update;
                                        }
                                    }

                                    $updated_services[$code] = $v;
                                }
                            } else {
                                log_message_user('error', '[MULTIVENDOR SERVICE - RESPONSE SETTING] Failed to parse response', $value->userid);
                            }
                        } else {
                            log_message_user('error', "[MULTIVENDOR SERVICE - RESPONSE INDICATOR] Failed to parse response", $value->userid);
                        }
                    }
                }

                $insert_batch_chunk = array_chunk($insert_batch, 25);
                $insert_log_batch_chunk = array_chunk($insert_log, 25);
                $update_log_batch_chunk = array_chunk($update_log, 25);
                $update_batch_chunk = array_chunk($update_batch, 25);

                if ($service->autoaddproduct == 1) {
                    if (count($updated_services) > 0) {
                        foreach ($old_services as $k => $v) {
                            if (!array_key_exists($k, $updated_services)) {
                                $this->msproduct->delete(array(
                                    'userid' => $value->userid,
                                    'vendor' => $value->name,
                                    'vendorid' => $value->id,
                                    'code' => $k,
                                    'category_apikey' => $category
                                ));
                            }
                        }
                    }

                    foreach ($insert_batch_chunk as $num => $insert) {
                        $this->msproduct->insert_batch($insert);
                    }
                } else {
                    $this->mstempproduct->delete(array(
                        'userid' => $value->userid,
                        'vendorid' => $value->id,
                    ));

                    foreach ($insert_batch_chunk as $num => $data) {
                        $insert = array();
                        $insert['userid'] = $value->userid;
                        $insert['vendor'] = $value->name;
                        $insert['vendorid'] = $value->id;
                        $insert['data'] = json_encode($data);
                        $insert['createddate'] = getCurrentDate();
                        $insert['createdby'] = $value->userid;
                        $insert['updateddate'] = getCurrentDate();
                        $insert['updatedby'] = $value->userid;

                        $this->mstempproduct->insert($insert);
                    }
                }

                foreach ($insert_log_batch_chunk as $num => $insert) {
                    $this->productpricelog->insert_batch($insert);
                }

                foreach ($update_log_batch_chunk as $num => $update) {
                    $this->productpricelog->update_batch($update, 'id');
                }

                foreach ($update_batch_chunk as $num => $update) {
                    $this->msproduct->update_batch($update, 'id');
                }
            }
        } catch (Exception $ex) {
        }
    }

    public function multivendor_status()
    {
        try {
            $users = $this->msusers->select('a.*, b.id AS vendorid, b.default_config')
                ->join('msvendor b', 'b.userid = a.id')
                ->result(array(
                    'a.merchantid' => null,
                    'a.licenseid !=' => null,
                    'a.expireddate >=' => getCurrentDate(),
                    'a.multivendor' => 1
                ));

            foreach ($users as $key_user => $value_user) {
                $vendordetail = $this->msvendordetail->get(array(
                    'vendorid' => $value_user->vendorid,
                    'apitype' => 'Status'
                ));

                if ($vendordetail->num_rows() == 0) continue;

                $vendor_order = $vendordetail->row();

                $response_indicator = json_decode($vendor_order->response_indicator);
                $response_setting = json_decode($vendor_order->response_setting);

                $whereorder = array(
                    "(LOWER(a.status) = 'pending' OR LOWER(a.status) = 'in progress' OR LOWER(a.status) = 'processing') =" => true,
                    "(b.merchantid = '$value_user->id' OR a.merchantid_order = '$value_user->id') =" => true,
                    "(a.queuetransaction IS NULL OR a.queuetransaction = 0) =" => true,
                    'a.servercode !=' => null,
                    "(a.status_payment = 'sukses' OR a.status_payment IS NULL) =" => true,
                    'a.vendorid' => $value_user->vendorid,
                );

                $order = $this->trorder->select('a.*, c.vendor AS vendor_product, c.category_apikey AS category_apikey_product, c.subcategory_apikey AS subcategory_apikey_product, c.type AS producttype, c.category, c.code, b.phonenumber, b.merchantid')
                    ->join('msusers b', 'b.id = a.userid', 'LEFT')
                    ->join('msproduct c', 'c.id = a.serviceid', 'LEFT')
                    ->result($whereorder);

                foreach ($order as $key_order => $value_order) {
                    $this->db->trans_begin();

                    $dynamicvendor = new DynamicVendor($value_user->vendorid, json_decode($value_user->default_config, true));
                    $status = $dynamicvendor->status($value_order->id);

                    if (
                        ($response_indicator->index == null && isset($status[$response_indicator->key]) && (
                            $response_indicator->datatype == 'string' && $status[$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'number' && $status[$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'boolean' && $status[$response_indicator->key] == true
                            || $response_indicator->datatype == 'exists' && isset($status[$response_indicator->key])
                        ))
                        || ($response_indicator->index != null && isset($status[$response_indicator->index][$response_indicator->key]) && (
                            $response_indicator->datatype == 'string' && $status[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'number' && $status[$response_indicator->index][$response_indicator->key] == $response_indicator->value
                            || $response_indicator->datatype == 'boolean' && $status[$response_indicator->index][$response_indicator->key] == true
                            || $response_indicator->datatype == 'exists' && isset($status[$response_indicator->index][$response_indicator->key])
                        ))
                    ) {
                        $var_referenceid = $response_setting->referenceid ?? null;
                        $var_start_count = $response_setting->start_count ?? null;
                        $var_remains = $response_setting->remains ?? null;
                        $var_status = $response_setting->status ?? null;
                        $var_errorrefund = $response_setting->errorrefund ?? null;
                        $var_partialrefund = $response_setting->partialrefund ?? null;

                        $exploding_errorrefund = explode('[#]', strtolower($var_errorrefund ?? ''));
                        $exploding_partialrefund = explode('[#]', strtolower($var_partialrefund ?? ''));

                        if ($response_setting->index != null) {
                            $status = $status[$response_setting->index];
                        }

                        $referenceid = $var_referenceid != null ? ($status[$var_referenceid] ?? null) : null;
                        $start_count = $var_start_count != null ? ($status[$var_start_count] ?? null) : null;
                        $remains = $var_remains != null ? ($status[$var_remains] ?? null) : null;
                        $status = $var_status != null ? ($status[$var_status] ?? null) : null;

                        if ($status != null) {
                            if (in_array($status, $exploding_errorrefund)) {
                                if ($value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $inserthistorybalance['nominal'] = $value_order->price - round($value_order->fee);
                                        } else {
                                            $inserthistorybalance['nominal'] = $value_order->price;
                                        }

                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $update = array();

                                        if ($value_order->paymenttype == 'Otomatis' && $value_order->fee != null) {
                                            $update['balance'] = $currentbalance + ($value_order->price - round($value_order->fee));
                                        } else {
                                            $update['balance'] = $currentbalance + $value_order->price;
                                        }

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $update);
                                    }
                                }
                            } else if (in_array($status, $exploding_partialrefund)) {
                                if ($value_order->userid != null) {
                                    $check_history_balance = $this->historybalance->total(array(
                                        'userid' => $value_order->userid,
                                        'type' => 'IN',
                                        'orderid' => $value_order->id
                                    ));

                                    if ($check_history_balance == 0) {
                                        $currentbalance = getCurrentBalance($value_order->userid, true);

                                        $remains = $remains ?? 0;
                                        $qty = $value_order->qty;
                                        $price = $value_order->price;
                                        $peritem = $price / $qty;

                                        $fee = round($value_order->fee ?? 0);
                                        if ($fee > 0) {
                                            $feeitem = $fee / $qty;
                                            $feerefund = (int)($remains * $feeitem);
                                        } else {
                                            $feerefund = 0;
                                        }

                                        $refund = (int)($remains * $peritem);

                                        $inserthistorybalance = array();
                                        $inserthistorybalance['userid'] = $value_order->userid;
                                        $inserthistorybalance['type'] = 'IN';
                                        $inserthistorybalance['nominal'] = $refund - $feerefund;
                                        $inserthistorybalance['currentbalance'] = $currentbalance;
                                        $inserthistorybalance['orderid'] = $value_order->id;
                                        $inserthistorybalance['createdby'] = $value_order->userid;
                                        $inserthistorybalance['createddate'] = getCurrentDate();

                                        $this->historybalance->insert($inserthistorybalance);

                                        $update = array();
                                        $update['balance'] = $currentbalance + $refund - $feerefund;

                                        $this->msusers->update(array(
                                            'id' => $value_order->userid
                                        ), $update);
                                    }
                                }
                            }

                            $update = array();
                            $update['status'] = $status;

                            if ($start_count != null) {
                                $update['startcount'] = $start_count;
                            }

                            if ($remains != null) {
                                $update['remain'] = $remains;
                            }

                            if ($value_order->queuetransaction == 1) {
                                $update['queuetransaction'] = 0;
                            }

                            $this->trorder->update(array(
                                'id' => $value_order->id
                            ), $update);

                            if (strtolower($status) != strtolower($value_order->status)) {
                                pullTransaction($value_order->userid ?? $value_order->clientip);
                                $this->send_notification($value_order->id, $value_order->merchantid ?? $value_order->merchantid_order, $value_order->phonenumber ?? $value_order->phonenumber_order);
                            }
                        }
                    } else {
                        log_message_user('error', "[MULTIVENDOR STATUS - RESPONSE INDICATOR] Failed to parse response, response: " . json_encode($status), $value_user->id);
                    }

                    if ($this->db->trans_status() === FALSE) {
                        $this->db->trans_rollback();
                    }

                    $this->db->trans_commit();
                }
            }
        } catch (Exception $ex) {
            $this->db->trans_rollback();
        }
    }

    public function broadcast_email_marketing()
    {
        try {
            $this->load->model('MsBroadcastEmailMarketing', 'broadcastemailmarketing');
            $this->load->model('MsBroadcastEmailMarketingQueue', 'broadcastemailmarketingqueue');
            $this->load->model('MsUsers', 'msusers');

            // Get broadcasts with status 'sending'
            $broadcasts = $this->broadcastemailmarketing->result(array(
                'status' => 'sending'
            ));

            foreach ($broadcasts as $broadcast) {
                $this->process_broadcast_emails($broadcast);
            }
        } catch (Exception $e) {
            log_message('error', '[BROADCAST EMAIL MARKETING CRON] Error: ' . $e->getMessage());
        }
    }

    private function process_broadcast_emails($broadcast)
    {
        try {
            $pending_emails = $this->broadcastemailmarketingqueue->limit(100)
                ->result(array(
                    'broadcastid' => $broadcast->id,
                    'status' => 'pending'
                ));

            if (count($pending_emails) == 0) {
                $this->broadcastemailmarketing->update(array('id' => $broadcast->id), array(
                    'status' => 'sent',
                    'updateddate' => getCurrentDate(),
                    'updatedby' => $broadcast->userid
                ));

                return;
            }

            $sender = $this->msusers->get(array('id' => $broadcast->userid))->row();
            if (!$sender) {
                throw new Exception('Sender not found for user ID: ' . $broadcast->userid);
            }

            $smtp_config = null;
            $use_platform_smtp = false;

            if ($sender->smtpemailconfig) {
                $smtp_config = json_decode(stringEncryption('decrypt', $sender->smtpemailconfig));
            }

            if (!$smtp_config || !$smtp_config->host || !$smtp_config->username) {
                $smtp_config = (object) array(
                    'host' => String_Helper::SMTP_HOST,
                    'username' => String_Helper::SMTP_USERNAME,
                    'password' => String_Helper::SMTP_PASSWORD,
                    'port' => String_Helper::SMTP_PORT
                );

                $use_platform_smtp = true;
            }

            foreach ($pending_emails as $email_queue) {
                $this->send_single_broadcast_email($broadcast, $email_queue, $smtp_config, $sender, $use_platform_smtp);
            }
        } catch (Exception $e) {
            log_message_user('error', '[BROADCAST EMAIL MARKETING] Error processing broadcast ID ' . $broadcast->id . ': ' . $e->getMessage(), $broadcast->userid);
        }
    }

    private function send_single_broadcast_email($broadcast, $email_queue, $smtp_config, $sender, $use_platform_smtp = false)
    {
        try {
            $this->broadcastemailmarketingqueue->update(array('id' => $email_queue->id), array(
                'status' => 'processing',
                'updateddate' => getCurrentDate()
            ));

            $recipient = $this->msusers->get(array('id' => $email_queue->recipientid))->row();

            if (!$recipient) {
                throw new Exception('Recipient not found: ' . $email_queue->recipientid);
            }

            // Check if recipient has unsubscribed (check in live/demo database)
            $unsubscribed = $this->msemailunsubscribe->get(array(
                'userid' => $email_queue->recipientid,
                'merchantid' => $broadcast->userid
            ))->num_rows() > 0;

            if ($unsubscribed) {
                // Mark as cancelled if unsubscribed
                $this->broadcastemailmarketingqueue->update(array('id' => $email_queue->id), array(
                    'status' => 'cancelled',
                    'error_message' => 'Recipient has unsubscribed',
                    'updateddate' => getCurrentDate()
                ));

                return;
            }

            $personalized_content = replaceParameterEmailMarketing(
                $broadcast->content,
                $broadcast->userid,
                $email_queue->recipientid,
                false,
                $broadcast->id
            );

            $personalized_subject = replaceParameterEmailMarketing(
                $broadcast->subject,
                $broadcast->userid,
                $email_queue->recipientid,
                true,
                $broadcast->id
            );

            $sender_name = $sender->companyname;
            $sender_email = $use_platform_smtp ? String_Helper::SMTP_USERNAME : $smtp_config->username;

            $email_sent = sendMail(
                $smtp_config->host,
                $sender_email,
                $smtp_config->password,
                $smtp_config->port,
                $recipient->email,
                $sender_name,
                $personalized_subject,
                $personalized_content
            );

            if ($email_sent) {
                $this->broadcastemailmarketingqueue->update(array('id' => $email_queue->id), array(
                    'status' => 'sent',
                    'sentdate' => getCurrentDate(),
                    'updateddate' => getCurrentDate()
                ));
            } else {
                $this->broadcastemailmarketingqueue->update(array('id' => $email_queue->id), array(
                    'status' => 'failed',
                    'error_message' => 'Failed to send email',
                    'updateddate' => getCurrentDate()
                ));
            }
        } catch (Exception $e) {
            $this->broadcastemailmarketingqueue->update(array('id' => $email_queue->id), array(
                'status' => 'failed',
                'error_message' => $e->getMessage(),
                'updateddate' => getCurrentDate()
            ));

            log_message_user('error', '[BROADCAST EMAIL MARKETING] Error sending email to recipient ' . $email_queue->recipientid . ': ' . $e->getMessage(), $broadcast->userid);
        }
    }
}
