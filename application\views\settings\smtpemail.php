<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<!--begin::Toolbar-->
<div class="toolbar mb-5 mb-lg-7" id="kt_toolbar">
    <!--begin::Page title-->
    <div class="page-title d-flex flex-column me-3">
        <!--begin::Title-->
        <h1 class="d-flex text-dark fw-bold my-1 fs-3">SMTP Email</h1>
        <!--end::Title-->

        <!--begin::Breadcrumb-->
        <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">
                <a href="<?= base_url('dashboard') ?>" class="text-gray-600 text-hover-primary">Beranda</a>
            </li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-600">Akun</li>
            <!--end::Item-->

            <!--begin::Item-->
            <li class="breadcrumb-item text-gray-500">SMTP Email</li>
            <!--end::Item-->
        </ul>
        <!--end::Breadcrumb-->
    </div>
    <!--end::Page title-->
</div>
<!--end::Toolbar-->

<div class="content flex-column-fluid">
    <div class="row">
        <div class="col-md-12">
            <!--begin::Alert-->
            <div class="alert alert-info d-flex align-items-center p-5">
                <!--begin::Svg Icon -->
                <span class="svg-icon svg-icon-2hx svg-icon-info me-4">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path opacity="0.3" d="M4.05424 15.1982C8.34524 7.76818 13.5782 3.26318 20.9282 2.01418C21.0729 1.98837 21.2216 1.99789 21.3618 2.04193C21.502 2.08597 21.6294 2.16323 21.7333 2.26712C21.8372 2.37101 21.9144 2.49846 21.9585 2.63863C22.0025 2.7788 22.012 2.92754 21.9862 3.07218C20.7372 10.4222 16.2322 15.6552 8.80224 19.9462L4.05424 15.1982ZM3.81924 17.3372L2.63324 20.4482C2.58427 20.5765 2.5735 20.7163 2.6022 20.8507C2.63091 20.9851 2.69788 21.1082 2.79503 21.2054C2.89218 21.3025 3.01536 21.3695 3.14972 21.3982C3.28408 21.4269 3.42387 21.4161 3.55224 21.3672L6.66524 20.1802L3.81924 17.3372ZM16.5002 5.99818C16.2036 5.99818 15.9136 6.08615 15.6669 6.25097C15.4202 6.41579 15.228 6.65006 15.1144 6.92415C15.0009 7.19824 14.9712 7.49984 15.0291 7.79081C15.0869 8.08178 15.2298 8.34906 15.4396 8.55884C15.6494 8.76862 15.9166 8.91148 16.2076 8.96935C16.4986 9.02723 16.8002 8.99753 17.0743 8.884C17.3484 8.77046 17.5826 8.5782 17.7474 8.33153C17.9123 8.08486 18.0002 7.79485 18.0002 7.49818C18.0002 7.10035 17.8422 6.71882 17.5609 6.43752C17.2796 6.15621 16.8981 5.99818 16.5002 5.99818Z" fill="currentColor" />
                        <path d="M4.05423 15.1982L2.24723 13.3912C2.15505 13.299 2.08547 13.1867 2.04395 13.0632C2.00243 12.9396 1.9901 12.8081 2.00793 12.679C2.02575 12.5498 2.07325 12.4266 2.14669 12.3189C2.22013 12.2112 2.31752 12.1219 2.43123 12.0582L9.15323 8.28918C7.17353 10.3717 5.4607 12.6926 4.05423 15.1982ZM8.80023 19.9442L10.6072 21.7512C10.6994 21.8434 10.8117 21.9129 10.9352 21.9545C11.0588 21.996 11.1903 22.0083 11.3195 21.9905C11.4486 21.9727 11.5718 21.9252 11.6795 21.8517C11.7872 21.7783 11.8765 21.6809 11.9402 21.5672L15.7092 14.8442C13.6269 16.8245 11.3061 18.5377 8.80023 19.9442ZM7.04023 18.1832L12.5832 12.6402C12.7381 12.4759 12.8228 12.2577 12.8195 12.032C12.8161 11.8063 12.725 11.5907 12.5653 11.4311C12.4057 11.2714 12.1901 11.1803 11.9644 11.1769C11.7387 11.1736 11.5205 11.2583 11.3562 11.4132L5.81323 16.9562L7.04023 18.1832Z" fill="currentColor" />
                    </svg>
                </span>
                <!--end::Svg Icon-->

                <!--begin::Wrapper-->
                <div class="d-flex flex-column">
                    <!--begin::Title-->
                    <h4 class="mb-1 text-info">Informasi</h4>
                    <!--end::Title-->

                    <!--begin::Content-->
                    <span>Kami akan memprioritaskan data yang anda kirim, Jika data yang anda masukkan tidak valid maka kami akan menggunakan SMTP Email milik kami untuk mengirimkan notifikasi email kepada pelanggan anda.</span>
                    <!--end::Content-->
                </div>
                <!--end::Wrapper-->
            </div>
            <!--end::Alert-->
        </div>

        <div class="col-md-6">
            <div class="card mb-5">
                <div class="card-header">
                    <div class="card-title m-0">
                        <h3 class="m-0 fw-bold">Pengaturan SMTP Email</h3>
                    </div>
                </div>

                <form id="frmSMTPEmail" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
                    <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">

                    <div class="card-body">
                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Host</label>
                            <input type="text" name="host" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Host" value="<?= $smtp != null ? $smtp->host : null ?>">
                        </div>

                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Username</label>
                            <input type="text" name="username" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Username" value="<?= $smtp != null ? $smtp->username : null ?>">
                        </div>

                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Password</label>
                            <input type="text" name="password" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Password" value="<?= $smtp != null ? $smtp->password : null ?>">
                        </div>

                        <div class="mb-7">
                            <label class="col-form-label fw-semibold fs-6 pt-0">Port</label>
                            <input type="number" name="port" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" placeholder="Masukkan Port" value="<?= $smtp != null ? $smtp->port : null ?>">
                        </div>
                    </div>

                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <button type="submit" class="btn btn-primary">Simpan</button>
                    </div>
                </form>
            </div>
        </div>

        <?php if (getCurrentUser()->access_emailmarketing == 1) : ?>
            <div class="col-md-6">
                <div class="card mb-5">
                    <div class="card-header">
                        <div class="card-title m-0">
                            <h3 class="m-0 fw-bold">SMTP Bonus Email Marketing</h3>
                        </div>
                    </div>

                    <div class="card-body">
                        <?php
                        $user_domain = getCurrentUser()->domain;
                        $default_host = $user_domain ? 'mail.' . $user_domain : 'mail.yourdomain.com';
                        $default_username = $user_domain ? 'support@' . $user_domain : '<EMAIL>';
                        ?>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-7">
                                    <label class="col-form-label fw-bold fs-6 pt-0 pb-1">Informasi</label>
                                    <p>Selamat! Anda mendapat SMTP bonus dari pembelian addon Email Marketing. Silahkan gunakan konfigurasi di bawah ini.</p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-bold fs-6 pt-0 pb-1">Host SMTP Bonus</label>
                                    <p><?= $default_host ?></p>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-bold fs-6 pt-0 pb-1">Username SMTP Bonus</label>
                                    <p><?= $default_username ?></p>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-bold fs-6 pt-0 pb-1">Password SMTP Bonus</label>
                                    <p>-</p>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-7">
                                    <label class="col-form-label fw-bold fs-6 pt-0 pb-1">Port SMTP Bonus</label>
                                    <p>465</p>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="mb-7">
                                    <label class="col-form-label fw-bold fs-6 pt-0 pb-1">Cara Menggunakan</label>
                                    <p>Salin konfigurasi SMTP bonus di atas ke form pengaturan SMTP. Pastikan domain Anda sudah dikonfigurasi dengan benar. SMTP bonus ini khusus untuk addon Email Marketing. Jika mengalami masalah, hubungi support untuk bantuan.</p>
                                </div>
                            </div>
                        </div>

                        <!--begin::Action Button-->
                        <div class="d-flex justify-content-center mt-7">
                            <button type="button" class="btn btn-success" onclick="copyBonusConfig()">
                                <i class="bi bi-arrow-left fs-4 me-2"></i>
                                Gunakan SMTP Bonus
                            </button>
                        </div>
                        <!--end::Action Button-->
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
    window.onload = function() {
        $.AjaxRequest('#frmSMTPEmail', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return Swal.fire({
                        title: 'Berhasil',
                        text: response.MESSAGE,
                        icon: 'success'
                    }).then(function(result) {
                        return window.location.reload();

                    });
                } else {
                    return Swal.fire({
                        title: 'Gagal',
                        text: response.MESSAGE,
                        icon: 'error'
                    });
                }
            },
            error: function() {
                return Swal.fire({
                    title: 'Gagal',
                    text: 'Server sedang sibuk silahkan coba lagi nanti',
                    icon: 'error'
                });
            }
        });
    };

    <?php if (getCurrentUser()->access_emailmarketing == 1) : ?>

        function copyBonusConfig() {
            // Get bonus SMTP values
            var bonusHost = '<?= $default_host ?>';
            var bonusUsername = '<?= $default_username ?>';
            var bonusPassword = '-';
            var bonusPort = '465';

            // Set values to form inputs
            $('input[name="host"]').val(bonusHost);
            $('input[name="username"]').val(bonusUsername);
            $('input[name="password"]').val(bonusPassword);
            $('input[name="port"]').val(bonusPort);

            // Show success message
            Swal.fire({
                title: 'Berhasil',
                text: 'Konfigurasi SMTP bonus telah disalin ke form',
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            });
        }
    <?php endif; ?>
</script>