<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="modal-dialog modal-lg">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">Struk Transaksi</h3>

            <!--begin::Close-->
            <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                <span class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::Close-->
        </div>

        <form id="frmStruk" action="<?= base_url(uri_string() . '/process') ?>" method="GET" autocomplete="off">
            <div class="modal-body" style="max-height: 500px; overflow: auto;">
                <div class="table-responsive">
                    <table class="table table-striped table-bordered table-hover datatables-product">
                        <?php if ($transaction->strukcontent == null) : ?>
                            <tr>
                                <td>ID</td>
                                <td><?= $transaction->clientcode ?></td>
                            </tr>

                            <tr>
                                <td>Produk</td>
                                <td><?= $transaction->productname ?? '- Produk telah dihapus -' ?></td>
                            </tr>

                            <tr>
                                <td>Harga</td>
                                <td>Rp <?= IDR($transaction->price) ?></td>
                            </tr>

                            <tr>
                                <td>Tanggal Proses</td>
                                <td><?= DateFormat($transaction->updateddate, 'd F Y H:i:s') ?></td>
                            </tr>

                            <tr>
                                <td>Tujuan</td>
                                <td><?= $transaction->target ?></td>
                            </tr>

                            <tr>
                                <td>Status</td>
                                <td>
                                    <?php
                                    $status = '';
                                    if (strtolower($transaction->status) == 'pending') {
                                        $status = "<span class=\"badge badge-warning\">Pending</span>";
                                    } else if (strtolower($transaction->status) == 'processing') {
                                        $status = "<span class=\"badge badge badge-info\">Processing</span>";
                                    } else if (strtolower($transaction->status) == 'success' || strtolower($transaction->status) == 'sukses' || strtolower($transaction->status) == 'completed') {
                                        $status = "<span class=\"badge badge-success\">Success</span>";
                                    } else {
                                        $status = "<span class=\"badge badge-danger\">" . ucfirst(strtolower($transaction->status)) . "</span>";
                                    }
                                    ?>
                                    <?= $status ?>
                                </td>
                            </tr>
                        <?php else : ?>
                            <?php
                            $decode = json_decode($transaction->strukcontent);
                            $numformatcol = array('startcount', 'remain', 'qty');
                            ?>

                            <?php foreach ($decode as $key => $value) : ?>
                                <?php
                                if (!isset(strukContent()[$value]))
                                    continue;

                                if ($transaction->category_apikey == 'PPOB') {
                                    $exception_col = array('startcount', 'remain', 'qty');

                                    if (in_array($value, $exception_col))
                                        continue;
                                } else if ($transaction->category_apikey == 'SMM') {
                                    $exception_col = array('sn');

                                    if (in_array($value, $exception_col))
                                        continue;
                                }
                                ?>

                                <tr>
                                    <td><?= strukContent()[$value] ?></td>
                                    <?php if ($value != 'aftercutsaldo' && !in_array($value, $numformatcol)) : ?>
                                        <td>
                                            <?php if ($value == 'currentsaldo') : ?>
                                                Rp <?= IDR($transaction->$value) ?>
                                            <?php elseif ($value == 'price') : ?>
                                                <input type="number" placeholder="Harga" name="price" class="form-control form-control-lg form-control-solid mb-3 mb-lg-0" value="<?= $transaction->$value ?>">
                                            <?php elseif ($value == 'status') : ?>
                                                <?php
                                                $status = '';
                                                if (strtolower($transaction->$value) == 'pending') {
                                                    $status = "<span class=\"badge badge-warning\">Pending</span>";
                                                } else if (strtolower($transaction->$value) == 'processing') {
                                                    $status = "<span class=\"badge badge-info\">Processing</span>";
                                                } else if (strtolower($transaction->$value) == 'success' || strtolower($transaction->$value) == 'sukses' || strtolower($transaction->$value) == 'completed') {
                                                    $status = "<span class=\"badge badge-success\">Success</span>";
                                                } else {
                                                    $status = "<span class=\"badge badge-danger\">" . ucfirst(strtolower($transaction->$value)) . "</span>";
                                                }
                                                ?>
                                                <?= $status ?>
                                            <?php else : ?>
                                                <?= $transaction->$value ?>
                                            <?php endif; ?>
                                        </td>

                                    <?php elseif (in_array($value, $numformatcol)) : ?>
                                        <td><?= IDR($transaction->$value) ?></td>
                                    <?php else : ?>
                                        <td>Rp <?= IDR($transaction->currentsaldo - $transaction->price) ?></td>
                                    <?php endif; ?>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </table>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Tutup</button>
                <button type="submit" class="btn btn-primary waves-effect waves-light" onclick="this.form.target='_blank';return true;">Print</button>
            </div>
        </form>
    </div>
</div>