<!-- SVG Icons -->
<svg xmlns="http://www.w3.org/2000/svg" style="display: none">
    <symbol id="check-circle-fill" fill="currentColor" viewBox="0 0 16 16">
        <path d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zm-3.97-3.03a.75.75 0 0 0-1.08.022L7.477 9.417 5.384 7.323a.75.75 0 0 0-1.06 1.06L6.97 11.03a.75.75 0 0 0 1.079-.02l3.992-4.99a.75.75 0 0 0-.01-1.05z"></path>
    </symbol>
    <symbol id="exclamation-triangle-fill" fill="currentColor" viewBox="0 0 16 16">
        <path d="M8.982 1.566a1.13 1.13 0 0 0-1.96 0L.165 13.233c-.457.778.091 1.767.98 1.767h13.713c.889 0 1.438-.99.98-1.767L8.982 1.566zM8 5c.535 0 .954.462.9.995l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 5.995A.905.905 0 0 1 8 5zm.002 6a1 1 0 1 1 0 2 1 1 0 0 1 0-2z"></path>
    </symbol>
    <symbol id="info-fill" fill="currentColor" viewBox="0 0 16 16">
        <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2z"></path>
    </symbol>
</svg>

<?php if ($this->session->flashdata('error')) : ?>
    <div class="alert alert-danger d-flex align-items-center mb-4" role="alert">
        <svg class="bi flex-shrink-0 me-2" width="24" height="24">
            <use xlink:href="#exclamation-triangle-fill"></use>
        </svg>
        <div><?= $this->session->flashdata('error') ?></div>
    </div>
<?php endif; ?>

<?php if ($already_unsubscribed) : ?>
    <!-- Already Unsubscribed -->
    <div class="text-center">
        <div class="mb-4">
            <div class="d-inline-flex align-items-center justify-content-center rounded-circle bg-success bg-opacity-10 mb-3" style="width: 80px; height: 80px;">
                <i class="ti ti-check fs-1 text-success"></i>
            </div>
            <h4 class="fw-bold text-dark mb-3">Sudah Berhenti Berlangganan</h4>
            <div class="alert alert-success d-flex align-items-center" role="alert">
                <svg class="bi flex-shrink-0 me-2" width="24" height="24">
                    <use xlink:href="#check-circle-fill"></use>
                </svg>
                <div>
                    Email <strong><?= $user->email ?></strong> sudah berhenti berlangganan dari email marketing <strong><?= $merchant->name ?></strong>.
                </div>
            </div>
        </div>

        <div class="d-grid">
            <a href="<?= base_url('?userid=' . $this->userid) ?>" class="btn btn-primary">
                <i class="ti ti-home me-2"></i>
                Kembali ke Beranda
            </a>
        </div>
    </div>
<?php else : ?>
    <!-- Unsubscribe Form -->
    <form class="form w-100" method="POST" action="<?= base_url('email-unsubscribe/process?userid=' . $this->userid) ?>" id="unsubscribeForm">
        <input type="hidden" name="<?= $this->security->get_csrf_token_name() ?>" value="<?= $this->security->get_csrf_hash() ?>">
        <input type="hidden" name="token" value="<?= $token ?>">

        <!-- Merchant Info Card -->
        <div class="card bg-light border-0 mb-4">
            <div class="card-body p-4">
                <div class="d-flex align-items-center">
                    <div class="flex-shrink-0 me-3">
                        <?php if ($merchant->companyicon) : ?>
                            <img src="<?= $merchant->companyicon ?>" alt="<?= $merchant->name ?>" class="rounded" style="width: 50px; height: 50px; object-fit: contain;" />
                        <?php else : ?>
                            <div class="d-flex align-items-center justify-content-center rounded bg-primary text-white fw-bold" style="width: 50px; height: 50px; font-size: 1.5rem;">
                                <?= strtoupper(substr($merchant->name, 0, 1)) ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="flex-grow-1">
                        <h5 class="mb-1 fw-bold"><?= $merchant->name ?></h5>
                        <p class="text-muted mb-0 small">
                            <i class="ti ti-mail me-1"></i>
                            <?= $user->email ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Info Message -->
        <div class="alert alert-info d-flex align-items-center mb-4" role="alert">
            <svg class="bi flex-shrink-0 me-2" width="24" height="24">
                <use xlink:href="#info-fill"></use>
            </svg>
            <div>
                <strong>Informasi:</strong> Anda akan berhenti berlangganan email marketing dari <strong><?= $merchant->name ?></strong>
            </div>
        </div>

        <!-- Warning -->
        <div class="alert alert-warning d-flex align-items-center mb-4" role="alert">
            <svg class="bi flex-shrink-0 me-2" width="24" height="24">
                <use xlink:href="#exclamation-triangle-fill"></use>
            </svg>
            <div>
                <strong>Perhatian:</strong> Setelah berhenti berlangganan, Anda tidak akan menerima email marketing dari <strong><?= $merchant->name ?></strong> lagi. Anda masih dapat berlangganan kembali kapan saja.
            </div>
        </div>

        <!-- Reason Input -->
        <div class="form-group mb-4">
            <label class="form-label fw-semibold">Alasan berhenti berlangganan (opsional)</label>
            <textarea name="reason" class="form-control" rows="3" placeholder="Berikan alasan mengapa Anda ingin berhenti berlangganan..."></textarea>
            <div class="form-text">
                Masukan Anda akan membantu kami meningkatkan layanan email marketing.
            </div>
        </div>

        <!-- Actions -->
        <div class="d-flex gap-2">
            <a href="<?= base_url('?userid=' . $this->userid) ?>" class="btn btn-light flex-fill">
                <i class="ti ti-arrow-left me-2"></i>
                Batal
            </a>

            <button type="submit" class="btn btn-danger flex-fill">
                <i class="ti ti-unlink me-2"></i>
                Berhenti Berlangganan
            </button>
        </div>
    </form>

    <script>
        document.getElementById('unsubscribeForm').addEventListener('submit', function(e) {
            e.preventDefault();

            if (confirm('Apakah Anda yakin ingin berhenti berlangganan dari email marketing ini?')) {
                this.submit();
            }
        });
    </script>
<?php endif; ?>